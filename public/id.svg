<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="41" height="41" fill="#1E1E1E"/>
<g id="Section 4">
<path d="M-12951 -4181C-12951 -4182.11 -12950.1 -4183 -12949 -4183H22518C22519.1 -4183 22520 -4182.1 22520 -4181V18791C22520 18792.1 22519.1 18793 22518 18793H-12949C-12950.1 18793 -12951 18792.1 -12951 18791V-4181Z" fill="#444444"/>
<path d="M-12949 -4183V-4182H22518V-4183V-4184H-12949V-4183ZM22520 -4181H22519V18791H22520H22521V-4181H22520ZM22518 18793V18792H-12949V18793V18794H22518V18793ZM-12951 18791H-12950V-4181H-12951H-12952V18791H-12951ZM-12949 18793V18792C-12949.6 18792 -12950 18791.6 -12950 18791H-12951H-12952C-12952 18792.7 -12950.7 18794 -12949 18794V18793ZM22520 18791H22519C22519 18791.6 22518.6 18792 22518 18792V18793V18794C22519.7 18794 22521 18792.7 22521 18791H22520ZM22518 -4183V-4182C22518.6 -4182 22519 -4181.55 22519 -4181H22520H22521C22521 -4182.66 22519.7 -4184 22518 -4184V-4183ZM-12949 -4183V-4184C-12950.7 -4184 -12952 -4182.66 -12952 -4181H-12951H-12950C-12950 -4181.55 -12949.6 -4182 -12949 -4182V-4183Z" fill="white" fill-opacity="0.1"/>
<g id="Sign up" clip-path="url(#clip0_17_12926)">
<rect width="390" height="844" transform="translate(-50.6602 -234.691)" fill="url(#paint0_linear_17_12926)"/>
<rect width="390" height="844" transform="translate(-50.6602 -234.691)" fill="black"/>
<rect width="390" height="844" transform="translate(-50.6602 -234.691)" fill="url(#paint1_linear_17_12926)"/>
<g id="Frame 2085665619">
<g id="Modal" filter="url(#filter0_dd_17_12926)">
<rect x="-27.1602" y="-127.582" width="343" height="644" rx="12" fill="white"/>
<g id="Content">
<g id="Checkbox group">
<g id="Checkbox group item">
<rect x="-10.6602" y="-15.082" width="310" height="91" rx="7.5" fill="white"/>
<rect x="-10.6602" y="-15.082" width="310" height="91" rx="7.5" stroke="#EAECF0"/>
<g id="Content_2">
<g id="Featured icon" filter="url(#filter1_d_17_12926)">
<rect x="4.83984" y="0.417969" width="32" height="32" rx="16" fill="white" shape-rendering="crispEdges"/>
<path id="Vector" d="M20.8391 16.0634C21.7908 16.0634 22.7035 15.6853 23.3764 15.0124C24.0494 14.3394 24.4274 13.4267 24.4274 12.475C24.4274 11.5234 24.0494 10.6107 23.3764 9.93772C22.7035 9.26477 21.7908 8.88672 20.8391 8.88672C19.8874 8.88672 18.9747 9.26477 18.3018 9.93772C17.6288 10.6107 17.2508 11.5234 17.2508 12.475C17.2508 13.4267 17.6288 14.3394 18.3018 15.0124C18.9747 15.6853 19.8874 16.0634 20.8391 16.0634ZM20.8391 17.6399C16.0682 17.6399 13.0098 20.2727 13.0098 21.5546V23.9482H28.6684V21.5546C28.6684 20.0044 25.773 17.6399 20.8391 17.6399Z" fill="#1A1C1E"/>
</g>
</g>
</g>
</g>
</g>
</g>
</g>
<g id="Group 37">
<g id="Ellipse 60" filter="url(#filter2_f_17_12926)">
<circle cx="143.66" cy="-234.691" r="179.383" fill="url(#paint2_linear_17_12926)" fill-opacity="0.26"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_dd_17_12926" x="-47.1602" y="-127.582" width="383" height="684" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_17_12926"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_17_12926"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect2_dropShadow_17_12926"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="12"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_17_12926" result="effect2_dropShadow_17_12926"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_17_12926" result="shape"/>
</filter>
<filter id="filter1_d_17_12926" x="0.839844" y="0.417969" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_17_12926"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_17_12926" result="shape"/>
</filter>
<filter id="filter2_f_17_12926" x="-115.723" y="-494.074" width="518.766" height="518.766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40" result="effect1_foregroundBlur_17_12926"/>
</filter>
<linearGradient id="paint0_linear_17_12926" x1="195" y1="0" x2="195" y2="844" gradientUnits="userSpaceOnUse">
<stop stop-color="#E5E5E5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_17_12926" x1="195" y1="0" x2="195" y2="844" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#2D2D2D"/>
</linearGradient>
<linearGradient id="paint2_linear_17_12926" x1="143.66" y1="-414.074" x2="143.66" y2="-55.3086" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#999999" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_17_12926">
<rect width="390" height="844" fill="white" transform="translate(-50.6602 -234.691)"/>
</clipPath>
</defs>
</svg>
