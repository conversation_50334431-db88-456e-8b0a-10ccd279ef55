<svg width="144" height="144" viewBox="0 0 144 144" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_di_254_7216)">
<g clip-path="url(#clip0_254_7216)">
<rect x="14.8984" y="22.75" width="85.0547" height="85.0547" rx="21.2637" fill="url(#paint0_linear_254_7216)" fill-opacity="0.04" shape-rendering="crispEdges"/>
<g clip-path="url(#clip1_254_7216)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M57.4258 42.9258C58.5619 42.9258 59.4829 43.8468 59.4829 44.9829V88.8686C59.4829 90.0047 58.5619 90.9258 57.4258 90.9258C56.2897 90.9258 55.3686 90.0047 55.3686 88.8686V44.9829C55.3686 43.8468 56.2897 42.9258 57.4258 42.9258ZM46.4544 51.1544C47.5905 51.1544 48.5115 52.0754 48.5115 53.2115V80.6401C48.5115 81.7762 47.5905 82.6972 46.4544 82.6972C45.3182 82.6972 44.3972 81.7762 44.3972 80.6401V53.2115C44.3972 52.0754 45.3182 51.1544 46.4544 51.1544ZM68.3972 51.1544C69.5333 51.1544 70.4544 52.0754 70.4544 53.2115V80.6401C70.4544 81.7762 69.5333 82.6972 68.3972 82.6972C67.2611 82.6972 66.3401 81.7762 66.3401 80.6401V53.2115C66.3401 52.0754 67.2611 51.1544 68.3972 51.1544ZM35.4829 62.1258C36.619 62.1258 37.5401 63.0468 37.5401 64.1829V69.6686C37.5401 70.8047 36.619 71.7258 35.4829 71.7258C34.3468 71.7258 33.4258 70.8047 33.4258 69.6686V64.1829C33.4258 63.0468 34.3468 62.1258 35.4829 62.1258ZM79.3686 62.1258C80.5047 62.1258 81.4258 63.0468 81.4258 64.1829V69.6686C81.4258 70.8047 80.5047 71.7258 79.3686 71.7258C78.2325 71.7258 77.3115 70.8047 77.3115 69.6686V64.1829C77.3115 63.0468 78.2325 62.1258 79.3686 62.1258Z" fill="white"/>
</g>
<g style="mix-blend-mode:plus-lighter" opacity="0.5" clip-path="url(#clip2_254_7216)" filter="url(#filter1_f_254_7216)">
<circle cx="57.4258" cy="65.2773" r="30" fill="#D9D9D9"/>
</g>
</g>
<rect x="14.8984" y="22.75" width="85.0547" height="85.0547" rx="21.2637" stroke="url(#paint1_linear_254_7216)" stroke-opacity="0.5" stroke-width="1.41758" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_di_254_7216" x="-21.248" y="-13.3984" width="164.436" height="157.352" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="14.1758" dy="7.08789"/>
<feGaussianBlur stdDeviation="14.1758"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_254_7216"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_254_7216" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="7.08789"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.826389 0 0 0 0 0.942083 0 0 0 0 0.991667 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_254_7216"/>
</filter>
<filter id="filter1_f_254_7216" x="7.81055" y="15.6621" width="99.2305" height="99.2305" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.6318" result="effect1_foregroundBlur_254_7216"/>
</filter>
<linearGradient id="paint0_linear_254_7216" x1="14.8984" y1="22.75" x2="99.9531" y2="107.805" gradientUnits="userSpaceOnUse">
<stop stop-color="#F8FBFF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_254_7216" x1="16.6704" y1="20.978" x2="97.2952" y2="107.805" gradientUnits="userSpaceOnUse">
<stop stop-color="#D8D8D8" stop-opacity="0.05"/>
<stop offset="1" stop-color="white" stop-opacity="0.4"/>
</linearGradient>
<clipPath id="clip0_254_7216">
<rect x="14.8984" y="22.75" width="85.0547" height="85.0547" rx="21.2637" fill="white"/>
</clipPath>
<clipPath id="clip1_254_7216">
<rect width="56.7031" height="56.7031" fill="white" transform="translate(29.0742 36.9258)"/>
</clipPath>
<clipPath id="clip2_254_7216">
<rect width="56.7031" height="56.7031" fill="white" transform="translate(29.0742 36.9258)"/>
</clipPath>
</defs>
</svg>
