<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_97_10784)">
<rect x="4.51172" y="0.171875" width="32" height="32" rx="16" fill="white" shape-rendering="crispEdges"/>
<g clip-path="url(#clip0_97_10784)">
<path d="M20.1857 9.69141L22.2457 13.8647L26.8524 14.5381L23.519 17.7847L24.3057 22.3714L20.1857 20.2047L16.0657 22.3714L16.8524 17.7847L13.519 14.5381L18.1257 13.8647L20.1857 9.69141Z" fill="#3F3F3F" stroke="#3F3F3F" stroke-width="1.40832" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_97_10784" x="0.511719" y="0.171875" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_97_10784"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_97_10784" result="shape"/>
</filter>
<clipPath id="clip0_97_10784">
<rect width="16" height="16" fill="white" transform="translate(12.1855 8.35742)"/>
</clipPath>
</defs>
</svg>
