<svg width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ddii_97_10673)">
<g filter="url(#filter1_d_97_10673)">
<rect x="9.5" y="5.34375" width="32" height="32" rx="16" fill="white" shape-rendering="crispEdges"/>
<path d="M18.507 28.1966C18.0487 28.1966 17.6528 28.0369 17.3195 27.7174C17.0001 27.3841 16.8403 26.9883 16.8403 26.5299V16.5299C16.8403 16.0716 17.0001 15.6827 17.3195 15.3633C17.6528 15.0299 18.0487 14.8633 18.507 14.8633H31.8403C32.2987 14.8633 32.6876 15.0299 33.007 15.3633C33.3403 15.6827 33.507 16.0716 33.507 16.5299V26.5299C33.507 26.9883 33.3403 27.3841 33.007 27.7174C32.6876 28.0369 32.2987 28.1966 31.8403 28.1966H18.507ZM25.1737 22.3633L31.8403 18.1966V16.5299L25.1737 20.6966L18.507 16.5299V18.1966L25.1737 22.3633Z" fill="#3F3F3F"/>
</g>
</g>
<defs>
<filter id="filter0_ddii_97_10673" x="0.340503" y="0.639553" width="76.7023" height="77.225" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="13.1917" dy="18.1696"/>
<feGaussianBlur stdDeviation="11.1756"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_97_10673"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4.72909" dy="5.47579"/>
<feGaussianBlur stdDeviation="5.08999"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_97_10673" result="effect2_dropShadow_97_10673"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_97_10673" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.497799" dy="-0.995597"/>
<feGaussianBlur stdDeviation="0.497799"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_97_10673"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.995597" dy="0.995597"/>
<feGaussianBlur stdDeviation="0.833813"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_97_10673" result="effect4_innerShadow_97_10673"/>
</filter>
<filter id="filter1_d_97_10673" x="5.5" y="5.34375" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_97_10673"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_97_10673" result="shape"/>
</filter>
</defs>
</svg>
