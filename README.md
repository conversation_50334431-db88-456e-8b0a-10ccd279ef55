# Celebrity Greeting SaaS Platform

A Next.js-based platform that connects customers with celebrities for personalized video greetings.

## Features

- User authentication (Customer + Celebrity roles)
- Celebrity search and filtering
- Greeting request management
- Admin dashboard for platform management
- Responsive design with Tailwind CSS

## Tech Stack

- Next.js 14 (App Router)
- JavaScript
- Tailwind CSS
- Headless UI
- Yarn package manager

## Project Structure

```
/src
  /app
    /admin
      /dashboard
      /users
      /moderation
      /analytics
    /auth
      /login
      /register
    /celebrity
      /dashboard
      /profile
      /greetings
      /analytics
    /customer
      /home
      /subscriptions
      /requests
      /history
  /components
  /layouts
  /utils
  /public
```

## Getting Started

1. Clone the repository:
```bash
git clone <repository-url>
cd dropp-celeb
```

2. Install dependencies:
```bash
yarn install
```

3. Run the development server:
```bash
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Available Scripts

- `yarn dev` - Start development server
- `yarn build` - Build for production
- `yarn start` - Start production server
- `yarn lint` - Run ESLint
- `yarn format` - Format code with Prettier

## Features to Implement

- [ ] User authentication with email/password
- [ ] Social login integration
- [ ] Email verification
- [ ] Two-factor authentication
- [ ] Payment processing
- [ ] Video upload and processing
- [ ] Real-time notifications
- [ ] Admin moderation tools
- [ ] Analytics and reporting
- [ ] Mobile app integration

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
