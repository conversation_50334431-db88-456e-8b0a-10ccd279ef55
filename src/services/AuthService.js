import axios from 'axios';
class AuthService {
  static async login({ email, password }) {
    const resp = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/auth/login`,
      { email, password }
    );
    return resp;
  }

  static async forgotPassword({ email }) {
    const resp = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/auth/forgot-password`,
      { email }
    );
    return resp;
  }

  static async resetPassword({ password, token }) {
    const resp = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/auth/reset-password`,
      { password, token }
    );
    return resp;
  }

  static async fetchProfile() {
    try {
      const resp = await axios.get('http://localhost:3001/api/profile');
      return resp.data;
    } catch (error) {
      console.error('Failed to fetch profile:', error.message);
      throw error;
    }
  }
}

export default AuthService;
