import { instance } from "@/utils/instances";

// Utility function to get token
const getToken = () => {
  if (typeof window === 'undefined') return null;

  try {
    const reduxState = window.__NEXT_REDUX_WRAPPER_STORE__?.getState?.();
    const token = reduxState?.auth?.userInfo?.accessToken;
    if (token) return token;
  } catch (e) {}

  try {
    const auth = JSON.parse(localStorage.getItem('persist:root'));
    if (auth && auth.auth) {
      const authObj = JSON.parse(auth.auth);
      if (authObj?.userInfo?.accessToken) {
        return authObj.userInfo.accessToken;
      }
    }
  } catch (e) {}

  return localStorage.getItem('accessToken');
};

export const SampleGreetingService = {
  createSampleGreeting: async (celebrityId, data) => {
    if (data instanceof FormData && !data.get('script')) {
      data.append('script', 'Hello! This is a sample of my digital human. I can help deliver your personalized messages with my unique voice and appearance!');
    }

    if (data instanceof FormData && !data.get('tones')) {
      data.append('tones', 'happy');
    }

    return await instance.post(`/celebrities/${celebrityId}/sample-greetings`, data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  getSampleGreetings: async (celebrityId) => {
    const token = getToken();
    return await instance.get(
      `/celebrity-greetings/celebrity/${celebrityId}`,
      {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      }
    );
  },

  // getSampleGreeting: async (celebrityId, sampleGreetingId) => {
  //   return await instance.get(`/celebrities/${celebrityId}/sample-greetings/${sampleGreetingId}`);
  // },

  updateSampleGreeting: async (greetingId, data) => {
    const token = getToken();
    
    if (data instanceof FormData) {
      // Only add script if not provided and if we're not just updating media files
      if (!data.get('script') && !data.get('avatar') && !data.get('video')) {
        data.append('script', 'This is a static script for the greeting.');
      }
    }

    return await instance.patch(
      `/celebrity-greetings/${greetingId}`,
      data,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      }
    );
  },

  deleteSampleGreeting: async (celebrityId, sampleGreetingId) => {
    return await instance.delete(`/celebrities/${celebrityId}/sample-greetings/${sampleGreetingId}`);
  },

  getCategories: async () => {
    return await instance.get(`/categories`);
  },

  getTones: async () => {
    return await instance.get(`/tones`);
  }
};

export default SampleGreetingService;
