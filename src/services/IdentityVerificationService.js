import axios from "axios";
import { instance } from "@/utils/instances";

class IdentityVerificationService {
  static async submitKycDocument(data) {
    const formData = new FormData();

    if (data.document) {
      if (
        typeof data.document === "string" &&
        data.document.startsWith("data:")
      ) {
        const blob = this.dataURItoBlob(data.document);
        formData.append("document", blob, "document.png");
      } else {
        formData.append("document", data.document);
      }
    }
    if (data.documentType) {
    } else {
    }

    if (data.country) {
      formData.append("country", data.country);
    }

    const token = localStorage.getItem("accessToken");

    let url = `celebrities/${data.celebrityId}/kyc-documents`;

    const documentType = data.documentType || "id_card";
    url += `?documentType=${encodeURIComponent(documentType)}`;

    if (data.country) {
      url += `&country=${encodeURIComponent(data.country)}`;
    }

    try {
      const headers = {
        "Content-Type": "multipart/form-data",
      };

      if (data.token) {
        headers["Authorization"] = `Bearer ${data.token}`;
      } else if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      const response = await instance({
        method: "POST",
        url,
        data: formData,
        headers,
      });
      return response;
    } catch (error) {
      console.error("Error submitting KYC document:", error);

      if (error.response) {
        let errorMessage = "Server error";

        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data && error.response.data.error) {
          errorMessage = error.response.data.error;
        }

        error.userMessage = `${errorMessage} (${error.response.status})`;
      } else if (error.request) {
        error.userMessage =
          "No response from server. Please check your connection.";
      } else {
        error.userMessage = error.message || "Unknown error occurred";
      }

      throw error;
    }
  }

  static dataURItoBlob(dataURI) {
    let byteString;
    if (dataURI.split(",")[0].indexOf("base64") >= 0) {
      byteString = atob(dataURI.split(",")[1]);
    } else {
      byteString = decodeURIComponent(dataURI.split(",")[1]);
    }

    const mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];

    const ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ia], { type: mimeString });
  }
}

export default IdentityVerificationService;
