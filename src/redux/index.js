import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit';
import storage from 'redux-persist/lib/storage';
import { persistReducer, persistStore } from 'redux-persist';
import rootReducer from './rootReducer';

const persistConfig = {
  key: 'root',
  storage,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore paths or actions you trust
        ignoredActions: ['persist/PERSIST'],
        ignoredActionPaths: ['register'], // <-- this avoids the error
        ignoredPaths: ['register'],
      },
    }),
});

const persistor = persistStore(store);

export { store, persistor };
