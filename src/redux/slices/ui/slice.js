import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isShareModalOpen: false,
  isGreetingPreviewOpen: false,
};

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    setShareModalOpen: (state, action) => {
      state.isShareModalOpen = action.payload;
    },
    setGreetingPreviewOpen: (state, action) => {
      state.isGreetingPreviewOpen = action.payload;
    },
  },
});

export const {
  setShareModalOpen,
  setGreetingPreviewOpen,
} = uiSlice.actions;

export default uiSlice.reducer;
