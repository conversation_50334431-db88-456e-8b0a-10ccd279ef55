import { createSlice } from "@reduxjs/toolkit";
import { submitKycDocument } from "./thunks";

const initialState = {
  isLoading: false,
  error: null,
  success: false,
  documentData: null,
};

const identityVerificationSlice = createSlice({
  name: "identityVerification",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    resetState: (state) => {
      state.isLoading = false;
      state.error = null;
      state.success = false;
      state.documentData = null;
    },
    setError: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(submitKycDocument.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(submitKycDocument.fulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.error = null;
        state.success = true;
        state.documentData = payload?.data;
      })
      .addCase(submitKycDocument.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
        state.success = false;
      }),
});

export const { setLoading, resetState, setError } = identityVerificationSlice.actions;

export default identityVerificationSlice.reducer;
