import { createAsyncThunk } from '@reduxjs/toolkit';
import IdentityVerificationService from '@/services/IdentityVerificationService';
import { setLoading } from './slice';

export const submitKycDocument = createAsyncThunk(
  'identityVerification/submitKycDocument',
  async (data, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      const response = await IdentityVerificationService.submitKycDocument(data);
      return { status: response.status, data: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);
