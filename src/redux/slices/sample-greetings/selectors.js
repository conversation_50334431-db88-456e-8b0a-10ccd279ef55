// Selectors for the sample greeting slice
export const selectSampleGreetings = (state) => state.sampleGreetings;
export const selectIsSampleGreetingsLoading = (state) => state.sampleGreetings.isLoading;
export const selectSampleGreetingsError = (state) => state.sampleGreetings.error;
export const selectAvatarFile = (state) => state.sampleGreetings.avatarFile;
export const selectVideoFile = (state) => state.sampleGreetings.video;
export const selectCategoryId = (state) => state.sampleGreetings.categoryId;
export const selectTones = (state) => state.sampleGreetings.tones;
export const selectSampleGreetingSuccess = (state) => state.sampleGreetings.success;
