import { createAsyncThunk } from "@reduxjs/toolkit";
import { instance } from "@/utils/instances";

export const createSampleGreeting = createAsyncThunk(
  "sampleGreetings/createSampleGreeting",
  async ({ celebrityId, categoryId, avatarFile, video, tones }, { rejectWithValue, getState }) => {
    try {
      const token = getState().auth?.userInfo?.accessToken || localStorage.getItem("accessToken");
      
      const fixedCategoryId = "6821b350e1e3e7a1e71bdbe6";
      
      const formData = new FormData();
      formData.append("categoryId", fixedCategoryId);
      
      if (avatarFile) {
        formData.append("avatar", avatarFile);
      }
      
      if (video) {
        const processedVideoFile = new File(
          [video], 
          "recorded-video.mp4", 
          { type: 'video/mp4' }
        );
        formData.append("video", processedVideoFile);
      }
      
      // Add static script
      formData.append("script", "Hello! This is a sample of my digital human. I can help deliver your personalized messages with my unique voice and appearance!");
      
      // Always send happy tone
      formData.append("tones", "happy");
      
      // Also include any additional tones if provided
      if (tones && tones.length > 0 && !tones.includes("happy")) {
        tones.forEach(tone => {
          formData.append("tones", tone);
        });
      }

      const url = `/celebrities/${celebrityId}/sample-greetings`;
      const response = await instance({
        method: "POST",
        url: url,
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": token ? `Bearer ${token}` : "",
        }
      });
      
      return response.data;
    } catch (error) {
      console.error("Sample Greeting API Error:", {
        status: error.response?.status,
        message: error.response?.data?.message,
        error: error.message
      });
      
      return rejectWithValue(
        error.response?.data || "Failed to create sample greeting"
      );
    }
  }
);
