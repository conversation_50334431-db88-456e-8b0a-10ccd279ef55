import { createSlice } from "@reduxjs/toolkit";
import { createSampleGreeting } from "./thunks";

const initialState = {
  isLoading: false,
  error: null,
  avatarFile: null,
  video: null,
  categoryId: null,
  tones: [],
  success: false,
  sampleGreetingData: null
};

const sampleGreetingsSlice = createSlice({
  name: "sampleGreetings",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setAvatarFile: (state, action) => {
      state.avatarFile = action.payload;
    },
    setVideoFile: (state, action) => {
      state.video = action.payload;
    },
    setCategoryId: (state, action) => {
      state.categoryId = action.payload;
    },
    setTones: (state, action) => {
      state.tones = action.payload;
    },
    resetSampleGreeting: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(createSampleGreeting.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSampleGreeting.fulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.success = true;
        state.sampleGreetingData = payload;
      })
      .addCase(createSampleGreeting.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      });
  },
});

export const {
  setLoading,
  setError,
  setAvatarFile,
  setVideoFile,
  setCategoryId,
  setTones,
  resetSampleGreeting
} = sampleGreetingsSlice.actions;

export default sampleGreetingsSlice.reducer;
