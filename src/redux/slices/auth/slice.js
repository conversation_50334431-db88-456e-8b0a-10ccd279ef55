import { createSlice } from "@reduxjs/toolkit";
import { loginUser } from "./thunks";

const initialState = {
  isLoading: false,
  error: null,
  isLogin: false,
  userInfo: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    logoutUser: (state) => {
      state.isLoading = false;
      state.error = null;
      state.isLogin = false;
      state.userInfo = null;
    },
    loginSuccess: (state, { payload }) => {
      state.isLoading = false;
      state.error = null;
      state.isLogin = true;
      state.userInfo = payload?.data;
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    updateUserToken: (state, { payload }) => {
      state.userInfo.accessToken = payload?.accessToken;
      state.userInfo.refreshToken = payload?.refreshToken;
    },
    updateUser: (state, { payload }) => {
      state.userInfo.user = payload?.user;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(loginUser.fulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.error = null;
        state.isLogin = true;
        state.userInfo = payload?.data;
      })
      .addCase(loginUser.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const {
  setLoading,
  logoutUser,
  loginSuccess,
  loginFailure,
  updateUserToken,
  updateUser,
} = authSlice.actions;

export default authSlice.reducer;
