import { createAsyncThunk } from '@reduxjs/toolkit';
import AuthService from '@/services/AuthService';
import { setLoading } from './slice';

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials, { dispatch }) => {
    try {
      dispatch(setLoading(true));
      const response = await AuthService.login(credentials);
      return { status: response.status, data: response.data };
    } catch (error) {
      return error.response;
    }
  }
);

export const forgotPassword = createAsyncThunk(
  'auth/forgotPassword',
  async (data, { dispatch }) => {
    try {
      dispatch(setLoading(true));
      const response = await AuthService.forgotPassword(data);
      return { status: response.status, data: response.data };
    } catch (err) {
      return err.response;
    }
  }
);

export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async (data, { dispatch }) => {
    try {
      dispatch(setLoading(true));
      const response = await AuthService.resetPassword(data);
      return { status: response.status, data: response.data };
    } catch (err) {
      return err.response;
    }
  }
);
