import { useEffect } from 'react';
import useSampleGreetings from './useSampleGreetings';

// This hook creates a test avatar file when the app is loaded
// It's a workaround to ensure we always have an avatar file available
const useTestAvatar = () => {
  const { uploadAvatar } = useSampleGreetings();

  useEffect(() => {
    // Create a simple canvas with a colored square
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // Draw a colored rectangle
    ctx.fillStyle = '#4287f5';
    ctx.fillRect(0, 0, 200, 200);
    
    // Draw a circle to make it look like an avatar
    ctx.beginPath();
    ctx.arc(100, 70, 50, 0, Math.PI * 2, true);
    ctx.fillStyle = '#ffffff';
    ctx.fill();
    
    // Draw body
    ctx.beginPath();
    ctx.moveTo(100, 120);
    ctx.lineTo(130, 170);
    ctx.lineTo(70, 170);
    ctx.closePath();
    ctx.fillStyle = '#ffffff';
    ctx.fill();
    
    // Convert to blob (important: use PNG format which is explicitly supported)
    canvas.toBlob((blob) => {
      // Create a file from the blob with PNG format
      const avatarFile = new File([blob], 'avatar.png', { type: 'image/png' });
      console.log('Test avatar file created:', avatarFile);
      console.log('Avatar file type:', avatarFile.type);
      console.log('Avatar file size:', avatarFile.size, 'bytes');
      
      // Upload to Redux store
      uploadAvatar(avatarFile);
    }, 'image/png', 0.95);
    
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

export default useTestAvatar;
