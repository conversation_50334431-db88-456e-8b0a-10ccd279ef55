import { toast, ToastOptions } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const useToaster = () => {
  const showToast = (message, options = {}) => {
    toast(message, {
      position: 'top-right',
      autoClose: 3000,
      hideProgressBar: false,
      closeOnClick: true,
      ...options,
    });
  };

  const showSuccessToast = (message, options = {}) => {
    showToast(message, { type: 'success', ...options });
  };

  const showErrorToast = (message, options = {}) => {
    showToast(message, { type: 'error', ...options });
  };

  return { showToast, showSuccessToast, showErrorToast };
};

export default useToaster;
