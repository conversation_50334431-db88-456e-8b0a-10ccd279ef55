import { useRouter } from 'next/router';
import { isAuthenticated } from '@/utils/isAuthenticated';
import { useSelector } from 'react-redux';
import { selectAuth } from '@/redux/slices/auth/selectors';

const withAuth = (WrappedComponent) => {
  const Auth = (props) => {
    const auth = useSelector(selectAuth);
    const token = auth?.otpData?.data?.accessToken;
    const router = useRouter();
    if (!isAuthenticated() || !token) {
      router.push('/auth/start');
    } else {
      return <WrappedComponent {...props} />;
    }
  };
  return Auth;
};

export default withAuth;
