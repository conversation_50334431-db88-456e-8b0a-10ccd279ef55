import { useDispatch, useSelector } from "react-redux";
import { useCallback } from "react";
import { 
  setAvatarFile, 
  setVideoFile, 
  setCategoryId, 
  setTones, 
  resetSampleGreeting 
} from "@/redux/slices/sample-greetings/slice";
import { createSampleGreeting } from "@/redux/slices/sample-greetings/thunks";
import { 
  selectSampleGreetings,
  selectIsSampleGreetingsLoading,
  selectSampleGreetingsError,
  selectAvatarFile,
  selectVideoFile,
  selectCategoryId,
  selectTones,
  selectSampleGreetingSuccess
} from "@/redux/slices/sample-greetings/selectors";
import useApiErrorHandler from "./useApiErrorHandler";
import useToaster from "./useToaster";
import { useSelector as reduxUseSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";

const useSampleGreetings = () => {
  const dispatch = useDispatch();
  const handleApiError = useApiErrorHandler();
  const { showSuccessToast } = useToaster();
  
  const auth = reduxUseSelector(selectAuth);
  const token = auth?.userInfo?.accessToken;
  const userId = auth?.userInfo?.user?._id;
  const celebrityId = auth?.userInfo?.user?.celebrityProfile?._id || 
                     auth?.userInfo?.user?._id;
  
  const sampleGreetingsState = useSelector(selectSampleGreetings);
  const isLoading = useSelector(selectIsSampleGreetingsLoading);
  const error = useSelector(selectSampleGreetingsError);
  const avatarFile = useSelector(selectAvatarFile);
  const video = useSelector(selectVideoFile);
  const categoryId = useSelector(selectCategoryId);
  const tones = useSelector(selectTones);
  const success = useSelector(selectSampleGreetingSuccess);

  const uploadAvatar = useCallback((file) => {
    dispatch(setAvatarFile(file));
  }, [dispatch]);

  const uploadVideo = useCallback((file) => {
    dispatch(setVideoFile(file));
  }, [dispatch]);

  const updateCategoryId = useCallback((id) => {
    dispatch(setCategoryId(id));
  }, [dispatch]);

  const updateTones = useCallback((selectedTones) => {
    dispatch(setTones(selectedTones));
  }, [dispatch]);

  const resetState = useCallback(() => {
    dispatch(resetSampleGreeting());
  }, [dispatch]);

  const submitSampleGreeting = useCallback(async () => {
    try {
      if (!avatarFile) {
        throw new Error("Profile image is required");
      }
      
      if (!video) {
        throw new Error("Sample greeting video is required");
      }
      
      if (!token) {
        throw new Error("Authentication token is required. Please log in again.");
      }
      
      let finalCelebrityId = celebrityId || localStorage.getItem("celebrityId");
      
      if (!finalCelebrityId) {
        throw new Error("Celebrity ID is required. Please log in again.");
      }
      
      
      let finalVideoFile = video;
      if (video.type !== 'video/mp4') {
        finalVideoFile = new File([video], "recorded-video.mp4", { type: 'video/mp4' });
        dispatch(setVideoFile(video));
      }
      
      dispatch(setTones(["happy"]));
      
      const fixedCategoryId = "6821b350e1e3e7a1e71bdbe6";
      dispatch(setCategoryId(fixedCategoryId));
      const result = await dispatch(
        createSampleGreeting({
          celebrityId: finalCelebrityId,
          categoryId: fixedCategoryId,
          avatarFile,
          video,
          tones: ["happy"] 
        })
      ).unwrap();
      
      showSuccessToast("Sample greeting created successfully!");
      return result;
    } catch (error) {
      handleApiError(error);
      throw error;
    }
  }, [dispatch, categoryId, avatarFile, video, tones, userId, handleApiError, showSuccessToast]);

  return {
    sampleGreetingsState,
    isLoading,
    error,
    avatarFile,
    video,
    categoryId,
    tones,
    success,
    uploadAvatar,
    uploadVideo,
    updateCategoryId,
    updateTones,
    submitSampleGreeting,
    resetState,
    token,
    userId
  };
};

export default useSampleGreetings;
