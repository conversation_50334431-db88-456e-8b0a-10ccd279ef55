import { useCallback } from "react";
import useToaster from "./useToaster";

const useApiErrorHandler = () => {
  const { showErrorToast } = useToaster();
  const handleApiError = useCallback((error) => {
    console.error(error);
    let msgStr = "";

    const errors = error?.response?.data?.message;

    if (Array.isArray(errors) && errors.length > 0) {
      msgStr = errors.join(" ");
    } else {
      msgStr = errors || error?.response?.data?.error || "An error occurred";
    }

    showErrorToast(msgStr);
  }, []);

  return handleApiError;
};

export default useApiErrorHandler;
