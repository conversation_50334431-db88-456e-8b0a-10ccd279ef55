import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
// import { toast } from 'react-toastify';
import { instance } from "@/utils/instances";
import { selectAuth } from "@/redux/slices/auth/selectors";
import { useRouter } from "next/navigation";
import { logoutUser } from "@/redux/slices/auth/slice";

const useAxiosInterceotor = () => {
  const auth = useSelector(selectAuth);
  const [isApiLoading, setIsApiLoading] = useState(false);

  useEffect(() => {
    // Get token from Redux or localStorage
    const token =
      auth?.userInfo?.accessToken || localStorage.getItem("accessToken");

    const requestIntercept = instance.interceptors.request.use(
      (config) => {
        setIsApiLoading(true);
        config.headers["retryCall"] = 1;

        // Add Authorization header if token is available and not already set
        if (token && !config.headers["Authorization"]) {
          config.headers["Authorization"] = `Bear<PERSON> ${token}`;
        }

        return config;
      },
      (error) => {
        setIsApiLoading(false);
        if (!(error instanceof Error)) {
          error = new Error(error); // Create a new Error if it's not one
        }

        // Reject the promise with the error
        return Promise.reject(error);
      }
    );

    const responseIntercept = instance.interceptors.response.use(
      function (response) {
        setIsApiLoading(false);
        return response;
      },
      async function (error) {
        setIsApiLoading(false);
        const prevRequest = error.config;
        if (prevRequest && +prevRequest.headers["retryCall"] === 1) {
          prevRequest.headers["retryCall"] = 2;
          // toast.error(error?.response?.data?.errors || error.message);
        }

        return Promise.reject(error);
      }
    );

    return () => {
      setIsApiLoading(false);
      instance.interceptors.request.eject(requestIntercept);
      instance.interceptors.response.eject(responseIntercept);
    };
  }, [auth]);

  return { instance, isApiLoading };
};

export default useAxiosInterceotor;
