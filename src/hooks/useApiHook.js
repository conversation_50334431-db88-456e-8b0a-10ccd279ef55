import useAxiosInterceptor from './useAxiosInterceptor';

const useApiHook = () => {
  const { instance, isApiLoading } = useAxiosInterceptor();

  const handleApiCall = async ({ method, url, data, headers }) => {
    const config = {
      method,
      url,
      data,
      headers,
    };

    if (data instanceof FormData) {
      config.headers = {
        ...headers,
        'Content-Type': 'multipart/form-data',
      };
    }

    return await instance(config);
  };

  return { handleApiCall, isApiLoading };
};

export default useApiHook;
