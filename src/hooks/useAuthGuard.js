"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const { selectAuth } = require("@/redux/slices/auth/selectors");
const { useSelector } = require("react-redux");

const useAuthGuard = () => {
  const router = useRouter();
  const auth = useSelector(selectAuth);
  const token = auth?.userInfo?.accessToken;
  const role = auth?.userInfo?.user?.role;
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    const currentPath =
      typeof window !== "undefined" ? window.location.pathname : "";

    if (!token) {
      router.push("/auth/start");
    } else if (token && role !== "admin" && currentPath.startsWith("/admin")) {
      router.push("/");
    } else {
      setIsLoading(false);
    }
  }, [token, role, router]);

  return isLoading;
};

export default useAuthGuard;
