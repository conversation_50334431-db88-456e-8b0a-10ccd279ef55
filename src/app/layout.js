// src/app/layout.js
import { Inter } from 'next/font/google';
import './globals.css';
import ReduxProvider from '@/utils/ReduxProvider';
import AppLayout from '@/components/common/AppLayout';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children }) {
  return (
    <html lang='en'>
      <head>
        <title>CelebGreetings - Personalized Celebrity Video Messages</title>
        <meta
          name='description'
          content='Get personalized video messages from your favorite celebrities for any occasion.'
        />
      </head>
      <body className={` text-white`}>
        <ReduxProvider>
          <AppLayout>{children}</AppLayout>
        </ReduxProvider>
      </body>
    </html>
  );
}
