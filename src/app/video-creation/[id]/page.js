"use client";

import { Formik, Form } from "formik";
import Image from "next/image";
import Step1 from "@/components/VideoCreation/Step1";
import Step2 from "@/components/VideoCreation/Step2";
import Step3 from "@/components/VideoCreation/Step3";
import Step4 from "@/components/VideoCreation/Step4";
import Step5 from "@/components/VideoCreation/Step5";
import { useEffect, useState } from "react";
import useApiHook from "@/hooks/useApiHook";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";
import useAuthGuard from "@/hooks/useAuthGuard";
import { useParams } from "next/navigation";
import PageLoader from "@/components/common/PageLoader";
import ShowPreview from "@/components/VideoCreation/ShowPreview";

const ProfileCreation = () => {
  const { id } = useParams();
  const { handleApiCall, isApiLoading } = useApiHook();
  const handleApiError = useApiErrorHandler();
  const [celebrity, setCelebrity] = useState({});

  const [currentStep, setCurrentStep] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const isLoading = useAuthGuard();
  const steps = [
    {
      component: Step1,
    },
    {
      title: "Choose Your Greeting Occasion",
      description: "Pick the type of moment this greeting is for",
      component: Step2,
    },
    {
      title: "Choose Your Tone of Voice",
      description: "Pick the tone that best matches your greeting",
      component: Step3,
    },
    {
      title: "Message",
      description: "What would you like the celebrity to say?",
      component: Step4,
    },
    {
      component: Step5,
    },
  ];

  const initialValues = {
    greetingFor: "",
    name: "",
    to: "",
    from: "",
    occasions: [],
    tones: [],
    message: "",
  };

  const fetchCelebrity = async () => {
    if (!id) return;
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities/${id}`,
      });

      if (resp.status === 200) {
        setCelebrity(resp.data);
      }
    } catch (error) {
      console.error("Celebrity fetch error:", error);
    }
  };

  const handleSubmit = async (values) => {
    setShowPreview(true);
    if (2 == "2") {
      return;
    }
    try {
      const formData = new FormData();
      formData.append("greetingFor", values.greetingFor);
      formData.append("to", values.to);
      formData.append("from", values.from);
      values.occasions.forEach((occasions) => {
        formData.append("occasions[]", occasions);
      });
      values.tones.forEach((tone) => {
        formData.append("tones[]", tone);
      });
      formData.append("message", values.message);

      const resp = await handleApiCall({
        method: "POST",
        url: "/users/profile",
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (resp.status === 200 || resp.status === 201) {
        // dispatch(updateUser({ user: resp?.data }));
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleNext = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const StepComponent = steps[currentStep].component;

  useEffect(() => {
    fetchCelebrity();
  }, [id]);

  return (
    <>
      {showPreview && <ShowPreview celebrity={celebrity} />}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, errors, touched }) => (
          <Form className="min-h-screen bg-gradient-custom text-white flex flex-col items-center px-6 py-10">
            {(isApiLoading || isLoading) && <PageLoader />}
            {/* Header */}
            <div className="w-full flex justify-between items-center mb-4 max-w-md">
              <button
                type="button"
                onClick={handlePrev}
                className="text-sm cursor-pointer"
                disabled={currentStep === 0}
              >
                <Image
                  src="/left-arrow.svg"
                  width={20}
                  height={20}
                  alt="Back"
                  className={
                    currentStep === 0
                      ? "opacity-50 cursor-not-allowed"
                      : "cursor-pointer"
                  }
                />
              </button>
              <h2 className="text-base font-medium w-full text-center">
                New Request
              </h2>
              <Image
                src="/x.svg"
                width={20}
                height={20}
                alt="cross"
                className="invert"
              />
            </div>

            {/* Profile Content */}
            <div className="bg-white text-black rounded-2xl shadow-xl w-full max-w-md p-6">
              <div className="mb-6">
                {" "}
                <h3 className="text-center text-xl font-medium mb-0 leading-[160%]">
                  {steps[currentStep].title}
                </h3>
                <p className="text-center text-xs text-[#6C7278] tracking-[-2%] leading-[160%]">
                  {steps[currentStep].description}
                </p>
              </div>
              <StepComponent
                values={values}
                errors={errors}
                touched={touched}
                celebrity={celebrity}
              />

              <div className="mt-6 flex gap-3">
                <button
                  type={currentStep === steps.length - 1 ? "submit" : "button"}
                  onClick={(e) => {
                    if (currentStep < steps.length - 1) {
                      e.preventDefault();
                      handleNext();
                    }
                  }}
                  className="flex w-full justify-center rounded-lg text-white p-3 grayButton cursor-pointer"
                >
                  {currentStep === steps.length - 1
                    ? "Preview greeting"
                    : "Continue"}
                </button>
              </div>
              {/* Disclaimer */}
              {currentStep === 0 && (
                <p className="mt-6 text-center text-xs text-gray-500">
                  Audio for endorsement or promotional use cannot be booked
                  through celebrityX
                </p>
              )}
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default ProfileCreation;
