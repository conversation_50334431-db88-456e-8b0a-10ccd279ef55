'use client';

import { useState, useEffect } from 'react';
import VideoRecorder from '@/components/VideoRecording/VideoRecorder';

export default function RecordVideoPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isUpdating, setIsUpdating] = useState(false);
  const [greetingId, setGreetingId] = useState(null);
  

  useEffect(() => {
    // Check if we're updating an existing greeting
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const id = params.get('greetingId');
      if (id) {
        setIsUpdating(true);
        setGreetingId(id);
      }
    }
  }, []);

  const handleStepChange = (step) => {
    setCurrentStep(step);
  };

  return (
    <div
      className="h-screen bg-black text-white flex flex-col overflow-hidden"
      style={{ fontFamily: '"AG Book Pro", sans-serif' }}
    >
      <main className="flex-1 flex flex-col relative">
        <VideoRecorder
          currentStep={currentStep}
          onStepChange={handleStepChange}
          isUpdating={isUpdating}
          greetingId={greetingId}
        />
      </main>
    </div>
  );
}
