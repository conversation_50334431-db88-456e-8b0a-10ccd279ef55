"use client";

import { Formik, Form } from "formik";
import Image from "next/image";
import Step1 from "@/components/Profile/Step1";
import Step2 from "@/components/Profile/Step2";
import Step3 from "@/components/Profile/Step3";
import Step4 from "@/components/Profile/Step4";
import Step5 from "@/components/Profile/Step5";
import Step6 from "@/components/Profile/Step6";
import Step7 from "@/components/Profile/Step7";
import { useEffect, useState } from "react";
import Success from "@/components/Profile/Success";
import { selectAuth } from "@/redux/slices/auth/selectors";
import { useDispatch, useSelector } from "react-redux";
import useApiHook from "@/hooks/useApiHook";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";
import { Loader, LoaderVariants } from "@/components/common/Loader";
import { updateUser } from "@/redux/slices/auth/slice";
import useAuthGuard from "@/hooks/useAuthGuard";

const ProfileCreation = () => {
  const dispatch = useDispatch();
  const { handleApiCall, isApiLoading } = useApiHook();
  const handleApiError = useApiErrorHandler();
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const [categories, setCategories] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [showSuccess, setShowSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const isLoading = useAuthGuard();

  const steps =
    user?.role === "celebrity"
      ? [
          {
            title: "Basic Information",
            description: "Help fans know what you do best",
            component: Step1,
          },
          {
            title: "Add Tags",
            description:
              "Help fans discover you by adding keywords that describe your style, identity, or vibe.",
            component: Step2,
          },
          {
            title: "Introduce Yourself",
            description: "Give fans a glimpse of who you are",
            component: Step3,
          },
          {
            title: "Choose Your Greeting Occasions",
            description:
              "Pick the types of moments you’d love to help fans celebrate",
            component: Step4,
          },
          {
            title: "Choose Your Tone of Voice",
            description: "Pick the tone that best matches your style",
            component: Step5,
          },
          {
            title: "Social Media",
            description: "Add links to your online handles",
            component: Step6,
          },
          {
            title: "Set Pricing",
            description:
              "Choose how much you’d like to charge for different types of greetings.",
            component: Step7,
          },
        ]
      : [
          {
            title: "Basic Information",
            description: "Help fans know what you do best",
            component: Step1,
          },
          {
            title: "Add Tags",
            description:
              "Help fans discover you by adding keywords that describe your style, identity, or vibe.",
            component: Step2,
          },
          {
            title: "Introduce Yourself",
            description: "Give fans a glimpse of who you are",
            component: Step3,
          },
          {
            title: "Choose Your Greeting Occasions",
            description:
              "Pick the types of moments you’d love to help fans celebrate",
            component: Step4,
          },
        ];
  const progress = ((currentStep + 1) / steps.length) * 100;

  const initialValues = {
    profileImage: user?.profilePhotoUrl || null,
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    name: user?.celebrityProfile?.name || "",
    roles: user?.celebrityProfile?.categories || [],
    bio: user?.celebrityProfile?.bio || "",
    occasions: [],
    tones: [],
    tags: [],
    socialLinks: [""],
    pricing: {
      characterCount: 0,
      amount: "",
      extraCharacterCount: 0,
      extraCharacterAmount: "",
    },
  };

  const getAllCategoris = async () => {
    try {
      setLoading(true);
      const resp = await handleApiCall({
        method: "GET",
        url: `/categories/`,
      });
      if (resp.status === 200) {
        setCategories(resp.data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values) => {
    try {
      const url =
        user?.role === "celebrity"
          ? `/celebrities/${user?.celebrityProfile?._id}/update-profile`
          : "/users/profile";

      const formData = new FormData();

      if (typeof values.profileImage !== "string")
        formData.append("profileImage", values.profileImage);
      formData.append("firstName", values.firstName);
      formData.append("lastName", values.lastName);
      values.tags.forEach((tag) => {
        formData.append("tags[]", tag);
      });
      formData.append("bio", values.bio);
      values.occasions.forEach((occasions) => {
        formData.append("occasions[]", occasions);
      });
      if (user?.role === "celebrity") {
        formData.append("name", values.name);
        values.roles.forEach((category) => {
          formData.append("categories[]", category);
        });
        values.socialLinks.forEach((link) => {
          formData.append("socialLinks[]", link);
        });
        values.tones.forEach((tone) => {
          formData.append("tones[]", tone);
        });
        formData.append("pricing", values.pricing);
      }

      const resp = await handleApiCall({
        method: user?.role === "celebrity" ? "POST" : "PUT",
        url,
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (resp.status === 200 || resp.status === 201) {
        dispatch(updateUser({ user: resp?.data }));
        setShowSuccess(true);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleNext = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleSkip = () => {
    setCurrentStep(steps.length - 1);
  };

  const StepComponent = steps[currentStep].component;

  useEffect(() => {
    getAllCategoris();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader variant={LoaderVariants.rotatingLines} size={48} />
      </div>
    );
  }

  return (
    <>
      {showSuccess && <Success />}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, errors, touched }) => (
          <Form className="min-h-screen bg-gradient-custom text-white flex flex-col items-center p-4">
            {/* Header */}
            <div className="w-full flex justify-between items-center mb-4 max-w-md">
              <button
                type="button"
                onClick={handlePrev}
                className="text-sm cursor-pointer"
                disabled={currentStep === 0}
              >
                <Image
                  src="/left-arrow.svg"
                  width={20}
                  height={20}
                  alt="Back"
                  className={
                    currentStep === 0
                      ? "opacity-50 cursor-not-allowed"
                      : "cursor-pointer"
                  }
                />
              </button>
              <h2 className="text-base font-medium w-full text-center">
                Create Profile{" "}
              </h2>
              <button
                type="button"
                onClick={handleSkip}
                className="text-xs cursor-pointer hover:underline"
                disabled={currentStep === steps.length - 1}
              >
                {currentStep === steps.length - 1 ? "" : "Skip"}
              </button>
            </div>

            {/* Progress Bar */}
            <div className="w-full max-w-md bg-gray-900 rounded-full h-2 mb-6">
              <div
                className="bg-[#7C7C7C] h-2 rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            {/* Profile Content */}
            <div className="bg-white text-black rounded-2xl shadow-xl w-full max-w-md p-6">
              <div className="mb-6">
                {" "}
                <h3 className="text-center text-xl font-medium mb-0 leading-[160%]">
                  {steps[currentStep].title}
                </h3>
                <p className="text-center text-xs text-[#6C7278] tracking-[-2%] leading-[160%]">
                  {steps[currentStep].description}
                </p>
              </div>
              <StepComponent
                values={values}
                errors={errors}
                touched={touched}
                categories={categories}
              />

              <div className="mt-6 flex gap-3">
                <button
                  type={currentStep === steps.length - 1 ? "submit" : "button"}
                  onClick={(e) => {
                    if (currentStep < steps.length - 1) {
                      e.preventDefault();
                      handleNext();
                    }
                  }}
                  className="flex w-full justify-center rounded-lg text-white p-3 grayButton cursor-pointer"
                >
                  {!loading && isApiLoading ? (
                    <Loader
                      variant={LoaderVariants.rotatingLines}
                      size={25}
                      color="white"
                    />
                  ) : currentStep === steps.length - 1 ? (
                    "Finish"
                  ) : (
                    "Continue"
                  )}
                </button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </>
  );
};

export default ProfileCreation;
