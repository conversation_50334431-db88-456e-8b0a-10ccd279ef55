'use client';

import { useEffect, useState } from 'react';
import VideoPreviewComponent from '@/components/VideoPreview/video-preview';
import SampleGreetingService from '@/services/SampleGreetingService';
import { useSelector } from 'react-redux';
import { selectAuth } from '@/redux/slices/auth/selectors';

export default function VideoPreviewPage() {
  const [greeting, setGreeting] = useState(null);
  const auth = useSelector(selectAuth);
  const celebrityId = auth?.userInfo?.user?.celebrityProfile?._id || auth?.userInfo?.user?._id;

  useEffect(() => {
    async function fetchGreeting() {
      if (!celebrityId) return;
      try {
        const res = await SampleGreetingService.getSampleGreetings(celebrityId);
        if (Array.isArray(res.data) && res.data.length > 0) {
          setGreeting(res.data[0]);
        } else if (res.data) {
          setGreeting(res.data);
        }
      } catch (err) {
        setGreeting(null);
      }
    }
    fetchGreeting();
  }, [celebrityId]);

  if (!greeting) {
    return <div className='text-white flex justify-center items-center min-h-screen'>Loading...</div>;
  }

  return (
    <div className='bg-[#0c0c0c] min-h-screen flex flex-col justify-center'>
      <VideoPreviewComponent
        // videoSrc={"https://www.w3schools.com/html/mov_bbb.mp4"}
        videoSrc={greeting.recordedVideoUrl}

        duration={60}
        avatar={greeting.avatarImageUrl}
        greeting={greeting}
      />
    </div>
  );
}
