'use client';

import { useState } from 'react';
import Link from 'next/link';

// Mock data for orders
const mockOrders = {
  overview: {
    totalOrders: 1200,
    pendingOrders: 45,
    completedOrders: 1100,
    refundedOrders: 55,
    totalRevenue: 150000,
  },
  orders: [
    {
      id: 'ORD-001',
      customer: {
        id: 'CUST-001',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-001',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      date: '2024-04-15T10:30:00Z',
      amount: 300,
      status: 'completed',
      type: 'birthday',
      paymentMethod: 'credit_card',
      paymentStatus: 'paid',
      refundStatus: null,
      message:
        'Happy Birthday! Wishing you a day filled with joy and laughter.',
      deliveryDate: '2024-04-16T00:00:00Z',
      rating: 5,
      review: 'Amazing service! The video was perfect.',
    },
    {
      id: 'ORD-002',
      customer: {
        id: 'CUST-002',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-002',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      date: '2024-04-14T09:15:00Z',
      amount: 250,
      status: 'pending',
      type: 'anniversary',
      paymentMethod: 'paypal',
      paymentStatus: 'paid',
      refundStatus: null,
      message:
        'Congratulations on your anniversary! May your love continue to grow.',
      deliveryDate: '2024-04-15T00:00:00Z',
      rating: null,
      review: null,
    },
    {
      id: 'ORD-003',
      customer: {
        id: 'CUST-003',
        name: 'Robert Brown',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-003',
        name: 'Emma Davis',
        email: '<EMAIL>',
      },
      date: '2024-03-15T16:45:00Z',
      amount: 400,
      status: 'refunded',
      type: 'graduation',
      paymentMethod: 'credit_card',
      paymentStatus: 'refunded',
      refundStatus: 'completed',
      message: 'Congratulations on your graduation! Your future is bright.',
      deliveryDate: '2024-03-16T00:00:00Z',
      rating: null,
      review: null,
      refundReason: 'Customer requested cancellation before delivery',
    },
  ],
};

export default function AdminOrdersPage() {
  const [orders, setOrders] = useState(mockOrders);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredOrders = orders.orders.filter((order) => {
    const matchesStatus =
      statusFilter === 'all' || order.status === statusFilter;
    const matchesType = typeFilter === 'all' || order.type === typeFilter;
    const matchesSearch =
      searchTerm === '' ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.celebrity.name.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesType && matchesSearch;
  });

  const handleStatusChange = (orderId, newStatus) => {
    setOrders((prev) => ({
      ...prev,
      orders: prev.orders.map((order) =>
        order.id === orderId ? { ...order, status: newStatus } : order
      ),
    }));
  };

  const handleRefund = (orderId) => {
    setOrders((prev) => ({
      ...prev,
      orders: prev.orders.map((order) =>
        order.id === orderId
          ? {
              ...order,
              status: 'refunded',
              paymentStatus: 'refunded',
              refundStatus: 'completed',
            }
          : order
      ),
    }));
  };

  // List of admin pages for navigation
  const adminPages = [
    { name: 'Dashboard', href: '/admin/dashboard' },
    { name: 'Users', href: '/admin/users' },
    { name: 'Customers', href: '/admin/customers' },
    { name: 'Analytics', href: '/admin/analytics' },
    { name: 'Categories', href: '/admin/categories' },
    { name: 'Orders', href: '/admin/orders' },
    { name: 'Celebrity', href: '/admin/celebrity' },
    { name: 'Payments', href: '/admin/payments' },
    { name: 'Audit', href: '/admin/audit' },
    { name: 'Notifications', href: '/admin/notifications' },
    { name: 'Support', href: '/admin/support' },
    { name: 'Reports', href: '/admin/reports' },
    { name: 'Settings', href: '/admin/settings' },
    { name: 'Profile', href: '/admin/profile' },
  ];

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      {/* Admin Navigation */}
      <nav className='mb-8 bg-white shadow rounded-lg p-4 flex flex-wrap gap-4'>
        {adminPages.map((page) => (
          <Link
            key={page.href}
            href={page.href}
            className='text-sm font-medium text-indigo-700 hover:underline'
          >
            {page.name}
          </Link>
        ))}
      </nav>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Orders</h1>
          <p className='mt-2 text-sm text-white'>
            Manage orders and handle refunds
          </p>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {orders.overview.totalOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Pending Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {orders.overview.pendingOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Completed Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {orders.overview.completedOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Refunded Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {orders.overview.refundedOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Revenue
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(orders.overview.totalRevenue)}
              </dd>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-3'>
          <div>
            <label
              htmlFor='search'
              className='block text-sm font-medium text-white'
            >
              Search
            </label>
            <input
              type='text'
              id='search'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder='Search by ID, customer, or celebrity...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='pending'>Pending</option>
              <option value='completed'>Completed</option>
              <option value='refunded'>Refunded</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='type'
              className='block text-sm font-medium text-white'
            >
              Type
            </label>
            <select
              id='type'
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Types</option>
              <option value='birthday'>Birthday</option>
              <option value='anniversary'>Anniversary</option>
              <option value='graduation'>Graduation</option>
              <option value='corporate'>Corporate</option>
            </select>
          </div>
        </div>

        {/* Orders Table */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Order ID
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Customer
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Celebrity
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Amount
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Payment
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredOrders.map((order) => (
                  <tr
                    key={order.id}
                    onClick={() => setSelectedOrder(order)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                      {order.id}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm text-gray-900'>
                        {order.customer.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {order.customer.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm text-gray-900'>
                        {order.celebrity.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {order.celebrity.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {formatCurrency(order.amount)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <select
                        value={order.status}
                        onChange={(e) =>
                          handleStatusChange(order.id, e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          order.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : order.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value='pending'>Pending</option>
                        <option value='completed'>Completed</option>
                        <option value='refunded'>Refunded</option>
                      </select>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          order.paymentStatus === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {order.paymentStatus}
                      </span>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {new Date(order.date).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Order Details Modal */}
        {selectedOrder && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Order Details - {selectedOrder.id}
                  </h3>
                  <button
                    onClick={() => setSelectedOrder(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Customer
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedOrder.customer.name} (
                      {selectedOrder.customer.email})
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Celebrity
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedOrder.celebrity.name} (
                      {selectedOrder.celebrity.email})
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Amount
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedOrder.amount)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <select
                        value={selectedOrder.status}
                        onChange={(e) =>
                          handleStatusChange(selectedOrder.id, e.target.value)
                        }
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          selectedOrder.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : selectedOrder.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value='pending'>Pending</option>
                        <option value='completed'>Completed</option>
                        <option value='refunded'>Refunded</option>
                      </select>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Payment Method
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedOrder.paymentMethod.replace('_', ' ')}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Payment Status
                    </dt>
                    <dd className='mt-1'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          selectedOrder.paymentStatus === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {selectedOrder.paymentStatus}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Order Date
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedOrder.date).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Delivery Date
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedOrder.deliveryDate).toLocaleString()}
                    </dd>
                  </div>
                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Message
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedOrder.message}
                    </dd>
                  </div>

                  {selectedOrder.rating && (
                    <div className='sm:col-span-2'>
                      <dt className='text-sm font-medium text-gray-500'>
                        Rating & Review
                      </dt>
                      <dd className='mt-1'>
                        <div className='text-sm text-gray-900'>
                          Rating: {selectedOrder.rating}/5
                        </div>
                        <div className='text-sm text-gray-500 mt-1'>
                          {selectedOrder.review}
                        </div>
                      </dd>
                    </div>
                  )}

                  {selectedOrder.refundStatus && (
                    <div className='sm:col-span-2'>
                      <dt className='text-sm font-medium text-gray-500'>
                        Refund Information
                      </dt>
                      <dd className='mt-1'>
                        <div className='text-sm text-gray-900'>
                          Status: {selectedOrder.refundStatus}
                        </div>
                        <div className='text-sm text-gray-500 mt-1'>
                          Reason: {selectedOrder.refundReason}
                        </div>
                      </dd>
                    </div>
                  )}

                  {selectedOrder.status !== 'refunded' && (
                    <div className='sm:col-span-2'>
                      <button
                        onClick={() => handleRefund(selectedOrder.id)}
                        className='w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
                      >
                        Process Refund
                      </button>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
