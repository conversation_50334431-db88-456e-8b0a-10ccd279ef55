'use client';

import { useState } from 'react';

// Mock data for analytics
const mockAnalytics = {
  overview: {
    totalRevenue: 150000,
    totalOrders: 1200,
    activeUsers: 850,
    averageOrderValue: 125,
  },
  revenue: {
    monthly: [
      { month: 'Jan', revenue: 12000 },
      { month: 'Feb', revenue: 15000 },
      { month: 'Mar', revenue: 18000 },
      { month: 'Apr', revenue: 22000 },
      { month: 'May', revenue: 25000 },
      { month: 'Jun', revenue: 28000 },
    ],
    byCategory: [
      { category: 'Birthday', revenue: 60000 },
      { category: 'Anniversary', revenue: 45000 },
      { category: 'Graduation', revenue: 25000 },
      { category: 'Corporate', revenue: 20000 },
    ],
  },
  users: {
    newRegistrations: [
      { date: '2024-06-01', count: 25 },
      { date: '2024-06-02', count: 30 },
      { date: '2024-06-03', count: 28 },
      { date: '2024-06-04', count: 35 },
      { date: '2024-06-05', count: 32 },
      { date: '2024-06-06', count: 40 },
      { date: '2024-06-07', count: 38 },
    ],
    byType: [
      { type: 'Customers', count: 1200 },
      { type: 'Celebrities', count: 150 },
      { type: 'Admins', count: 5 },
    ],
  },
  orders: {
    byStatus: [
      { status: 'Completed', count: 900 },
      { status: 'Pending', count: 200 },
      { status: 'Refunded', count: 100 },
    ],
    averageDeliveryTime: '24 hours',
    satisfactionRate: 4.8,
  },
  performance: {
    responseTime: '1.2s',
    uptime: '99.9%',
    errorRate: '0.1%',
    activeSessions: 450,
  },
};

export default function AdminAnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number);
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Analytics</h1>
          <p className='mt-2 text-sm text-white'>
            Platform metrics, insights, and performance data
          </p>
        </div>

        {/* Time Range Selector */}
        <div className='mb-6'>
          <div className='flex items-center space-x-4'>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className='rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='7d'>Last 7 days</option>
              <option value='30d'>Last 30 days</option>
              <option value='90d'>Last 90 days</option>
              <option value='1y'>Last year</option>
            </select>
          </div>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Revenue
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(mockAnalytics.overview.totalRevenue)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatNumber(mockAnalytics.overview.totalOrders)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Active Users
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatNumber(mockAnalytics.overview.activeUsers)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Average Order Value
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(mockAnalytics.overview.averageOrderValue)}
              </dd>
            </div>
          </div>
        </div>

        {/* Metric Selector */}
        <div className='mb-6'>
          <div className='flex items-center space-x-4'>
            <button
              onClick={() => setSelectedMetric('revenue')}
              className={`px-4 py-2 rounded-md cursor-pointer ${
                selectedMetric === 'revenue'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Revenue
            </button>
            <button
              onClick={() => setSelectedMetric('users')}
              className={`px-4 py-2 rounded-md cursor-pointer ${
                selectedMetric === 'users'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Users
            </button>
            <button
              onClick={() => setSelectedMetric('orders')}
              className={`px-4 py-2 rounded-md cursor-pointer ${
                selectedMetric === 'orders'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Orders
            </button>
            <button
              onClick={() => setSelectedMetric('performance')}
              className={`px-4 py-2 rounded-md cursor-pointer ${
                selectedMetric === 'performance'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Performance
            </button>
          </div>
        </div>

        {/* Revenue Section */}
        {selectedMetric === 'revenue' && (
          <div className='space-y-6'>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Monthly Revenue
              </h3>
              <div className='h-64 flex items-end space-x-2'>
                {mockAnalytics.revenue.monthly.map((item) => (
                  <div key={item.month} className='flex-1'>
                    <div
                      className='bg-indigo-600 rounded-t'
                      style={{
                        height: `${(item.revenue / 28000) * 100}%`,
                      }}
                    />
                    <div className='text-center text-sm text-gray-500 mt-2'>
                      {item.month}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Revenue by Category
              </h3>
              <div className='space-y-4'>
                {mockAnalytics.revenue.byCategory.map((item) => (
                  <div key={item.category}>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-gray-700'>
                        {item.category}
                      </span>
                      <span className='text-sm text-gray-500'>
                        {formatCurrency(item.revenue)}
                      </span>
                    </div>
                    <div className='w-full bg-gray-200 rounded-full h-2'>
                      <div
                        className='bg-indigo-600 h-2 rounded-full'
                        style={{
                          width: `${(item.revenue / 60000) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Users Section */}
        {selectedMetric === 'users' && (
          <div className='space-y-6'>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                New Registrations
              </h3>
              <div className='h-64 flex items-end space-x-2'>
                {mockAnalytics.users.newRegistrations.map((item) => (
                  <div key={item.date} className='flex-1'>
                    <div
                      className='bg-green-600 rounded-t'
                      style={{
                        height: `${(item.count / 40) * 100}%`,
                      }}
                    />
                    <div className='text-center text-sm text-gray-500 mt-2'>
                      {new Date(item.date).toLocaleDateString('en-US', {
                        day: 'numeric',
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Users by Type
              </h3>
              <div className='space-y-4'>
                {mockAnalytics.users.byType.map((item) => (
                  <div key={item.type}>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-gray-700'>
                        {item.type}
                      </span>
                      <span className='text-sm text-gray-500'>
                        {formatNumber(item.count)}
                      </span>
                    </div>
                    <div className='w-full bg-gray-200 rounded-full h-2'>
                      <div
                        className='bg-green-600 h-2 rounded-full'
                        style={{
                          width: `${(item.count / 1200) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Orders Section */}
        {selectedMetric === 'orders' && (
          <div className='space-y-6'>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Orders by Status
              </h3>
              <div className='space-y-4'>
                {mockAnalytics.orders.byStatus.map((item) => (
                  <div key={item.status}>
                    <div className='flex items-center justify-between mb-1'>
                      <span className='text-sm font-medium text-gray-700'>
                        {item.status}
                      </span>
                      <span className='text-sm text-gray-500'>
                        {formatNumber(item.count)}
                      </span>
                    </div>
                    <div className='w-full bg-gray-200 rounded-full h-2'>
                      <div
                        className={`h-2 rounded-full ${
                          item.status === 'Completed'
                            ? 'bg-green-600'
                            : item.status === 'Pending'
                            ? 'bg-yellow-600'
                            : 'bg-red-600'
                        }`}
                        style={{
                          width: `${(item.count / 900) * 100}%`,
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className='grid grid-cols-1 gap-6 sm:grid-cols-3'>
              <div className='bg-white shadow rounded-lg p-6'>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>
                  Average Delivery Time
                </h3>
                <p className='text-3xl font-semibold text-gray-900'>
                  {mockAnalytics.orders.averageDeliveryTime}
                </p>
              </div>
              <div className='bg-white shadow rounded-lg p-6'>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>
                  Satisfaction Rate
                </h3>
                <p className='text-3xl font-semibold text-gray-900'>
                  {mockAnalytics.orders.satisfactionRate}/5
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Performance Section */}
        {selectedMetric === 'performance' && (
          <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4'>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                Response Time
              </h3>
              <p className='text-3xl font-semibold text-gray-900'>
                {mockAnalytics.performance.responseTime}
              </p>
            </div>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>Uptime</h3>
              <p className='text-3xl font-semibold text-gray-900'>
                {mockAnalytics.performance.uptime}
              </p>
            </div>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                Error Rate
              </h3>
              <p className='text-3xl font-semibold text-gray-900'>
                {mockAnalytics.performance.errorRate}
              </p>
            </div>
            <div className='bg-white shadow rounded-lg p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                Active Sessions
              </h3>
              <p className='text-3xl font-semibold text-gray-900'>
                {formatNumber(mockAnalytics.performance.activeSessions)}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
