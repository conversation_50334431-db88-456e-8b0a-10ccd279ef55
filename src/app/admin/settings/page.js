'use client';

import { useState } from 'react';

// Mock data for settings
const mockSettings = {
  platform: {
    siteName: 'CelebGreetings',
    siteDescription:
      'Get personalized video greetings from your favorite celebrities',
    contactEmail: '<EMAIL>',
    supportPhone: '+****************',
    maintenanceMode: false,
    allowRegistrations: true,
    defaultLanguage: 'en',
    timezone: 'UTC-5',
  },
  payment: {
    currency: 'USD',
    stripeEnabled: true,
    paypalEnabled: true,
    minimumPayout: 100,
    platformFee: 20,
    taxRate: 8.5,
    paymentMethods: ['credit_card', 'paypal', 'bank_transfer'],
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    notificationTypes: {
      newOrder: true,
      orderStatus: true,
      paymentReceived: true,
      payoutProcessed: true,
      systemAlert: true,
    },
  },
  security: {
    twoFactorAuth: true,
    sessionTimeout: 30,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
    },
    ipWhitelist: ['***********', '********'],
  },
};

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState(mockSettings);
  const [activeTab, setActiveTab] = useState('platform');
  const [isEditing, setIsEditing] = useState(false);
  const [editedSettings, setEditedSettings] = useState(null);

  const handleEdit = () => {
    setEditedSettings(JSON.parse(JSON.stringify(settings)));
    setIsEditing(true);
  };

  const handleSave = () => {
    setSettings(editedSettings);
    setIsEditing(false);
    setEditedSettings(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedSettings(null);
  };

  const handleSettingChange = (category, field, value) => {
    setEditedSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: value,
      },
    }));
  };

  const handleNotificationChange = (type, value) => {
    setEditedSettings((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        notificationTypes: {
          ...prev.notifications.notificationTypes,
          [type]: value,
        },
      },
    }));
  };

  const handlePasswordPolicyChange = (field, value) => {
    setEditedSettings((prev) => ({
      ...prev,
      security: {
        ...prev.security,
        passwordPolicy: {
          ...prev.security.passwordPolicy,
          [field]: value,
        },
      },
    }));
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Settings</h1>
          <p className='mt-2 text-sm text-white'>
            Manage platform settings and configurations
          </p>
        </div>

        {/* Tab Navigation */}
        <div className='mb-6'>
          <nav className='flex space-x-4'>
            {['platform', 'payment', 'notifications', 'security'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-3 py-2 rounded-md text-sm font-medium cursor-pointer ${
                  activeTab === tab
                    ? 'bg-indigo-100 text-indigo-700'
                    : 'text-white'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </nav>
        </div>

        {/* Settings Content */}
        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            {/* Platform Settings */}
            {activeTab === 'platform' && (
              <div className='space-y-6'>
                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Site Name
                    </label>
                    <input
                      type='text'
                      value={
                        isEditing
                          ? editedSettings.platform.siteName
                          : settings.platform.siteName
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'siteName',
                          e.target.value
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Contact Email
                    </label>
                    <input
                      type='email'
                      value={
                        isEditing
                          ? editedSettings.platform.contactEmail
                          : settings.platform.contactEmail
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'contactEmail',
                          e.target.value
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Support Phone
                    </label>
                    <input
                      type='tel'
                      value={
                        isEditing
                          ? editedSettings.platform.supportPhone
                          : settings.platform.supportPhone
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'supportPhone',
                          e.target.value
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Default Language
                    </label>
                    <select
                      value={
                        isEditing
                          ? editedSettings.platform.defaultLanguage
                          : settings.platform.defaultLanguage
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'defaultLanguage',
                          e.target.value
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    >
                      <option value='en'>English</option>
                      <option value='es'>Spanish</option>
                      <option value='fr'>French</option>
                    </select>
                  </div>
                </div>
                <div className='space-y-4'>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='maintenance-mode'
                      checked={
                        isEditing
                          ? editedSettings.platform.maintenanceMode
                          : settings.platform.maintenanceMode
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'maintenanceMode',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                    />
                    <label
                      htmlFor='maintenance-mode'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Maintenance Mode
                    </label>
                  </div>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='allow-registrations'
                      checked={
                        isEditing
                          ? editedSettings.platform.allowRegistrations
                          : settings.platform.allowRegistrations
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'platform',
                          'allowRegistrations',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                    />
                    <label
                      htmlFor='allow-registrations'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Allow New Registrations
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Payment Settings */}
            {activeTab === 'payment' && (
              <div className='space-y-6'>
                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Currency
                    </label>
                    <select
                      value={
                        isEditing
                          ? editedSettings.payment.currency
                          : settings.payment.currency
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'currency',
                          e.target.value
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    >
                      <option value='USD'>USD</option>
                      <option value='EUR'>EUR</option>
                      <option value='GBP'>GBP</option>
                    </select>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Platform Fee (%)
                    </label>
                    <input
                      type='number'
                      value={
                        isEditing
                          ? editedSettings.payment.platformFee
                          : settings.payment.platformFee
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'platformFee',
                          parseFloat(e.target.value)
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Tax Rate (%)
                    </label>
                    <input
                      type='number'
                      value={
                        isEditing
                          ? editedSettings.payment.taxRate
                          : settings.payment.taxRate
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'taxRate',
                          parseFloat(e.target.value)
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Minimum Payout
                    </label>
                    <input
                      type='number'
                      value={
                        isEditing
                          ? editedSettings.payment.minimumPayout
                          : settings.payment.minimumPayout
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'minimumPayout',
                          parseFloat(e.target.value)
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                </div>
                <div className='space-y-4'>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='stripe-enabled'
                      checked={
                        isEditing
                          ? editedSettings.payment.stripeEnabled
                          : settings.payment.stripeEnabled
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'stripeEnabled',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded '
                    />
                    <label
                      htmlFor='stripe-enabled'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Enable Stripe Payments
                    </label>
                  </div>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='paypal-enabled'
                      checked={
                        isEditing
                          ? editedSettings.payment.paypalEnabled
                          : settings.payment.paypalEnabled
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'payment',
                          'paypalEnabled',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded '
                    />
                    <label
                      htmlFor='paypal-enabled'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Enable PayPal Payments
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeTab === 'notifications' && (
              <div className='space-y-6'>
                <div className='space-y-4'>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='email-notifications'
                      checked={
                        isEditing
                          ? editedSettings.notifications.emailNotifications
                          : settings.notifications.emailNotifications
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'notifications',
                          'emailNotifications',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded '
                    />
                    <label
                      htmlFor='email-notifications'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Email Notifications
                    </label>
                  </div>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='push-notifications'
                      checked={
                        isEditing
                          ? editedSettings.notifications.pushNotifications
                          : settings.notifications.pushNotifications
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'notifications',
                          'pushNotifications',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded '
                    />
                    <label
                      htmlFor='push-notifications'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Push Notifications
                    </label>
                  </div>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='sms-notifications'
                      checked={
                        isEditing
                          ? editedSettings.notifications.smsNotifications
                          : settings.notifications.smsNotifications
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'notifications',
                          'smsNotifications',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded '
                    />
                    <label
                      htmlFor='sms-notifications'
                      className='ml-3 text-sm text-gray-700'
                    >
                      SMS Notifications
                    </label>
                  </div>
                </div>
                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Notification Types
                  </h3>
                  <div className='space-y-4'>
                    {Object.entries(
                      settings.notifications.notificationTypes
                    ).map(([type, enabled]) => (
                      <div key={type} className='flex items-center'>
                        <input
                          type='checkbox'
                          id={`notification-${type}`}
                          checked={
                            isEditing
                              ? editedSettings.notifications.notificationTypes[
                                  type
                                ]
                              : enabled
                          }
                          onChange={(e) =>
                            handleNotificationChange(type, e.target.checked)
                          }
                          disabled={!isEditing}
                          className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                        />
                        <label
                          htmlFor={`notification-${type}`}
                          className='ml-3 text-sm text-gray-700'
                        >
                          {type
                            .split(/(?=[A-Z])/)
                            .join(' ')
                            .replace(/^\w/, (c) => c.toUpperCase())}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div className='space-y-6'>
                <div className='space-y-4'>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='two-factor-auth'
                      checked={
                        isEditing
                          ? editedSettings.security.twoFactorAuth
                          : settings.security.twoFactorAuth
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'security',
                          'twoFactorAuth',
                          e.target.checked
                        )
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                    />
                    <label
                      htmlFor='two-factor-auth'
                      className='ml-3 text-sm text-gray-700'
                    >
                      Enable Two-Factor Authentication
                    </label>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Session Timeout (minutes)
                    </label>
                    <input
                      type='number'
                      value={
                        isEditing
                          ? editedSettings.security.sessionTimeout
                          : settings.security.sessionTimeout
                      }
                      onChange={(e) =>
                        handleSettingChange(
                          'security',
                          'sessionTimeout',
                          parseInt(e.target.value)
                        )
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                </div>
                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Password Policy
                  </h3>
                  <div className='space-y-4'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Minimum Length
                      </label>
                      <input
                        type='number'
                        value={
                          isEditing
                            ? editedSettings.security.passwordPolicy.minLength
                            : settings.security.passwordPolicy.minLength
                        }
                        onChange={(e) =>
                          handlePasswordPolicyChange(
                            'minLength',
                            parseInt(e.target.value)
                          )
                        }
                        disabled={!isEditing}
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                      />
                    </div>
                    <div className='space-y-2'>
                      <div className='flex items-center'>
                        <input
                          type='checkbox'
                          id='require-uppercase'
                          checked={
                            isEditing
                              ? editedSettings.security.passwordPolicy
                                  .requireUppercase
                              : settings.security.passwordPolicy
                                  .requireUppercase
                          }
                          onChange={(e) =>
                            handlePasswordPolicyChange(
                              'requireUppercase',
                              e.target.checked
                            )
                          }
                          disabled={!isEditing}
                          className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                        />
                        <label
                          htmlFor='require-uppercase'
                          className='ml-3 text-sm text-gray-700'
                        >
                          Require Uppercase Letters
                        </label>
                      </div>
                      <div className='flex items-center'>
                        <input
                          type='checkbox'
                          id='require-lowercase'
                          checked={
                            isEditing
                              ? editedSettings.security.passwordPolicy
                                  .requireLowercase
                              : settings.security.passwordPolicy
                                  .requireLowercase
                          }
                          onChange={(e) =>
                            handlePasswordPolicyChange(
                              'requireLowercase',
                              e.target.checked
                            )
                          }
                          disabled={!isEditing}
                          className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                        />
                        <label
                          htmlFor='require-lowercase'
                          className='ml-3 text-sm text-gray-700'
                        >
                          Require Lowercase Letters
                        </label>
                      </div>
                      <div className='flex items-center'>
                        <input
                          type='checkbox'
                          id='require-numbers'
                          checked={
                            isEditing
                              ? editedSettings.security.passwordPolicy
                                  .requireNumbers
                              : settings.security.passwordPolicy.requireNumbers
                          }
                          onChange={(e) =>
                            handlePasswordPolicyChange(
                              'requireNumbers',
                              e.target.checked
                            )
                          }
                          disabled={!isEditing}
                          className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                        />
                        <label
                          htmlFor='require-numbers'
                          className='ml-3 text-sm text-gray-700'
                        >
                          Require Numbers
                        </label>
                      </div>
                      <div className='flex items-center'>
                        <input
                          type='checkbox'
                          id='require-special'
                          checked={
                            isEditing
                              ? editedSettings.security.passwordPolicy
                                  .requireSpecialChars
                              : settings.security.passwordPolicy
                                  .requireSpecialChars
                          }
                          onChange={(e) =>
                            handlePasswordPolicyChange(
                              'requireSpecialChars',
                              e.target.checked
                            )
                          }
                          disabled={!isEditing}
                          className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                        />
                        <label
                          htmlFor='require-special'
                          className='ml-3 text-sm text-gray-700'
                        >
                          Require Special Characters
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className='mt-6 flex justify-end space-x-3'>
              {!isEditing ? (
                <button
                  onClick={handleEdit}
                  className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                >
                  Edit Settings
                </button>
              ) : (
                <>
                  <button
                    onClick={handleCancel}
                    className='inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                  >
                    Save Changes
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
