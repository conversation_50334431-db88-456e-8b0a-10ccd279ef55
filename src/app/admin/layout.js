'use client';

import { useEffect, useState } from 'react';
import Sidebar from '@/components/Sidebar';
import Link from 'next/link';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/navigation';
import { selectAuth } from '@/redux/slices/auth/selectors';

export default function AdminLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

    const auth = useSelector(selectAuth);
    
    const router = useRouter();
    
    useEffect(() => {
      if (auth?.userInfo?.user?.role !== "admin") {
        router.push('/');
      }
    }, [auth, router]); 


  return (
    <div className='flex flex-col bg-[#0c0c0c] text-white'>
      {/* Mobile Top Bar */}
      <div className='lg:hidden p-6 flex items-center justify-between border-b border-white/10'>
        <Link href='/' className='text-2xl font-bold text-white'>
          CelebGreetings
        </Link>{' '}
        <button onClick={() => setSidebarOpen(true)} className='text-white'>
          {/* Hamburger Icon SVG */}
          <svg
            xmlns='http://www.w3.org/2000/svg'
            fill='none'
            viewBox='0 0 24 24'
            strokeWidth={1.5}
            stroke='currentColor'
            className='w-6 h-6'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              d='M3.75 5.25h16.5M3.75 12h16.5M3.75 18.75h16.5'
            />
          </svg>
        </button>
      </div>

      <div className='flex flex-1'>
        {/* Sidebar for large screens */}
        <div className='hidden lg:block'>
          <Sidebar />
        </div>

        {/* Sidebar overlay for mobile */}
        {sidebarOpen && (
          <div className='fixed inset-0 z-40 flex lg:hidden'>
            {/* Overlay */}
            <div
              className='fixed inset-0 bg-black/50'
              onClick={() => setSidebarOpen(false)}
            />
            {/* Sidebar Panel */}
            <div className='relative z-50 w-64 bg-[#0c0c0c] shadow-xl'>
              <Sidebar />
              {/* Close button */}
              <button
                onClick={() => setSidebarOpen(false)}
                className='absolute top-4 right-4 text-white text-2xl'
              >
                &times;
              </button>
            </div>
          </div>
        )}

        {/* Main content */}
        <main className='flex-1 px-4 sm:px-6 lg:px-8 py-8'>{children}</main>
      </div>
    </div>
  );
}
