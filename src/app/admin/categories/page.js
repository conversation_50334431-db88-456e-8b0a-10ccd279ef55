'use client';

import { useState } from 'react';

// Mock data for categories
const mockCategories = {
  overview: {
    totalCategories: 8,
    activeCategories: 7,
    totalOrders: 1200,
    averagePrice: 250,
  },
  categories: [
    {
      id: 'CAT-001',
      name: 'Birthday',
      description: 'Personalized birthday messages from celebrities',
      status: 'active',
      basePrice: 200,
      minPrice: 150,
      maxPrice: 500,
      averageDeliveryTime: '24 hours',
      orderCount: 450,
      revenue: 90000,
      features: [
        'Personalized message',
        'Name mention',
        'Custom background',
        'Downloadable video',
      ],
      requirements: [
        'Recipient name',
        'Birthday date',
        'Special message (optional)',
      ],
      availability: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
      },
    },
    {
      id: 'CAT-002',
      name: 'Anniversary',
      description: 'Celebrate special milestones with celebrity wishes',
      status: 'active',
      basePrice: 300,
      minPrice: 250,
      maxPrice: 600,
      averageDeliveryTime: '48 hours',
      orderCount: 300,
      revenue: 90000,
      features: [
        'Personalized message',
        'Couple names',
        'Anniversary year',
        'Custom background',
        'Downloadable video',
      ],
      requirements: [
        'Couple names',
        'Anniversary date',
        'Years of marriage',
        'Special message (optional)',
      ],
      availability: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true,
      },
    },
    {
      id: 'CAT-003',
      name: 'Graduation',
      description:
        'Celebrate academic achievements with celebrity congratulations',
      status: 'active',
      basePrice: 250,
      minPrice: 200,
      maxPrice: 450,
      averageDeliveryTime: '36 hours',
      orderCount: 200,
      revenue: 50000,
      features: [
        'Personalized message',
        'Graduate name',
        'Degree/Field of study',
        'Custom background',
        'Downloadable video',
      ],
      requirements: [
        'Graduate name',
        'Graduation date',
        'Degree/Field of study',
        'Special message (optional)',
      ],
      availability: {
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: false,
        sunday: false,
      },
    },
  ],
};

export default function AdminCategoriesPage() {
  const [categories, setCategories] = useState(mockCategories);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editedCategory, setEditedCategory] = useState(null);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredCategories = categories.categories.filter((category) => {
    const matchesStatus =
      statusFilter === 'all' || category.status === statusFilter;
    const matchesSearch =
      searchTerm === '' ||
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  const handleStatusChange = (categoryId, newStatus) => {
    setCategories((prev) => ({
      ...prev,
      categories: prev.categories.map((category) =>
        category.id === categoryId
          ? { ...category, status: newStatus }
          : category
      ),
    }));
  };

  const handleEdit = (category) => {
    setEditedCategory({ ...category });
    setIsEditing(true);
  };

  const handleSave = () => {
    setCategories((prev) => ({
      ...prev,
      categories: prev.categories.map((category) =>
        category.id === editedCategory.id ? editedCategory : category
      ),
    }));
    setIsEditing(false);
    setEditedCategory(null);
  };

  const handleAvailabilityChange = (day) => {
    setEditedCategory((prev) => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: !prev.availability[day],
      },
    }));
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Categories</h1>
          <p className='mt-2 text-sm text-white'>
            Manage greeting types, pricing, and availability
          </p>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Categories
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {categories.overview.totalCategories}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Active Categories
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {categories.overview.activeCategories}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {categories.overview.totalOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Average Price
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(categories.overview.averagePrice)}
              </dd>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2'>
          <div>
            <label
              htmlFor='search'
              className='block text-sm font-medium text-white'
            >
              Search
            </label>
            <input
              type='text'
              id='search'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder='Search by name or description...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        </div>

        {/* Categories Table */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Category
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Base Price
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Orders
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Revenue
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Delivery Time
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredCategories.map((category) => (
                  <tr
                    key={category.id}
                    onClick={() => setSelectedCategory(category)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {category.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {category.description}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <select
                        value={category.status}
                        onChange={(e) =>
                          handleStatusChange(category.id, e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          category.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {formatCurrency(category.basePrice)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {category.orderCount} orders
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {formatCurrency(category.revenue)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {category.averageDeliveryTime}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Category Details Modal */}
        {selectedCategory && !isEditing && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Category Details - {selectedCategory.name}
                  </h3>
                  <div className='flex items-center space-x-4'>
                    <button
                      onClick={() => handleEdit(selectedCategory)}
                      className='text-indigo-600 hover:text-indigo-900'
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => setSelectedCategory(null)}
                      className='text-gray-400 hover:text-gray-500'
                    >
                      <span className='sr-only'>Close</span>
                      <svg
                        className='h-6 w-6'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M6 18L18 6M6 6l12 12'
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Description
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCategory.description}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <select
                        value={selectedCategory.status}
                        onChange={(e) =>
                          handleStatusChange(
                            selectedCategory.id,
                            e.target.value
                          )
                        }
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          selectedCategory.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Base Price
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedCategory.basePrice)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Price Range
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedCategory.minPrice)} -{' '}
                      {formatCurrency(selectedCategory.maxPrice)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Orders
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCategory.orderCount}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Revenue
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedCategory.revenue)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Delivery Time
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCategory.averageDeliveryTime}
                    </dd>
                  </div>

                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Features
                    </dt>
                    <dd className='mt-1'>
                      <ul className='list-disc pl-5 text-sm text-gray-900'>
                        {selectedCategory.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </dd>
                  </div>

                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Requirements
                    </dt>
                    <dd className='mt-1'>
                      <ul className='list-disc pl-5 text-sm text-gray-900'>
                        {selectedCategory.requirements.map(
                          (requirement, index) => (
                            <li key={index}>{requirement}</li>
                          )
                        )}
                      </ul>
                    </dd>
                  </div>

                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Availability
                    </dt>
                    <dd className='mt-1'>
                      <div className='grid grid-cols-7 gap-2'>
                        {Object.entries(selectedCategory.availability).map(
                          ([day, available]) => (
                            <div
                              key={day}
                              className={`text-center p-2 rounded ${
                                available
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {day.charAt(0).toUpperCase()}
                            </div>
                          )
                        )}
                      </div>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}

        {/* Edit Category Modal */}
        {isEditing && editedCategory && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Edit Category - {editedCategory.name}
                  </h3>
                  <div className='flex items-center space-x-4'>
                    <button
                      onClick={handleSave}
                      className='text-indigo-600 hover:text-indigo-900'
                    >
                      Save
                    </button>
                    <button
                      onClick={() => {
                        setIsEditing(false);
                        setEditedCategory(null);
                      }}
                      className='text-gray-400 hover:text-gray-500'
                    >
                      <span className='sr-only'>Close</span>
                      <svg
                        className='h-6 w-6'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M6 18L18 6M6 6l12 12'
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div className='px-6 py-4'>
                <div className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <label
                      htmlFor='name'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Name
                    </label>
                    <input
                      type='text'
                      id='name'
                      value={editedCategory.name}
                      onChange={(e) =>
                        setEditedCategory({
                          ...editedCategory,
                          name: e.target.value,
                        })
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    />
                  </div>
                  <div>
                    <label
                      htmlFor='status'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Status
                    </label>
                    <select
                      id='status'
                      value={editedCategory.status}
                      onChange={(e) =>
                        setEditedCategory({
                          ...editedCategory,
                          status: e.target.value,
                        })
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    >
                      <option value='active'>Active</option>
                      <option value='inactive'>Inactive</option>
                    </select>
                  </div>
                  <div>
                    <label
                      htmlFor='basePrice'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Base Price
                    </label>
                    <input
                      type='number'
                      id='basePrice'
                      value={editedCategory.basePrice}
                      onChange={(e) =>
                        setEditedCategory({
                          ...editedCategory,
                          basePrice: Number(e.target.value),
                        })
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    />
                  </div>
                  <div>
                    <label
                      htmlFor='deliveryTime'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Average Delivery Time
                    </label>
                    <input
                      type='text'
                      id='deliveryTime'
                      value={editedCategory.averageDeliveryTime}
                      onChange={(e) =>
                        setEditedCategory({
                          ...editedCategory,
                          averageDeliveryTime: e.target.value,
                        })
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    />
                  </div>
                  <div className='sm:col-span-2'>
                    <label
                      htmlFor='description'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Description
                    </label>
                    <textarea
                      id='description'
                      rows={3}
                      value={editedCategory.description}
                      onChange={(e) =>
                        setEditedCategory({
                          ...editedCategory,
                          description: e.target.value,
                        })
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    />
                  </div>

                  <div className='sm:col-span-2'>
                    <label className='block text-sm font-medium text-gray-700'>
                      Availability
                    </label>
                    <div className='mt-2 grid grid-cols-7 gap-2'>
                      {Object.entries(editedCategory.availability).map(
                        ([day, available]) => (
                          <button
                            key={day}
                            type='button'
                            onClick={() => handleAvailabilityChange(day)}
                            className={`text-center p-2 rounded ${
                              available
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {day.charAt(0).toUpperCase()}
                          </button>
                        )
                      )}
                    </div>
                  </div>

                  <div className='sm:col-span-2'>
                    <label className='block text-sm font-medium text-gray-700'>
                      Features
                    </label>
                    <div className='mt-2 space-y-2'>
                      {editedCategory.features.map((feature, index) => (
                        <input
                          key={index}
                          type='text'
                          value={feature}
                          onChange={(e) => {
                            const newFeatures = [...editedCategory.features];
                            newFeatures[index] = e.target.value;
                            setEditedCategory({
                              ...editedCategory,
                              features: newFeatures,
                            });
                          }}
                          className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                        />
                      ))}
                      <button
                        type='button'
                        onClick={() =>
                          setEditedCategory({
                            ...editedCategory,
                            features: [...editedCategory.features, ''],
                          })
                        }
                        className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                      >
                        Add Feature
                      </button>
                    </div>
                  </div>

                  <div className='sm:col-span-2'>
                    <label className='block text-sm font-medium text-gray-700'>
                      Requirements
                    </label>
                    <div className='mt-2 space-y-2'>
                      {editedCategory.requirements.map((requirement, index) => (
                        <input
                          key={index}
                          type='text'
                          value={requirement}
                          onChange={(e) => {
                            const newRequirements = [
                              ...editedCategory.requirements,
                            ];
                            newRequirements[index] = e.target.value;
                            setEditedCategory({
                              ...editedCategory,
                              requirements: newRequirements,
                            });
                          }}
                          className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                        />
                      ))}
                      <button
                        type='button'
                        onClick={() =>
                          setEditedCategory({
                            ...editedCategory,
                            requirements: [...editedCategory.requirements, ''],
                          })
                        }
                        className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                      >
                        Add Requirement
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
