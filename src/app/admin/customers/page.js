'use client';

import { useState } from 'react';

// Mock data for customers
const mockCustomers = {
  overview: {
    totalCustomers: 500,
    activeCustomers: 450,
    totalOrders: 1200,
    totalRevenue: 150000,
  },
  customers: [
    {
      id: 'CUST-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      joinDate: '2024-01-10T09:00:00Z',
      lastActive: '2024-04-15T14:30:00Z',
      totalOrders: 5,
      totalSpent: 1250,
      recentOrders: [
        {
          id: 'ORD-001',
          date: '2024-04-15T10:30:00Z',
          amount: 300,
          status: 'completed',
          type: 'birthday',
          celebrity: '<PERSON>',
        },
      ],
      supportTickets: [
        {
          id: 'TICKET-001',
          subject: 'Order Status Inquiry',
          status: 'open',
          priority: 'medium',
          date: '2024-04-15T11:00:00Z',
        },
      ],
    },
    {
      id: 'CUST-002',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      joinDate: '2024-02-05T14:00:00Z',
      lastActive: '2024-04-14T16:45:00Z',
      totalOrders: 3,
      totalSpent: 750,
      recentOrders: [
        {
          id: 'ORD-002',
          date: '2024-04-14T09:15:00Z',
          amount: 250,
          status: 'completed',
          type: 'anniversary',
          celebrity: 'Mike Johnson',
        },
      ],
      supportTickets: [],
    },
    {
      id: 'CUST-003',
      name: 'Robert Brown',
      email: '<EMAIL>',
      status: 'inactive',
      joinDate: '2024-01-20T11:00:00Z',
      lastActive: '2024-03-15T10:20:00Z',
      totalOrders: 2,
      totalSpent: 500,
      recentOrders: [
        {
          id: 'ORD-003',
          date: '2024-03-15T16:45:00Z',
          amount: 400,
          status: 'refunded',
          type: 'graduation',
          celebrity: 'Emma Davis',
        },
      ],
      supportTickets: [
        {
          id: 'TICKET-002',
          subject: 'Refund Request',
          status: 'closed',
          priority: 'high',
          date: '2024-03-15T17:00:00Z',
        },
      ],
    },
  ],
};

export default function AdminCustomersPage() {
  const [customers, setCustomers] = useState(mockCustomers);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredCustomers = customers.customers.filter((customer) => {
    const matchesStatus =
      statusFilter === 'all' || customer.status === statusFilter;
    const matchesSearch =
      searchTerm === '' ||
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  const handleStatusChange = (customerId, newStatus) => {
    setCustomers((prev) => ({
      ...prev,
      customers: prev.customers.map((customer) =>
        customer.id === customerId
          ? { ...customer, status: newStatus }
          : customer
      ),
    }));
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Customers</h1>
          <p className='mt-2 text-sm text-white'>
            Manage customer accounts and orders
          </p>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Customers
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {customers.overview.totalCustomers}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Active Customers
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {customers.overview.activeCustomers}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {customers.overview.totalOrders}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Revenue
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(customers.overview.totalRevenue)}
              </dd>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2'>
          <div>
            <label
              htmlFor='search'
              className='block text-sm font-medium text-white'
            >
              Search
            </label>
            <input
              type='text'
              id='search'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder='Search by name or email...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        </div>

        {/* Customers Table */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Customer
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Orders
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Total Spent
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Support Tickets
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Last Active
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredCustomers.map((customer) => (
                  <tr
                    key={customer.id}
                    onClick={() => setSelectedCustomer(customer)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {customer.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {customer.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <select
                        value={customer.status}
                        onChange={(e) =>
                          handleStatusChange(customer.id, e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          customer.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {customer.totalOrders} orders
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {formatCurrency(customer.totalSpent)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          customer.supportTickets.length > 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {customer.supportTickets.length} tickets
                      </span>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {new Date(customer.lastActive).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Customer Details Modal */}
        {selectedCustomer && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Customer Details - {selectedCustomer.name}
                  </h3>
                  <button
                    onClick={() => setSelectedCustomer(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>Email</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCustomer.email}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <select
                        value={selectedCustomer.status}
                        onChange={(e) =>
                          handleStatusChange(
                            selectedCustomer.id,
                            e.target.value
                          )
                        }
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          selectedCustomer.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Join Date
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedCustomer.joinDate).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Total Orders
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCustomer.totalOrders}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Total Spent
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedCustomer.totalSpent)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Support Tickets
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCustomer.supportTickets.length}
                    </dd>
                  </div>

                  {/* Recent Orders */}
                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Recent Orders
                    </dt>
                    <dd className='mt-1'>
                      <div className='overflow-x-auto'>
                        <table className='min-w-full divide-y divide-gray-200'>
                          <thead>
                            <tr>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Order ID
                              </th>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Date
                              </th>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Amount
                              </th>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Status
                              </th>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Type
                              </th>
                              <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Celebrity
                              </th>
                            </tr>
                          </thead>
                          <tbody className='divide-y divide-gray-200'>
                            {selectedCustomer.recentOrders.map((order) => (
                              <tr key={order.id}>
                                <td className='px-4 py-2 text-sm text-gray-900'>
                                  {order.id}
                                </td>
                                <td className='px-4 py-2 text-sm text-gray-500'>
                                  {new Date(order.date).toLocaleString()}
                                </td>
                                <td className='px-4 py-2 text-sm text-gray-500'>
                                  {formatCurrency(order.amount)}
                                </td>
                                <td className='px-4 py-2 text-sm'>
                                  <span
                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                      order.status === 'completed'
                                        ? 'bg-green-100 text-green-800'
                                        : order.status === 'pending'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-red-100 text-red-800'
                                    }`}
                                  >
                                    {order.status}
                                  </span>
                                </td>
                                <td className='px-4 py-2 text-sm text-gray-500 capitalize'>
                                  {order.type}
                                </td>
                                <td className='px-4 py-2 text-sm text-gray-500'>
                                  {order.celebrity}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </dd>
                  </div>

                  {/* Support Tickets */}
                  {selectedCustomer.supportTickets.length > 0 && (
                    <div className='sm:col-span-2'>
                      <dt className='text-sm font-medium text-gray-500'>
                        Support Tickets
                      </dt>
                      <dd className='mt-1'>
                        <div className='overflow-x-auto'>
                          <table className='min-w-full divide-y divide-gray-200'>
                            <thead>
                              <tr>
                                <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                  Ticket ID
                                </th>
                                <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                  Subject
                                </th>
                                <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                  Status
                                </th>
                                <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                  Priority
                                </th>
                                <th className='px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                  Date
                                </th>
                              </tr>
                            </thead>
                            <tbody className='divide-y divide-gray-200'>
                              {selectedCustomer.supportTickets.map((ticket) => (
                                <tr key={ticket.id}>
                                  <td className='px-4 py-2 text-sm text-gray-900'>
                                    {ticket.id}
                                  </td>
                                  <td className='px-4 py-2 text-sm text-gray-500'>
                                    {ticket.subject}
                                  </td>
                                  <td className='px-4 py-2 text-sm'>
                                    <span
                                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        ticket.status === 'open'
                                          ? 'bg-yellow-100 text-yellow-800'
                                          : 'bg-green-100 text-green-800'
                                      }`}
                                    >
                                      {ticket.status}
                                    </span>
                                  </td>
                                  <td className='px-4 py-2 text-sm'>
                                    <span
                                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        ticket.priority === 'high'
                                          ? 'bg-red-100 text-red-800'
                                          : ticket.priority === 'medium'
                                          ? 'bg-yellow-100 text-yellow-800'
                                          : 'bg-green-100 text-green-800'
                                      }`}
                                    >
                                      {ticket.priority}
                                    </span>
                                  </td>
                                  <td className='px-4 py-2 text-sm text-gray-500'>
                                    {new Date(ticket.date).toLocaleString()}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
