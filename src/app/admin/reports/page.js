'use client';

import { useState } from 'react';

// Mock data for reports
const mockReports = {
  overview: {
    totalRevenue: 125000,
    totalRequests: 1500,
    activeCelebrities: 45,
    activeCustomers: 320,
    averageRating: 4.8,
    completionRate: 95,
  },
  revenue: {
    monthly: [
      { month: 'Jan', revenue: 25000, requests: 300 },
      { month: 'Feb', revenue: 30000, requests: 350 },
      { month: 'Mar', revenue: 35000, requests: 400 },
      { month: 'Apr', revenue: 35000, requests: 450 },
    ],
    byGreetingType: [
      { type: 'Birthday', revenue: 45000, count: 600 },
      { type: 'Anniversary', revenue: 35000, count: 400 },
      { type: 'Graduation', revenue: 25000, count: 300 },
      { type: 'Wedding', revenue: 20000, count: 200 },
    ],
  },
  userActivity: {
    newUsers: [
      { date: '2024-04-15', celebrities: 5, customers: 15 },
      { date: '2024-04-14', celebrities: 3, customers: 12 },
      { date: '2024-04-13', celebrities: 4, customers: 18 },
      { date: '2024-04-12', celebrities: 2, customers: 10 },
    ],
    activeUsers: {
      celebrities: [
        { name: '<PERSON>', requests: 45, earnings: 4500 },
        { name: '<PERSON>', requests: 38, earnings: 3800 },
        { name: '<PERSON>', requests: 32, earnings: 3200 },
      ],
      customers: [
        { name: '<PERSON> <PERSON>', requests: 12, spent: 1200 },
        { name: 'Bob <PERSON>', requests: 10, spent: 1000 },
        { name: 'Carol White', requests: 8, spent: 800 },
      ],
    },
  },
  performance: {
    responseTime: {
      average: 2.5,
      byGreetingType: [
        { type: 'Birthday', time: 2.2 },
        { type: 'Anniversary', time: 2.8 },
        { type: 'Graduation', time: 2.5 },
        { type: 'Wedding', time: 2.7 },
      ],
    },
    ratings: {
      average: 4.8,
      distribution: [
        { rating: 5, count: 1200 },
        { rating: 4, count: 200 },
        { rating: 3, count: 50 },
        { rating: 2, count: 30 },
        { rating: 1, count: 20 },
      ],
    },
  },
};

export default function AdminReportsPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('month');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Platform Reports</h1>
          <p className='mt-2 text-sm text-white'>
            View platform analytics, revenue reports, and user activity metrics
          </p>
        </div>

        {/* Time Range Selector */}
        <div className='mb-6'>
          <div className='flex space-x-4'>
            {['week', 'month', 'year', 'all'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  timeRange === range
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Tabs */}
        <div className='bg-white rounded-lg shadow'>
          <div className='border-b border-gray-200'>
            <nav className='flex -mb-px'>
              {['overview', 'revenue', 'activity', 'performance'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-6 text-sm font-medium border-b-2 ${
                    activeTab === tab
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className='p-6'>
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3'>
                <div className='bg-white overflow-hidden shadow rounded-lg'>
                  <div className='p-5'>
                    <div className='flex items-center'>
                      <div className='flex-shrink-0'>
                        <svg
                          className='h-6 w-6 text-gray-400'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                          />
                        </svg>
                      </div>
                      <div className='ml-5 w-0 flex-1'>
                        <dl>
                          <dt className='text-sm font-medium text-gray-500 truncate'>
                            Total Revenue
                          </dt>
                          <dd className='text-lg font-medium text-gray-900'>
                            {formatCurrency(mockReports.overview.totalRevenue)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='bg-white overflow-hidden shadow rounded-lg'>
                  <div className='p-5'>
                    <div className='flex items-center'>
                      <div className='flex-shrink-0'>
                        <svg
                          className='h-6 w-6 text-gray-400'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
                          />
                        </svg>
                      </div>
                      <div className='ml-5 w-0 flex-1'>
                        <dl>
                          <dt className='text-sm font-medium text-gray-500 truncate'>
                            Active Users
                          </dt>
                          <dd className='text-lg font-medium text-gray-900'>
                            {mockReports.overview.activeCelebrities}{' '}
                            Celebrities, {mockReports.overview.activeCustomers}{' '}
                            Customers
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='bg-white overflow-hidden shadow rounded-lg'>
                  <div className='p-5'>
                    <div className='flex items-center'>
                      <div className='flex-shrink-0'>
                        <svg
                          className='h-6 w-6 text-gray-400'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                          />
                        </svg>
                      </div>
                      <div className='ml-5 w-0 flex-1'>
                        <dl>
                          <dt className='text-sm font-medium text-gray-500 truncate'>
                            Performance
                          </dt>
                          <dd className='text-lg font-medium text-gray-900'>
                            {mockReports.overview.averageRating} Rating,{' '}
                            {mockReports.overview.completionRate}% Completion
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Revenue Tab */}
            {activeTab === 'revenue' && (
              <div className='space-y-6'>
                <div>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Monthly Revenue
                  </h3>
                  <div className='mt-4'>
                    <div className='overflow-x-auto'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Month
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Revenue
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Requests
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {mockReports.revenue.monthly.map((item) => (
                            <tr key={item.month}>
                              <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                {item.month}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {formatCurrency(item.revenue)}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {item.requests}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Revenue by Greeting Type
                  </h3>
                  <div className='mt-4'>
                    <div className='overflow-x-auto'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Type
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Revenue
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Count
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {mockReports.revenue.byGreetingType.map((item) => (
                            <tr key={item.type}>
                              <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                {item.type}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {formatCurrency(item.revenue)}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {item.count}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Activity Tab */}
            {activeTab === 'activity' && (
              <div className='space-y-6'>
                <div>
                  <h3 className='text-lg font-medium text-gray-900'>
                    New Users
                  </h3>
                  <div className='mt-4'>
                    <div className='overflow-x-auto'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Date
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              New Celebrities
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              New Customers
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {mockReports.userActivity.newUsers.map((item) => (
                            <tr key={item.date}>
                              <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                {new Date(item.date).toLocaleDateString()}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {item.celebrities}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                {item.customers}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <h3 className='text-lg font-medium text-gray-900'>
                      Top Celebrities
                    </h3>
                    <div className='mt-4'>
                      <div className='overflow-x-auto'>
                        <table className='min-w-full divide-y divide-gray-200'>
                          <thead>
                            <tr>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Name
                              </th>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Requests
                              </th>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Earnings
                              </th>
                            </tr>
                          </thead>
                          <tbody className='bg-white divide-y divide-gray-200'>
                            {mockReports.userActivity.activeUsers.celebrities.map(
                              (user) => (
                                <tr key={user.name}>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                    {user.name}
                                  </td>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                    {user.requests}
                                  </td>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                    {formatCurrency(user.earnings)}
                                  </td>
                                </tr>
                              )
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-lg font-medium text-gray-900'>
                      Top Customers
                    </h3>
                    <div className='mt-4'>
                      <div className='overflow-x-auto'>
                        <table className='min-w-full divide-y divide-gray-200'>
                          <thead>
                            <tr>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Name
                              </th>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Requests
                              </th>
                              <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                                Spent
                              </th>
                            </tr>
                          </thead>
                          <tbody className='bg-white divide-y divide-gray-200'>
                            {mockReports.userActivity.activeUsers.customers.map(
                              (user) => (
                                <tr key={user.name}>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                    {user.name}
                                  </td>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                    {user.requests}
                                  </td>
                                  <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                    {formatCurrency(user.spent)}
                                  </td>
                                </tr>
                              )
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Performance Tab */}
            {activeTab === 'performance' && (
              <div className='space-y-6'>
                <div>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Response Time
                  </h3>
                  <div className='mt-4'>
                    <div className='overflow-x-auto'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Greeting Type
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Average Response Time (days)
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {mockReports.performance.responseTime.byGreetingType.map(
                            (item) => (
                              <tr key={item.type}>
                                <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                  {item.type}
                                </td>
                                <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                  {item.time}
                                </td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Rating Distribution
                  </h3>
                  <div className='mt-4'>
                    <div className='overflow-x-auto'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Rating
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Count
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Percentage
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {mockReports.performance.ratings.distribution.map(
                            (item) => (
                              <tr key={item.rating}>
                                <td className='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>
                                  {item.rating} Stars
                                </td>
                                <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                  {item.count}
                                </td>
                                <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                                  {((item.count / 1500) * 100).toFixed(1)}%
                                </td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
