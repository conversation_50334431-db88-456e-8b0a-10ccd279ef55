'use client';

import { useState } from 'react';

// Mock data for celebrities
const mockCelebrities = {
  overview: {
    totalCelebrities: 150,
    activeCelebrities: 120,
    pendingVerification: 15,
    totalEarnings: 250000,
  },
  celebrities: [
    {
      id: 'CELEB-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      verificationStatus: 'verified',
      joinDate: '2024-01-15T10:00:00Z',
      lastActive: '2024-04-15T14:30:00Z',
      totalRequests: 45,
      completedRequests: 42,
      averageRating: 4.8,
      totalEarnings: 12500,
      categories: ['birthday', 'anniversary', 'graduation'],
      bio: 'Professional voice actor with 10 years of experience',
      socialMedia: {
        instagram: '@sarahsmith',
        twitter: '@sarahsmith',
        youtube: '<PERSON>',
      },
    },
    {
      id: 'CELEB-002',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'active',
      verificationStatus: 'pending',
      joinDate: '2024-02-01T09:00:00Z',
      lastActive: '2024-04-14T16:45:00Z',
      totalRequests: 30,
      completedRequests: 28,
      averageRating: 4.6,
      totalEarnings: 8500,
      categories: ['birthday', 'corporate'],
      bio: 'Former TV host and motivational speaker',
      socialMedia: {
        instagram: '@mikejohnson',
        twitter: '@mikejohnson',
      },
    },
    {
      id: 'CELEB-003',
      name: 'Emma Davis',
      email: '<EMAIL>',
      status: 'inactive',
      verificationStatus: 'verified',
      joinDate: '2024-01-20T11:00:00Z',
      lastActive: '2024-03-15T10:20:00Z',
      totalRequests: 25,
      completedRequests: 25,
      averageRating: 4.9,
      totalEarnings: 7500,
      categories: ['birthday', 'anniversary'],
      bio: 'Professional singer and performer',
      socialMedia: {
        instagram: '@emmadavis',
        youtube: 'Emma Davis Music',
      },
    },
  ],
};

export default function AdminCelebritiesPage() {
  const [celebrities, setCelebrities] = useState(mockCelebrities);
  const [selectedCelebrity, setSelectedCelebrity] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const filteredCelebrities = celebrities.celebrities.filter((celebrity) => {
    const matchesStatus =
      statusFilter === 'all' || celebrity.status === statusFilter;
    const matchesVerification =
      verificationFilter === 'all' ||
      celebrity.verificationStatus === verificationFilter;
    const matchesSearch =
      searchTerm === '' ||
      celebrity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      celebrity.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesVerification && matchesSearch;
  });

  const handleStatusChange = (celebrityId, newStatus) => {
    setCelebrities((prev) => ({
      ...prev,
      celebrities: prev.celebrities.map((celebrity) =>
        celebrity.id === celebrityId
          ? { ...celebrity, status: newStatus }
          : celebrity
      ),
    }));
  };

  const handleVerificationChange = (celebrityId, newStatus) => {
    setCelebrities((prev) => ({
      ...prev,
      celebrities: prev.celebrities.map((celebrity) =>
        celebrity.id === celebrityId
          ? { ...celebrity, verificationStatus: newStatus }
          : celebrity
      ),
    }));
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Celebrities</h1>
          <p className='mt-2 text-sm text-white'>
            Manage celebrity profiles and verification
          </p>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Celebrities
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {celebrities.overview.totalCelebrities}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Active Celebrities
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {celebrities.overview.activeCelebrities}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Pending Verification
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {celebrities.overview.pendingVerification}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Earnings
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(celebrities.overview.totalEarnings)}
              </dd>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
          <div>
            <label
              htmlFor='search'
              className='block text-sm font-medium text-white'
            >
              Search
            </label>
            <input
              type='text'
              id='search'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder='Search by name or email...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='verification'
              className='block text-sm font-medium text-white'
            >
              Verification
            </label>
            <select
              id='verification'
              value={verificationFilter}
              onChange={(e) => setVerificationFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Verification</option>
              <option value='verified'>Verified</option>
              <option value='pending'>Pending</option>
              <option value='rejected'>Rejected</option>
            </select>
          </div>
        </div>

        {/* Celebrities Table */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Celebrity
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Verification
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Performance
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Earnings
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Last Active
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredCelebrities.map((celebrity) => (
                  <tr
                    key={celebrity.id}
                    onClick={() => setSelectedCelebrity(celebrity)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {celebrity.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {celebrity.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <select
                        value={celebrity.status}
                        onChange={(e) =>
                          handleStatusChange(celebrity.id, e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          celebrity.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <select
                        value={celebrity.verificationStatus}
                        onChange={(e) =>
                          handleVerificationChange(celebrity.id, e.target.value)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          celebrity.verificationStatus === 'verified'
                            ? 'bg-green-100 text-green-800'
                            : celebrity.verificationStatus === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value='verified'>Verified</option>
                        <option value='pending'>Pending</option>
                        <option value='rejected'>Rejected</option>
                      </select>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm text-gray-900'>
                        {celebrity.completedRequests} /{' '}
                        {celebrity.totalRequests} requests
                      </div>
                      <div className='text-sm text-gray-500'>
                        Rating: {celebrity.averageRating.toFixed(1)}/5.0
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {formatCurrency(celebrity.totalEarnings)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {new Date(celebrity.lastActive).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Celebrity Details Modal */}
        {selectedCelebrity && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Celebrity Details - {selectedCelebrity.name}
                  </h3>
                  <button
                    onClick={() => setSelectedCelebrity(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>Email</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCelebrity.email}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <select
                        value={selectedCelebrity.status}
                        onChange={(e) =>
                          handleStatusChange(
                            selectedCelebrity.id,
                            e.target.value
                          )
                        }
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          selectedCelebrity.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Verification Status
                    </dt>
                    <dd className='mt-1'>
                      <select
                        value={selectedCelebrity.verificationStatus}
                        onChange={(e) =>
                          handleVerificationChange(
                            selectedCelebrity.id,
                            e.target.value
                          )
                        }
                        className={`text-sm rounded-full px-2 py-1 font-semibold ${
                          selectedCelebrity.verificationStatus === 'verified'
                            ? 'bg-green-100 text-green-800'
                            : selectedCelebrity.verificationStatus === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        <option value='verified'>Verified</option>
                        <option value='pending'>Pending</option>
                        <option value='rejected'>Rejected</option>
                      </select>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Join Date
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedCelebrity.joinDate).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Total Requests
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCelebrity.completedRequests} /{' '}
                      {selectedCelebrity.totalRequests}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Average Rating
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCelebrity.averageRating.toFixed(1)}/5.0
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Total Earnings
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedCelebrity.totalEarnings)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Categories
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCelebrity.categories.join(', ')}
                    </dd>
                  </div>
                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>Bio</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedCelebrity.bio}
                    </dd>
                  </div>
                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Social Media
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {Object.entries(selectedCelebrity.socialMedia).map(
                        ([platform, handle]) => (
                          <div key={platform} className='capitalize'>
                            {platform}: {handle}
                          </div>
                        )
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
