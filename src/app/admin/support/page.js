'use client';

import { useState } from 'react';

// Mock data for support tickets
const mockTickets = [
  {
    id: 'TICKET-001',
    subject: 'Payment Issue',
    description: 'Unable to process payment for a birthday greeting request',
    status: 'open',
    priority: 'high',
    category: 'payment',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
    },
    createdAt: '2024-04-15T10:30:00Z',
    lastUpdated: '2024-04-15T10:30:00Z',
    assignedTo: null,
    messages: [
      {
        id: 1,
        sender: 'customer',
        content: 'I tried to make a payment but received an error message.',
        timestamp: '2024-04-15T10:30:00Z',
      },
    ],
  },
  {
    id: 'TICKET-002',
    subject: 'Video Quality Issue',
    description: 'The delivered video greeting has poor audio quality',
    status: 'in_progress',
    priority: 'medium',
    category: 'technical',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
    },
    createdAt: '2024-04-14T15:45:00Z',
    lastUpdated: '2024-04-15T09:20:00Z',
    assignedTo: 'Admin User',
    messages: [
      {
        id: 1,
        sender: 'customer',
        content: 'The video greeting I received has very low audio quality.',
        timestamp: '2024-04-14T15:45:00Z',
      },
      {
        id: 2,
        sender: 'admin',
        content:
          'We are looking into the issue and will get back to you shortly.',
        timestamp: '2024-04-15T09:20:00Z',
      },
    ],
  },
  {
    id: 'TICKET-003',
    subject: 'Celebrity Verification',
    description: 'Need help with celebrity account verification process',
    status: 'resolved',
    priority: 'low',
    category: 'account',
    customer: {
      name: 'Mike Brown',
      email: '<EMAIL>',
      phone: '+****************',
    },
    createdAt: '2024-04-13T09:15:00Z',
    lastUpdated: '2024-04-14T14:30:00Z',
    assignedTo: 'Support Team',
    messages: [
      {
        id: 1,
        sender: 'customer',
        content:
          'I need help with the verification process for my celebrity account.',
        timestamp: '2024-04-13T09:15:00Z',
      },
      {
        id: 2,
        sender: 'admin',
        content: 'Please provide the required documents for verification.',
        timestamp: '2024-04-13T11:30:00Z',
      },
      {
        id: 3,
        sender: 'customer',
        content: 'I have submitted all the required documents.',
        timestamp: '2024-04-14T10:15:00Z',
      },
      {
        id: 4,
        sender: 'admin',
        content: 'Your account has been verified successfully.',
        timestamp: '2024-04-14T14:30:00Z',
      },
    ],
  },
];

export default function AdminSupportPage() {
  const [tickets, setTickets] = useState(mockTickets);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [newMessage, setNewMessage] = useState('');

  const handleStatusChange = (ticketId, newStatus) => {
    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              status: newStatus,
              lastUpdated: new Date().toISOString(),
            }
          : ticket
      )
    );
  };

  const handlePriorityChange = (ticketId, newPriority) => {
    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId ? { ...ticket, priority: newPriority } : ticket
      )
    );
  };

  const handleAssignTicket = (ticketId, assignee) => {
    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId ? { ...ticket, assignedTo: assignee } : ticket
      )
    );
  };

  const handleSendMessage = (ticketId) => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now(),
      sender: 'admin',
      content: newMessage,
      timestamp: new Date().toISOString(),
    };

    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              messages: [...ticket.messages, message],
              lastUpdated: message.timestamp,
            }
          : ticket
      )
    );

    setNewMessage('');
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesStatus =
      statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority =
      priorityFilter === 'all' || ticket.priority === priorityFilter;
    const matchesCategory =
      categoryFilter === 'all' || ticket.category === categoryFilter;
    const matchesSearch =
      searchTerm === '' ||
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesPriority && matchesCategory && matchesSearch;
  });

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Support Tickets</h1>
          <p className='mt-2 text-sm text-white'>
            Manage customer support tickets and inquiries
          </p>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
          <div>
            <label
              htmlFor='search'
              className='block text-sm font-medium text-white'
            >
              Search
            </label>
            <input
              type='text'
              id='search'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder='Search tickets...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='open'>Open</option>
              <option value='in_progress'>In Progress</option>
              <option value='resolved'>Resolved</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='priority'
              className='block text-sm font-medium text-white'
            >
              Priority
            </label>
            <select
              id='priority'
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Priorities</option>
              <option value='high'>High</option>
              <option value='medium'>Medium</option>
              <option value='low'>Low</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='category'
              className='block text-sm font-medium text-white'
            >
              Category
            </label>
            <select
              id='category'
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Categories</option>
              <option value='payment'>Payment</option>
              <option value='technical'>Technical</option>
              <option value='account'>Account</option>
            </select>
          </div>
        </div>

        {/* Tickets List */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Ticket
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Customer
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Priority
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Assigned To
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Last Updated
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredTickets.map((ticket) => (
                  <tr
                    key={ticket.id}
                    onClick={() => setSelectedTicket(ticket)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {ticket.id}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {ticket.subject}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {ticket.customer.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {ticket.customer.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          ticket.status === 'open'
                            ? 'bg-red-100 text-red-800'
                            : ticket.status === 'in_progress'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {ticket.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          ticket.priority === 'high'
                            ? 'bg-red-100 text-red-800'
                            : ticket.priority === 'medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}
                      >
                        {ticket.priority}
                      </span>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {ticket.assignedTo || 'Unassigned'}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {new Date(ticket.lastUpdated).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Ticket Details Modal */}
        {selectedTicket && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Ticket {selectedTicket.id}
                  </h3>
                  <button
                    onClick={() => setSelectedTicket(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <h4 className='text-sm font-medium text-gray-500'>
                      Subject
                    </h4>
                    <p className='mt-1 text-sm text-gray-900'>
                      {selectedTicket.subject}
                    </p>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium text-gray-500'>
                      Category
                    </h4>
                    <p className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedTicket.category}
                    </p>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium text-gray-500'>
                      Customer
                    </h4>
                    <p className='mt-1 text-sm text-gray-900'>
                      {selectedTicket.customer.name}
                    </p>
                    <p className='text-sm text-gray-500'>
                      {selectedTicket.customer.email}
                    </p>
                    <p className='text-sm text-gray-500'>
                      {selectedTicket.customer.phone}
                    </p>
                  </div>
                  <div>
                    <h4 className='text-sm font-medium text-gray-500'>
                      Description
                    </h4>
                    <p className='mt-1 text-sm text-gray-900'>
                      {selectedTicket.description}
                    </p>
                  </div>
                </div>

                <div className='mt-6'>
                  <h4 className='text-sm font-medium text-gray-500'>
                    Messages
                  </h4>
                  <div className='mt-2 space-y-4'>
                    {selectedTicket.messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.sender === 'admin'
                            ? 'justify-end'
                            : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-lg rounded-lg px-4 py-2 ${
                            message.sender === 'admin'
                              ? 'bg-indigo-100 text-indigo-900'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          <p className='text-sm'>{message.content}</p>
                          <p className='text-xs mt-1 text-gray-500'>
                            {new Date(message.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className='mt-4'>
                    <textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder='Type your message...'
                      rows={3}
                      className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    />
                    <div className='mt-2 flex justify-end'>
                      <button
                        onClick={() => handleSendMessage(selectedTicket.id)}
                        className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                      >
                        Send Message
                      </button>
                    </div>
                  </div>
                </div>

                <div className='mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3'>
                  <div>
                    <label
                      htmlFor='status'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Status
                    </label>
                    <select
                      id='status'
                      value={selectedTicket.status}
                      onChange={(e) =>
                        handleStatusChange(selectedTicket.id, e.target.value)
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    >
                      <option value='open'>Open</option>
                      <option value='in_progress'>In Progress</option>
                      <option value='resolved'>Resolved</option>
                    </select>
                  </div>
                  <div>
                    <label
                      htmlFor='priority'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Priority
                    </label>
                    <select
                      id='priority'
                      value={selectedTicket.priority}
                      onChange={(e) =>
                        handlePriorityChange(selectedTicket.id, e.target.value)
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    >
                      <option value='high'>High</option>
                      <option value='medium'>Medium</option>
                      <option value='low'>Low</option>
                    </select>
                  </div>
                  <div>
                    <label
                      htmlFor='assignee'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Assign To
                    </label>
                    <select
                      id='assignee'
                      value={selectedTicket.assignedTo || ''}
                      onChange={(e) =>
                        handleAssignTicket(selectedTicket.id, e.target.value)
                      }
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
                    >
                      <option value=''>Unassigned</option>
                      <option value='Admin User'>Admin User</option>
                      <option value='Support Team'>Support Team</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
