'use client';

import { useState } from 'react';

// Mock data for audit logs
const mockAuditLogs = [
  {
    id: 1,
    timestamp: '2024-04-15T10:30:00Z',
    user: {
      id: 'ADMIN-001',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    action: 'login',
    category: 'authentication',
    details: 'Successful login from IP ***********',
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 2,
    timestamp: '2024-04-15T10:25:00Z',
    user: {
      id: 'CELEB-123',
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    action: 'update_profile',
    category: 'profile',
    details: 'Updated profile information',
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
  },
  {
    id: 3,
    timestamp: '2024-04-15T10:20:00Z',
    user: {
      id: 'ADMIN-001',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    action: 'delete_user',
    category: 'user_management',
    details: 'Deleted user account USER-456',
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 4,
    timestamp: '2024-04-15T10:15:00Z',
    user: {
      id: 'USER-789',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
    },
    action: 'failed_login',
    category: 'authentication',
    details: 'Failed login attempt - Invalid credentials',
    status: 'error',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
  },
  {
    id: 5,
    timestamp: '2024-04-15T10:10:00Z',
    user: {
      id: 'ADMIN-001',
      name: 'Admin User',
      email: '<EMAIL>',
    },
    action: 'update_settings',
    category: 'system',
    details: 'Updated system notification settings',
    status: 'success',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
];

export default function AdminAuditPage() {
  const [logs, setLogs] = useState(mockAuditLogs);
  const [selectedLog, setSelectedLog] = useState(null);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [userFilter, setUserFilter] = useState('');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: '',
  });

  const handleDateRangeChange = (field, value) => {
    setDateRange((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const filteredLogs = logs.filter((log) => {
    const matchesCategory =
      categoryFilter === 'all' || log.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
    const matchesUser =
      userFilter === '' ||
      log.user.name.toLowerCase().includes(userFilter.toLowerCase()) ||
      log.user.email.toLowerCase().includes(userFilter.toLowerCase());
    const matchesDateRange =
      (!dateRange.start ||
        new Date(log.timestamp) >= new Date(dateRange.start)) &&
      (!dateRange.end || new Date(log.timestamp) <= new Date(dateRange.end));

    return matchesCategory && matchesStatus && matchesUser && matchesDateRange;
  });

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Audit Log</h1>
          <p className='mt-2 text-sm text-white'>
            Track system activities and user actions
          </p>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4'>
          <div>
            <label
              htmlFor='category'
              className='block text-sm font-medium text-white'
            >
              Category
            </label>
            <select
              id='category'
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Categories</option>
              <option value='authentication'>Authentication</option>
              <option value='profile'>Profile</option>
              <option value='user_management'>User Management</option>
              <option value='system'>System</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='success'>Success</option>
              <option value='error'>Error</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='user'
              className='block text-sm font-medium text-white'
            >
              User
            </label>
            <input
              type='text'
              id='user'
              value={userFilter}
              onChange={(e) => setUserFilter(e.target.value)}
              placeholder='Search by name or email...'
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            />
          </div>
          <div>
            <label
              htmlFor='date-range'
              className='block text-sm font-medium text-white'
            >
              Date Range
            </label>
            <div className='mt-1 grid grid-cols-2 gap-2'>
              <input
                type='date'
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
              />
              <input
                type='date'
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
              />
            </div>
          </div>
        </div>

        {/* Audit Logs Table */}
        <div className='bg-white shadow rounded-lg'>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Timestamp
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    User
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Action
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Category
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredLogs.map((log) => (
                  <tr
                    key={log.id}
                    onClick={() => setSelectedLog(log)}
                    className='hover:bg-gray-50 cursor-pointer'
                  >
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {new Date(log.timestamp).toLocaleString()}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {log.user.name}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {log.user.email}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {log.action.replace('_', ' ')}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize'>
                      {log.category.replace('_', ' ')}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          log.status === 'success'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {log.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Log Details Modal */}
        {selectedLog && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Log Details - {selectedLog.action.replace('_', ' ')}
                  </h3>
                  <button
                    onClick={() => setSelectedLog(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Timestamp
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedLog.timestamp).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          selectedLog.status === 'success'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {selectedLog.status}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>User</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedLog.user.name} ({selectedLog.user.email})
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Category
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedLog.category.replace('_', ' ')}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      IP Address
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedLog.ipAddress}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      User Agent
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedLog.userAgent}
                    </dd>
                  </div>
                  <div className='sm:col-span-2'>
                    <dt className='text-sm font-medium text-gray-500'>
                      Details
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedLog.details}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
