'use client';

import { useState } from 'react';

// Mock data for payments
const mockPayments = {
  overview: {
    totalRevenue: 125000,
    pendingPayouts: 45000,
    completedPayouts: 80000,
    averageTransaction: 250,
  },
  transactions: [
    {
      id: 'TRX-001',
      date: '2024-04-15T10:30:00Z',
      customer: {
        id: 'USER-123',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-456',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      amount: 300,
      status: 'completed',
      type: 'greeting',
      paymentMethod: 'credit_card',
      payoutStatus: 'paid',
      payoutDate: '2024-04-16T09:00:00Z',
    },
    {
      id: 'TRX-002',
      date: '2024-04-15T09:15:00Z',
      customer: {
        id: 'USER-456',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-789',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      amount: 250,
      status: 'completed',
      type: 'greeting',
      paymentMethod: 'paypal',
      payoutStatus: 'pending',
      payoutDate: null,
    },
    {
      id: 'TRX-003',
      date: '2024-04-14T16:45:00Z',
      customer: {
        id: 'USER-789',
        name: 'Robert Brown',
        email: '<EMAIL>',
      },
      celebrity: {
        id: 'CELEB-123',
        name: 'Emma <PERSON>',
        email: '<EMAIL>',
      },
      amount: 400,
      status: 'refunded',
      type: 'greeting',
      paymentMethod: 'credit_card',
      payoutStatus: 'reversed',
      payoutDate: '2024-04-15T11:20:00Z',
    },
  ],
  payouts: [
    {
      id: 'PAY-001',
      date: '2024-04-16T09:00:00Z',
      celebrity: {
        id: 'CELEB-456',
        name: 'Sarah Smith',
        email: '<EMAIL>',
      },
      amount: 270,
      status: 'completed',
      method: 'bank_transfer',
      reference: 'BANK-REF-123',
    },
    {
      id: 'PAY-002',
      date: '2024-04-16T08:30:00Z',
      celebrity: {
        id: 'CELEB-789',
        name: 'Mike Johnson',
        email: '<EMAIL>',
      },
      amount: 225,
      status: 'pending',
      method: 'paypal',
      reference: null,
    },
  ],
};

export default function AdminPaymentsPage() {
  const [payments, setPayments] = useState(mockPayments);
  const [activeTab, setActiveTab] = useState('transactions');
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [selectedPayout, setSelectedPayout] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: '',
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleDateRangeChange = (field, value) => {
    setDateRange((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const filteredTransactions = payments.transactions.filter((transaction) => {
    const matchesStatus =
      statusFilter === 'all' || transaction.status === statusFilter;
    const matchesDateRange =
      (!dateRange.start ||
        new Date(transaction.date) >= new Date(dateRange.start)) &&
      (!dateRange.end || new Date(transaction.date) <= new Date(dateRange.end));

    return matchesStatus && matchesDateRange;
  });

  const filteredPayouts = payments.payouts.filter((payout) => {
    const matchesStatus =
      statusFilter === 'all' || payout.status === statusFilter;
    const matchesDateRange =
      (!dateRange.start ||
        new Date(payout.date) >= new Date(dateRange.start)) &&
      (!dateRange.end || new Date(payout.date) <= new Date(dateRange.end));

    return matchesStatus && matchesDateRange;
  });

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Payments</h1>
          <p className='mt-2 text-sm text-white'>
            Manage transactions and payouts
          </p>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Revenue
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(payments.overview.totalRevenue)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Pending Payouts
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(payments.overview.pendingPayouts)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Completed Payouts
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(payments.overview.completedPayouts)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Average Transaction
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(payments.overview.averageTransaction)}
              </dd>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className='mb-6'>
          <div className='sm:hidden'>
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='transactions'>Transactions</option>
              <option value='payouts'>Payouts</option>
            </select>
          </div>
          <div className='hidden sm:block'>
            <div className='border-b border-gray-200'>
              <nav className='-mb-px flex space-x-8'>
                {['transactions', 'payouts'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`${
                      activeTab === tab
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-white'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize cursor-pointer`}
                  >
                    {tab}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className='mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
          <div>
            <label
              htmlFor='status'
              className='block text-sm font-medium text-white'
            >
              Status
            </label>
            <select
              id='status'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Status</option>
              <option value='completed'>Completed</option>
              <option value='pending'>Pending</option>
              <option value='refunded'>Refunded</option>
            </select>
          </div>
          <div>
            <label
              htmlFor='date-range'
              className='block text-sm font-medium text-white'
            >
              Date Range
            </label>
            <div className='mt-1 grid grid-cols-2 gap-2'>
              <input
                type='date'
                value={dateRange.start}
                onChange={(e) => handleDateRangeChange('start', e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
              />
              <input
                type='date'
                value={dateRange.end}
                onChange={(e) => handleDateRangeChange('end', e.target.value)}
                className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
              />
            </div>
          </div>
        </div>

        {/* Transactions Table */}
        {activeTab === 'transactions' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='overflow-x-auto'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Date
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Customer
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Celebrity
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Amount
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Payout Status
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {filteredTransactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      onClick={() => setSelectedTransaction(transaction)}
                      className='hover:bg-gray-50 cursor-pointer'
                    >
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {new Date(transaction.date).toLocaleString()}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>
                          {transaction.customer.name}
                        </div>
                        <div className='text-sm text-gray-500'>
                          {transaction.customer.email}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>
                          {transaction.celebrity.name}
                        </div>
                        <div className='text-sm text-gray-500'>
                          {transaction.celebrity.email}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {formatCurrency(transaction.amount)}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            transaction.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : transaction.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {transaction.status}
                        </span>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            transaction.payoutStatus === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : transaction.payoutStatus === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {transaction.payoutStatus}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Payouts Table */}
        {activeTab === 'payouts' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='overflow-x-auto'>
              <table className='min-w-full divide-y divide-gray-200'>
                <thead>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Date
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Celebrity
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Amount
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Method
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Reference
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {filteredPayouts.map((payout) => (
                    <tr
                      key={payout.id}
                      onClick={() => setSelectedPayout(payout)}
                      className='hover:bg-gray-50 cursor-pointer'
                    >
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {new Date(payout.date).toLocaleString()}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>
                          {payout.celebrity.name}
                        </div>
                        <div className='text-sm text-gray-500'>
                          {payout.celebrity.email}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {formatCurrency(payout.amount)}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            payout.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {payout.status}
                        </span>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize'>
                        {payout.method.replace('_', ' ')}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {payout.reference || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Transaction Details Modal */}
        {selectedTransaction && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Transaction Details - {selectedTransaction.id}
                  </h3>
                  <button
                    onClick={() => setSelectedTransaction(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>Date</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedTransaction.date).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Amount
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedTransaction.amount)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          selectedTransaction.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : selectedTransaction.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {selectedTransaction.status}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>Type</dt>
                    <dd className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedTransaction.type}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Payment Method
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedTransaction.paymentMethod.replace('_', ' ')}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Payout Status
                    </dt>
                    <dd className='mt-1'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          selectedTransaction.payoutStatus === 'paid'
                            ? 'bg-green-100 text-green-800'
                            : selectedTransaction.payoutStatus === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {selectedTransaction.payoutStatus}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Customer
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedTransaction.customer.name} (
                      {selectedTransaction.customer.email})
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Celebrity
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedTransaction.celebrity.name} (
                      {selectedTransaction.celebrity.email})
                    </dd>
                  </div>
                  {selectedTransaction.payoutDate && (
                    <div>
                      <dt className='text-sm font-medium text-gray-500'>
                        Payout Date
                      </dt>
                      <dd className='mt-1 text-sm text-gray-900'>
                        {new Date(
                          selectedTransaction.payoutDate
                        ).toLocaleString()}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          </div>
        )}

        {/* Payout Details Modal */}
        {selectedPayout && (
          <div className='fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4'>
            <div className='bg-white rounded-lg shadow-xl max-w-2xl w-full'>
              <div className='px-6 py-4 border-b border-gray-200'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Payout Details - {selectedPayout.id}
                  </h3>
                  <button
                    onClick={() => setSelectedPayout(null)}
                    className='text-gray-400 hover:text-gray-500'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <div className='px-6 py-4'>
                <dl className='grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2'>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>Date</dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {new Date(selectedPayout.date).toLocaleString()}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Amount
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {formatCurrency(selectedPayout.amount)}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Status
                    </dt>
                    <dd className='mt-1'>
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          selectedPayout.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {selectedPayout.status}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Method
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900 capitalize'>
                      {selectedPayout.method.replace('_', ' ')}
                    </dd>
                  </div>
                  <div>
                    <dt className='text-sm font-medium text-gray-500'>
                      Celebrity
                    </dt>
                    <dd className='mt-1 text-sm text-gray-900'>
                      {selectedPayout.celebrity.name} (
                      {selectedPayout.celebrity.email})
                    </dd>
                  </div>
                  {selectedPayout.reference && (
                    <div>
                      <dt className='text-sm font-medium text-gray-500'>
                        Reference
                      </dt>
                      <dd className='mt-1 text-sm text-gray-900'>
                        {selectedPayout.reference}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
