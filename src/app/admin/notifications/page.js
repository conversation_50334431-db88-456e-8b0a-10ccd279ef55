'use client';

import { useState } from 'react';

// Mock data for notifications
const mockNotifications = {
  system: [
    {
      id: 1,
      type: 'error',
      title: 'Payment Gateway Error',
      message: 'Stripe API connection failed. Please check API credentials.',
      timestamp: '2024-04-15T10:30:00Z',
      read: false,
    },
    {
      id: 2,
      type: 'warning',
      title: 'High Server Load',
      message: 'Server CPU usage is above 80%. Consider scaling resources.',
      timestamp: '2024-04-15T09:15:00Z',
      read: true,
    },
    {
      id: 3,
      type: 'info',
      title: 'System Update Available',
      message: 'New version 2.1.0 is available for deployment.',
      timestamp: '2024-04-14T16:45:00Z',
      read: false,
    },
  ],
  user: [
    {
      id: 4,
      type: 'success',
      title: 'New Celebrity Signup',
      message: '<PERSON> has completed the verification process.',
      timestamp: '2024-04-15T11:20:00Z',
      read: false,
    },
    {
      id: 5,
      type: 'warning',
      title: 'Suspicious Activity',
      message: 'Multiple failed login attempts detected for user ID: 12345.',
      timestamp: '2024-04-15T08:30:00Z',
      read: true,
    },
    {
      id: 6,
      type: 'info',
      title: 'New Support Ticket',
      message: 'High priority ticket #TICKET-001 has been created.',
      timestamp: '2024-04-14T14:15:00Z',
      read: false,
    },
  ],
  preferences: {
    email: {
      systemAlerts: true,
      userActivity: true,
      securityAlerts: true,
      marketingUpdates: false,
    },
    push: {
      systemAlerts: true,
      userActivity: true,
      securityAlerts: true,
      marketingUpdates: false,
    },
    inApp: {
      systemAlerts: true,
      userActivity: true,
      securityAlerts: true,
      marketingUpdates: false,
    },
  },
};

export default function AdminNotificationsPage() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [activeTab, setActiveTab] = useState('all');
  const [preferences, setPreferences] = useState(mockNotifications.preferences);

  const handleMarkAsRead = (notificationId) => {
    setNotifications((prev) => ({
      ...prev,
      system: prev.system.map((n) =>
        n.id === notificationId ? { ...n, read: true } : n
      ),
      user: prev.user.map((n) =>
        n.id === notificationId ? { ...n, read: true } : n
      ),
    }));
  };

  const handleMarkAllAsRead = () => {
    setNotifications((prev) => ({
      ...prev,
      system: prev.system.map((n) => ({ ...n, read: true })),
      user: prev.user.map((n) => ({ ...n, read: true })),
    }));
  };

  const handleDeleteNotification = (notificationId) => {
    setNotifications((prev) => ({
      ...prev,
      system: prev.system.filter((n) => n.id !== notificationId),
      user: prev.user.filter((n) => n.id !== notificationId),
    }));
  };

  const handlePreferenceChange = (channel, type, value) => {
    setPreferences((prev) => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [type]: value,
      },
    }));
  };

  const filteredNotifications = [
    ...(activeTab === 'all' || activeTab === 'system'
      ? notifications.system
      : []),
    ...(activeTab === 'all' || activeTab === 'user' ? notifications.user : []),
  ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  const unreadCount = filteredNotifications.filter((n) => !n.read).length;

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Notifications</h1>
          <p className='mt-2 text-sm text-white'>
            Manage system notifications and alerts
          </p>
        </div>

        {/* Tabs */}
        <div className='mb-6'>
          <div className='sm:hidden'>
            <select
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
              className='block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='all'>All Notifications</option>
              <option value='system'>System Alerts</option>
              <option value='user'>User Activity</option>
              <option value='preferences'>Preferences</option>
            </select>
          </div>
          <div className='hidden sm:block'>
            <div className='border-b border-gray-200'>
              <nav className='-mb-px flex space-x-8'>
                {['all', 'system', 'user', 'preferences'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`${
                      activeTab === tab
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-white'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize cursor-pointer`}
                  >
                    {tab}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        {activeTab !== 'preferences' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:px-6 flex justify-between items-center'>
              <h3 className='text-lg font-medium text-gray-900'>
                {unreadCount} unread notifications
              </h3>
              <button
                onClick={handleMarkAllAsRead}
                className='inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
              >
                Mark all as read
              </button>
            </div>
            <div className='border-t border-gray-200'>
              <ul className='divide-y divide-gray-200'>
                {filteredNotifications.map((notification) => (
                  <li key={notification.id} className='px-4 py-4 sm:px-6'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center'>
                        <div
                          className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${
                            notification.type === 'error'
                              ? 'bg-red-100'
                              : notification.type === 'warning'
                              ? 'bg-yellow-100'
                              : notification.type === 'success'
                              ? 'bg-green-100'
                              : 'bg-blue-100'
                          }`}
                        >
                          {notification.type === 'error' && (
                            <svg
                              className='h-5 w-5 text-red-600'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
                              />
                            </svg>
                          )}
                          {notification.type === 'warning' && (
                            <svg
                              className='h-5 w-5 text-yellow-600'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
                              />
                            </svg>
                          )}
                          {notification.type === 'success' && (
                            <svg
                              className='h-5 w-5 text-green-600'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M5 13l4 4L19 7'
                              />
                            </svg>
                          )}
                          {notification.type === 'info' && (
                            <svg
                              className='h-5 w-5 text-blue-600'
                              fill='none'
                              viewBox='0 0 24 24'
                              stroke='currentColor'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                              />
                            </svg>
                          )}
                        </div>
                        <div className='ml-3'>
                          <p
                            className={`text-sm font-medium ${
                              notification.read
                                ? 'text-gray-500'
                                : 'text-gray-900'
                            }`}
                          >
                            {notification.title}
                          </p>
                          <p className='text-sm text-gray-500'>
                            {notification.message}
                          </p>
                          <p className='text-xs text-gray-400 mt-1'>
                            {new Date(notification.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className='flex space-x-2'>
                        {!notification.read && (
                          <button
                            onClick={() => handleMarkAsRead(notification.id)}
                            className='text-sm text-indigo-600 hover:text-indigo-900'
                          >
                            Mark as read
                          </button>
                        )}
                        <button
                          onClick={() =>
                            handleDeleteNotification(notification.id)
                          }
                          className='text-sm text-red-600 hover:text-red-900'
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Notification Preferences */}
        {activeTab === 'preferences' && (
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900'>
                Notification Preferences
              </h3>
              <div className='mt-6 space-y-6'>
                {Object.entries(preferences).map(([channel, settings]) => (
                  <div key={channel} className='border-b border-gray-200 pb-6'>
                    <h4 className='text-sm font-medium text-gray-900 capitalize mb-4'>
                      {channel} Notifications
                    </h4>
                    <div className='space-y-4'>
                      {Object.entries(settings).map(([type, enabled]) => (
                        <div
                          key={type}
                          className='flex items-center justify-between'
                        >
                          <div>
                            <label
                              htmlFor={`${channel}-${type}`}
                              className='text-sm font-medium text-gray-700 capitalize'
                            >
                              {type.replace(/([A-Z])/g, ' $1').trim()}
                            </label>
                            <p className='text-sm text-gray-500'>
                              Receive{' '}
                              {type
                                .replace(/([A-Z])/g, ' $1')
                                .trim()
                                .toLowerCase()}{' '}
                              notifications via {channel}
                            </p>
                          </div>
                          <div className='flex items-center'>
                            <button
                              type='button'
                              onClick={() =>
                                handlePreferenceChange(channel, type, !enabled)
                              }
                              className={`${
                                enabled ? 'bg-indigo-600' : 'bg-gray-200'
                              } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2`}
                            >
                              <span
                                className={`${
                                  enabled ? 'translate-x-5' : 'translate-x-0'
                                } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
                              />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
