'use client';

import { useState } from 'react';

// Mock data for admin profile
const mockAdminProfile = {
  id: 'ADM-001',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'Administrator',
  avatar:
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  phone: '+****************',
  department: 'Platform Management',
  joinDate: '2023-01-15',
  lastActive: '2024-03-15 14:30:00',
  preferences: {
    notifications: {
      email: true,
      push: true,
      inApp: true,
    },
    theme: 'light',
    language: 'en',
    timezone: 'UTC-5',
  },
  security: {
    twoFactorEnabled: true,
    lastPasswordChange: '2024-02-01',
    loginHistory: [
      {
        date: '2024-03-15 14:30:00',
        ip: '***********',
        device: 'Chrome on Windows',
        location: 'New York, USA',
      },
      {
        date: '2024-03-14 09:15:00',
        ip: '***********',
        device: 'Chrome on Windows',
        location: 'New York, USA',
      },
    ],
  },
};

export default function AdminProfilePage() {
  const [profile, setProfile] = useState(mockAdminProfile);
  const [activeTab, setActiveTab] = useState('general');
  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState(null);

  const handleEdit = () => {
    setEditedProfile({ ...profile });
    setIsEditing(true);
  };

  const handleSave = () => {
    setProfile(editedProfile);
    setIsEditing(false);
    setEditedProfile(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedProfile(null);
  };

  const handlePreferenceChange = (category, setting, value) => {
    setEditedProfile((prev) => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [category]: {
          ...prev.preferences[category],
          [setting]: value,
        },
      },
    }));
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-white'>Profile Settings</h1>
          <p className='mt-2 text-sm text-white'>
            Manage your account settings and preferences
          </p>
        </div>

        <div className='bg-white shadow rounded-lg'>
          <div className='px-4 py-5 sm:p-6'>
            {/* Profile Header */}
            <div className='flex items-center space-x-4 mb-8'>
              <img
                src={profile.avatar}
                alt={profile.name}
                className='h-16 w-16 rounded-full'
              />
              <div>
                <h2 className='text-xl font-semibold text-gray-900'>
                  {profile.name}
                </h2>
                <p className='text-sm text-gray-500'>{profile.role}</p>
              </div>
              {!isEditing && (
                <button
                  onClick={handleEdit}
                  className='ml-auto inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700'
                >
                  Edit Profile
                </button>
              )}
            </div>

            {/* Tab Navigation */}
            <div className='border-b border-gray-200 mb-6'>
              <nav className='-mb-px flex space-x-8'>
                <button
                  onClick={() => setActiveTab('general')}
                  className={`${
                    activeTab === 'general'
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  General
                </button>
                <button
                  onClick={() => setActiveTab('preferences')}
                  className={`${
                    activeTab === 'preferences'
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Preferences
                </button>
                <button
                  onClick={() => setActiveTab('security')}
                  className={`${
                    activeTab === 'security'
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Security
                </button>
              </nav>
            </div>

            {/* General Information */}
            {activeTab === 'general' && (
              <div className='space-y-6'>
                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <label
                      htmlFor='name'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Full Name
                    </label>
                    <input
                      type='text'
                      id='name'
                      value={isEditing ? editedProfile.name : profile.name}
                      onChange={(e) =>
                        setEditedProfile({
                          ...editedProfile,
                          name: e.target.value,
                        })
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label
                      htmlFor='email'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Email
                    </label>
                    <input
                      type='email'
                      id='email'
                      value={isEditing ? editedProfile.email : profile.email}
                      onChange={(e) =>
                        setEditedProfile({
                          ...editedProfile,
                          email: e.target.value,
                        })
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label
                      htmlFor='phone'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Phone
                    </label>
                    <input
                      type='tel'
                      id='phone'
                      value={isEditing ? editedProfile.phone : profile.phone}
                      onChange={(e) =>
                        setEditedProfile({
                          ...editedProfile,
                          phone: e.target.value,
                        })
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                  <div>
                    <label
                      htmlFor='department'
                      className='block text-sm font-medium text-gray-700'
                    >
                      Department
                    </label>
                    <input
                      type='text'
                      id='department'
                      value={
                        isEditing
                          ? editedProfile.department
                          : profile.department
                      }
                      onChange={(e) =>
                        setEditedProfile({
                          ...editedProfile,
                          department: e.target.value,
                        })
                      }
                      disabled={!isEditing}
                      className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                    />
                  </div>
                </div>
                <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Join Date
                    </label>
                    <p className='mt-1 text-sm text-gray-900'>
                      {new Date(profile.joinDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Last Active
                    </label>
                    <p className='mt-1 text-sm text-gray-900'>
                      {new Date(profile.lastActive).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Preferences */}
            {activeTab === 'preferences' && (
              <div className='space-y-6'>
                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Notification Settings
                  </h3>
                  <div className='space-y-4'>
                    <div className='flex items-center'>
                      <input
                        type='checkbox'
                        id='email-notifications'
                        checked={
                          isEditing
                            ? editedProfile.preferences.notifications.email
                            : profile.preferences.notifications.email
                        }
                        onChange={(e) =>
                          handlePreferenceChange(
                            'notifications',
                            'email',
                            e.target.checked
                          )
                        }
                        disabled={!isEditing}
                        className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                      />
                      <label
                        htmlFor='email-notifications'
                        className='ml-3 text-sm text-gray-700'
                      >
                        Email Notifications
                      </label>
                    </div>
                    <div className='flex items-center'>
                      <input
                        type='checkbox'
                        id='push-notifications'
                        checked={
                          isEditing
                            ? editedProfile.preferences.notifications.push
                            : profile.preferences.notifications.push
                        }
                        onChange={(e) =>
                          handlePreferenceChange(
                            'notifications',
                            'push',
                            e.target.checked
                          )
                        }
                        disabled={!isEditing}
                        className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                      />
                      <label
                        htmlFor='push-notifications'
                        className='ml-3 text-sm text-gray-700'
                      >
                        Push Notifications
                      </label>
                    </div>
                    <div className='flex items-center'>
                      <input
                        type='checkbox'
                        id='inapp-notifications'
                        checked={
                          isEditing
                            ? editedProfile.preferences.notifications.inApp
                            : profile.preferences.notifications.inApp
                        }
                        onChange={(e) =>
                          handlePreferenceChange(
                            'notifications',
                            'inApp',
                            e.target.checked
                          )
                        }
                        disabled={!isEditing}
                        className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'
                      />
                      <label
                        htmlFor='inapp-notifications'
                        className='ml-3 text-sm text-gray-700'
                      >
                        In-App Notifications
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Display Settings
                  </h3>
                  <div className='grid grid-cols-1 gap-6 sm:grid-cols-2'>
                    <div>
                      <label
                        htmlFor='theme'
                        className='block text-sm font-medium text-gray-700'
                      >
                        Theme
                      </label>
                      <select
                        id='theme'
                        value={
                          isEditing
                            ? editedProfile.preferences.theme
                            : profile.preferences.theme
                        }
                        onChange={(e) =>
                          setEditedProfile({
                            ...editedProfile,
                            preferences: {
                              ...editedProfile.preferences,
                              theme: e.target.value,
                            },
                          })
                        }
                        disabled={!isEditing}
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                      >
                        <option value='light'>Light</option>
                        <option value='dark'>Dark</option>
                        <option value='system'>System</option>
                      </select>
                    </div>
                    <div>
                      <label
                        htmlFor='language'
                        className='block text-sm font-medium text-gray-700'
                      >
                        Language
                      </label>
                      <select
                        id='language'
                        value={
                          isEditing
                            ? editedProfile.preferences.language
                            : profile.preferences.language
                        }
                        onChange={(e) =>
                          setEditedProfile({
                            ...editedProfile,
                            preferences: {
                              ...editedProfile.preferences,
                              language: e.target.value,
                            },
                          })
                        }
                        disabled={!isEditing}
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm disabled:bg-gray-100 text-black'
                      >
                        <option value='en'>English</option>
                        <option value='es'>Spanish</option>
                        <option value='fr'>French</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Security */}
            {activeTab === 'security' && (
              <div className='space-y-6'>
                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Two-Factor Authentication
                  </h3>
                  <div className='flex items-center'>
                    <input
                      type='checkbox'
                      id='2fa'
                      checked={profile.security.twoFactorEnabled}
                      onChange={(e) =>
                        setEditedProfile({
                          ...editedProfile,
                          security: {
                            ...editedProfile.security,
                            twoFactorEnabled: e.target.checked,
                          },
                        })
                      }
                      disabled={!isEditing}
                      className='h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded text-black'
                    />
                    <label htmlFor='2fa' className='ml-3 text-sm text-gray-700'>
                      Enable Two-Factor Authentication
                    </label>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Password
                  </h3>
                  <div className='space-y-4'>
                    <div>
                      <label
                        htmlFor='current-password'
                        className='block text-sm font-medium text-gray-700'
                      >
                        Current Password
                      </label>
                      <input
                        type='password'
                        id='current-password'
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-black'
                      />
                    </div>
                    <div>
                      <label
                        htmlFor='new-password'
                        className='block text-sm font-medium text-gray-700'
                      >
                        New Password
                      </label>
                      <input
                        type='password'
                        id='new-password'
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-black'
                      />
                    </div>
                    <div>
                      <label
                        htmlFor='confirm-password'
                        className='block text-sm font-medium text-gray-700'
                      >
                        Confirm New Password
                      </label>
                      <input
                        type='password'
                        id='confirm-password'
                        className='mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-black'
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-medium text-gray-900 mb-4'>
                    Recent Login Activity
                  </h3>
                  <div className='space-y-4'>
                    {profile.security.loginHistory.map((login, index) => (
                      <div
                        key={index}
                        className='flex items-center justify-between text-sm'
                      >
                        <div>
                          <p className='font-medium text-gray-900'>
                            {new Date(login.date).toLocaleString()}
                          </p>
                          <p className='text-gray-500'>
                            {login.device} • {login.location}
                          </p>
                        </div>
                        <p className='text-gray-500'>{login.ip}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {isEditing && (
              <div className='mt-6 flex justify-end space-x-3'>
                <button
                  onClick={handleCancel}
                  className='inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
                >
                  Save Changes
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
