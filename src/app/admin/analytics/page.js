'use client';

import { Loader, LoaderVariants } from '@/components/common/Loader';
import useAuthGuard from '@/hooks/useAuthGuard';
import { useState } from 'react';

// Mock data for dashboard
const mockDashboardData = {
  overview: {
    totalRevenue: 150000,
    totalOrders: 1200,
    activeUsers: 850,
    pendingRequests: 45,
  },
  recentActivity: {
    orders: [
      {
        id: 'ORD-001',
        customer: '<PERSON>',
        celebrity: '<PERSON>',
        type: 'Birthday',
        amount: 250,
        status: 'completed',
        date: '2024-03-15 14:30:00',
      },
      {
        id: 'ORD-002',
        customer: '<PERSON>',
        celebrity: '<PERSON>',
        type: 'Anniversary',
        amount: 300,
        status: 'pending',
        date: '2024-03-15 13:45:00',
      },
      {
        id: 'ORD-003',
        customer: '<PERSON>',
        celebrity: '<PERSON>',
        type: 'Graduation',
        amount: 200,
        status: 'processing',
        date: '2024-03-15 12:15:00',
      },
    ],
    users: [
      {
        id: 'USR-001',
        name: '<PERSON>',
        type: 'Customer',
        action: 'registered',
        date: '2024-03-15 15:20:00',
      },
      {
        id: 'USR-002',
        name: '<PERSON>',
        type: 'Celebrity',
        action: 'verified',
        date: '2024-03-15 14:45:00',
      },
      {
        id: 'USR-003',
        name: 'Tom Anderson',
        type: 'Customer',
        action: 'updated_profile',
        date: '2024-03-15 13:30:00',
      },
    ],
  },
  systemStatus: {
    uptime: '99.9%',
    responseTime: '1.2s',
    activeSessions: 450,
    errorRate: '0.1%',
    alerts: [
      {
        type: 'warning',
        message: 'High server load detected',
        timestamp: '2024-03-15 14:00:00',
      },
      {
        type: 'info',
        message: 'System backup completed',
        timestamp: '2024-03-15 13:00:00',
      },
    ],
  },
  quickStats: {
    revenueByCategory: [
      { category: 'Birthday', amount: 60000 },
      { category: 'Anniversary', amount: 45000 },
      { category: 'Graduation', amount: 25000 },
      { category: 'Corporate', amount: 20000 },
    ],
    userDistribution: [
      { type: 'Customers', count: 1200 },
      { type: 'Celebrities', count: 150 },
      { type: 'Admins', count: 5 },
    ],
  },
};

export default function AdminDashboardPage() {
  const [timeRange, setTimeRange] = useState('today');
  const isLoading = useAuthGuard();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number);
  };

  if (isLoading) {
    return (
      <div className='flex justify-center items-center h-screen'>
        <Loader variant={LoaderVariants.rotatingLines} size={48} />
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div className='w-full'>
        <div className='mb-5'>
          <h1 className='text-3xl font-bold text-white'>Dashboard</h1>
          <p className='mt-2 text-sm text-white'>
            Platform overview and key metrics
          </p>
        </div>

        {/* Time Range Selector */}
        <div className='mb-6'>
          <div className='flex items-center space-x-4'>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className='rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm'
            >
              <option value='today'>Today</option>
              <option value='week'>This Week</option>
              <option value='month'>This Month</option>
              <option value='year'>This Year</option>
            </select>
          </div>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8'>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Revenue
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatCurrency(mockDashboardData.overview.totalRevenue)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Total Orders
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatNumber(mockDashboardData.overview.totalOrders)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Active Users
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatNumber(mockDashboardData.overview.activeUsers)}
              </dd>
            </div>
          </div>
          <div className='bg-white overflow-hidden shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <dt className='text-sm font-medium text-gray-500 truncate'>
                Pending Requests
              </dt>
              <dd className='mt-1 text-3xl font-semibold text-gray-900'>
                {formatNumber(mockDashboardData.overview.pendingRequests)}
              </dd>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
          {/* Recent Orders */}
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Recent Orders
              </h3>
              <div className='space-y-4'>
                {mockDashboardData.recentActivity.orders.map((order) => (
                  <div
                    key={order.id}
                    className='flex items-center justify-between'
                  >
                    <div>
                      <p className='text-sm font-medium text-gray-900'>
                        {order.customer} → {order.celebrity}
                      </p>
                      <p className='text-sm text-gray-500'>{order.type}</p>
                    </div>
                    <div className='text-right'>
                      <p className='text-sm font-medium text-gray-900'>
                        {formatCurrency(order.amount)}
                      </p>
                      <p
                        className={`text-sm ${
                          order.status === 'completed'
                            ? 'text-green-600'
                            : order.status === 'pending'
                            ? 'text-yellow-600'
                            : 'text-blue-600'
                        }`}
                      >
                        {order.status.charAt(0).toUpperCase() +
                          order.status.slice(1)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Recent User Activity */}
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Recent User Activity
              </h3>
              <div className='space-y-4'>
                {mockDashboardData.recentActivity.users.map((user) => (
                  <div
                    key={user.id}
                    className='flex items-center justify-between'
                  >
                    <div>
                      <p className='text-sm font-medium text-gray-900'>
                        {user.name}
                      </p>
                      <p className='text-sm text-gray-500'>{user.type}</p>
                    </div>
                    <div className='text-right'>
                      <p className='text-sm font-medium text-gray-900'>
                        {user.action
                          .split('_')
                          .map(
                            (word) =>
                              word.charAt(0).toUpperCase() + word.slice(1)
                          )
                          .join(' ')}
                      </p>
                      <p className='text-sm text-gray-500'>
                        {new Date(user.date).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                System Status
              </h3>
              <div className='grid grid-cols-2 gap-4 mb-6'>
                <div>
                  <p className='text-sm text-gray-500'>Uptime</p>
                  <p className='text-lg font-semibold text-gray-900'>
                    {mockDashboardData.systemStatus.uptime}
                  </p>
                </div>
                <div>
                  <p className='text-sm text-gray-500'>Response Time</p>
                  <p className='text-lg font-semibold text-gray-900'>
                    {mockDashboardData.systemStatus.responseTime}
                  </p>
                </div>
                <div>
                  <p className='text-sm text-gray-500'>Active Sessions</p>
                  <p className='text-lg font-semibold text-gray-900'>
                    {formatNumber(
                      mockDashboardData.systemStatus.activeSessions
                    )}
                  </p>
                </div>
                <div>
                  <p className='text-sm text-gray-500'>Error Rate</p>
                  <p className='text-lg font-semibold text-gray-900'>
                    {mockDashboardData.systemStatus.errorRate}
                  </p>
                </div>
              </div>
              <div className='space-y-3'>
                {mockDashboardData.systemStatus.alerts.map((alert, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-md ${
                      alert.type === 'warning'
                        ? 'bg-yellow-50 text-yellow-800'
                        : 'bg-blue-50 text-blue-800'
                    }`}
                  >
                    <p className='text-sm'>{alert.message}</p>
                    <p className='text-xs mt-1'>
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className='bg-white shadow rounded-lg'>
            <div className='px-4 py-5 sm:p-6'>
              <h3 className='text-lg font-medium text-gray-900 mb-4'>
                Quick Stats
              </h3>
              <div className='space-y-6'>
                <div>
                  <h4 className='text-sm font-medium text-gray-500 mb-3'>
                    Revenue by Category
                  </h4>
                  <div className='space-y-3'>
                    {mockDashboardData.quickStats.revenueByCategory.map(
                      (item) => (
                        <div key={item.category}>
                          <div className='flex items-center justify-between mb-1'>
                            <span className='text-sm text-gray-700'>
                              {item.category}
                            </span>
                            <span className='text-sm text-gray-500'>
                              {formatCurrency(item.amount)}
                            </span>
                          </div>
                          <div className='w-full bg-gray-200 rounded-full h-2'>
                            <div
                              className='bg-indigo-600 h-2 rounded-full'
                              style={{
                                width: `${(item.amount / 60000) * 100}%`,
                              }}
                            />
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-500 mb-3'>
                    User Distribution
                  </h4>
                  <div className='space-y-3'>
                    {mockDashboardData.quickStats.userDistribution.map(
                      (item) => (
                        <div key={item.type}>
                          <div className='flex items-center justify-between mb-1'>
                            <span className='text-sm text-gray-700'>
                              {item.type}
                            </span>
                            <span className='text-sm text-gray-500'>
                              {formatNumber(item.count)}
                            </span>
                          </div>
                          <div className='w-full bg-gray-200 rounded-full h-2'>
                            <div
                              className='bg-green-600 h-2 rounded-full'
                              style={{
                                width: `${(item.count / 1200) * 100}%`,
                              }}
                            />
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
