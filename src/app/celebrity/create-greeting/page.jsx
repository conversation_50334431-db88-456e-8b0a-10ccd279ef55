"use client"
import CongratsGreeting from '@/components/celebrity/CongratsGreeting';
import Projects from '@/components/celebrity/Projects';
import Requests from '@/components/celebrity/Requests';
import Uploads from '@/components/celebrity/Uploads';
import React, { useState } from 'react';

const TABS = ["Uploads", "Requests", "Projects"];

export default function createGreeting() {
  const [activeTab, setActiveTab] = useState("Requests");
  const [expandedId, setExpandedId] = useState(null);
  const [expandedProjectId, setExpandedProjectId] = useState(null);
  const [showSubmitted, setShowSubmitted] = useState(false);

  if (showSubmitted) {
    return (
      <CongratsGreeting setShowSubmitted={setShowSubmitted} />
    );
  }

  return (
    <div className="flex flex-col items-center py-8 px-5 greetingsPageBgGradient h-screen">
     
    

    </div>
  );
} 