"use client";
import Link from "next/link";
import use<PERSON>piHook from "@/hooks/useApiHook";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
export default function CelebrityList() {
  const { handleApiCall, isApiLoading } = useApiHook();
  const [celebrities, setCelebrities] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("All");
  const auth = useSelector(selectAuth);

  useEffect(() => {
    const fetchCelebrities = async () => {
      try {
        const response = await handleApiCall({
          method: "GET",
          url: "celebrities",
        });
        if (response && response.data && Array.isArray(response.data)) {
          setCelebrities(response.data);
        } else {
          setCelebrities([]);
        }
      } catch (error) {
        console.log(error);
        setCelebrities([]); // Set to empty array on error
      }
    };
    fetchCelebrities();
  }, []);

  const categories = [
    "All",
    "Actor",
    "Athlete",
    "Musician",
    "Comedian",
    "Influencer",
    "Business",
  ];

  // Make sure celebrities is an array before filtering
  const filteredCelebrities = Array.isArray(celebrities)
    ? selectedCategory === "All"
      ? celebrities
      : celebrities.filter(
          (celebrity) =>
            celebrity?.category?.toLowerCase() ===
            selectedCategory.toLowerCase()
        )
    : [];

  const formatPrice = (price) => {
    return `$${(price / 100).toFixed(2)}`;
  };

  const getImageUrl = (celebrity) => {
    if (celebrity?.avatarUrl[0] > 0) {
      return celebrity.profileImages[0];
    }
    return "/images/celebrities/david.jpg";
  };

  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-10 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              Browse Celebrities
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              Find the perfect celebrity to create a personalized video message
              for your special occasion.
            </p>
          </div>
        </div>
      </div>

      {/* Filters Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-2">
        <div className="flex flex-wrap gap-4 justify-center">
          {categories.map((category) => (
            <button
              key={category}
              className={`rounded-full px-4 py-2 text-sm font-semibold cursor-pointer ${
                selectedCategory === category
                  ? "bg-white text-black"
                  : "bgGray hover:bg-black text-white"
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {isApiLoading && (
        <div className="flex justify-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
        </div>
      )}

      {/* Celebrities Grid */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-24 sm:py-16">
        {!isApiLoading && filteredCelebrities.length === 0 ? (
          <div className="text-center text-gray-300 py-16">
            <p className="text-xl">No celebrities found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-x-8 gap-y-16 sm:grid-cols-2 lg:grid-cols-3">
            {filteredCelebrities.map((celebrity) => (
              <Link
                key={celebrity._id}
                href={
                  auth?.userInfo?.user?.role === "customer"
                    ? `/celebrity/${celebrity._id}`
                    : `#`
                }
                onClick={(e) => {
                  if (auth?.userInfo?.user?.role != "customer") {
                    e.preventDefault();
                  }
                }}
                className="group relative flex flex-col overflow-hidden rounded-2xl bgGray"
              >
                <div className="aspect-[3/4] overflow-hidden">
                  <img
                    src={celebrity?.avatarUrl[0]}
                    alt={celebrity.name}
                    className="object-cover object-center group-hover:opacity-75 h-full w-full"
                  />
                </div>
                <div className="flex flex-1 flex-col justify-between p-6">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-300">
                      {celebrity.profession || celebrity.category}
                    </p>
                    <h3 className="mt-2 text-xl font-semibold text-white">
                      {celebrity.name}
                    </h3>
                    <p className="mt-3 text-base text-gray-300">
                      {celebrity.bio}
                    </p>
                  </div>
                  <div className="mt-6 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex items-center">
                        {[0, 1, 2, 3, 4].map((star) => (
                          <svg
                            key={star}
                            className={`h-5 w-5 flex-shrink-0 ${
                              star < Math.floor(celebrity.rating || 0)
                                ? "text-yellow-400"
                                : "text-gray-300"
                            }`}
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                              clipRule="evenodd"
                            />
                          </svg>
                        ))}
                      </div>
                      <p className="ml-2 text-sm text-gray-300">
                        {(celebrity.rating || 0).toFixed(1)} (
                        {celebrity.totalRatings || 0} reviews)
                      </p>
                    </div>
                    <p className="text-lg font-semibold text-white">
                      {formatPrice(celebrity.baseRate || 0)}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
