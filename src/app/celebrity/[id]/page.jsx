"use client";
import use<PERSON><PERSON>Hook from "@/hooks/useApiHook";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import LoginForm from "@/components/auth/LoginForm";

export default function CelebrityDetail({ params }) {
  const auth = useSelector(selectAuth);
  const { handleApiCall, isApiLoading } = useApiHook();
  const [celebrity, setCelebrity] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false); // Modal state
  const [formData, setFormData] = useState({
    occasion: "Birthday",
    recipient: "",
    message: "",
    deliveryDate: "",
    emotion: "happy", // Default emotion
  });
  const [orderStatus, setOrderStatus] = useState({
    success: false,
    message: "",
  });

  useEffect(() => {
    const fetchCelebrity = async () => {
      try {
        const response = await handleApiCall({
          method: "GET",
          url: "celebrities/" + params.id,
        });
        if (response && response.data) {
          setCelebrity(response.data);
        } else {
          setCelebrity(null);
        }
      } catch (error) {
        console.log(error);
        setCelebrity(null);
      }
    };
    fetchCelebrity();
  }, []);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isUserLoggedIn()) {
      setIsLoginModalOpen(true);
      return;
    }
    setIsSubmitting(true);

    try {
      // Call the digital-humans API
      const response = await handleApiCall({
        method: "POST",
        url: "digital-humans",
        data: {
          celebrityId: params.id,
          script: formData.message, // Using the special instructions as script
          emotion: formData.emotion,
        },
      });
      setOrderStatus({
        success: true,
        message: "Your video request has been submitted successfully!",
      });

      // Reset form
      setFormData({
        occasion: "Birthday",
        recipient: "",
        message: "",
        deliveryDate: "",
        emotion: "happy",
      });
    } catch (error) {
      console.error("Error placing order:", error);
      setOrderStatus({
        success: false,
        message: "Failed to submit your request. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isUserLoggedIn = () => {
    return Boolean(auth?.userInfo?.accessToken);
  };

  // Show loading state while fetching data
  if (isApiLoading || !celebrity) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  // Format price from cents to dollars
  const formattedPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(celebrity.price);

  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-4 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl lg:mx-0">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              {celebrity.name}
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              {celebrity.bio}
            </p>
            <div className="mt-6 flex items-center gap-x-4">
              <div className="flex items-center">
                {[0, 1, 2, 3, 4].map((rating) => (
                  <svg
                    key={rating}
                    className={`h-5 w-5 flex-shrink-0 ${
                      rating < Math.floor(celebrity.rating)
                        ? "text-yellow-400"
                        : "text-gray-300"
                    }`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                      clipRule="evenodd"
                    />
                  </svg>
                ))}
                <p className="ml-2 text-sm text-gray-300">
                  {celebrity.rating} ({celebrity.totalRatings} reviews)
                </p>
              </div>
              <p className="text-lg font-semibold text-white">
                {formattedPrice}
              </p>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
              {celebrity.categories &&
                celebrity.categories.map((category, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center rounded-md bg-gray-800 px-2 py-1 text-xs font-medium text-gray-300"
                  >
                    {category}
                  </span>
                ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="mx-auto max-w-7xl lg:px-8 py-4 sm:py-16">
        <div className="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:max-w-none lg:grid-cols-2">
          {/* Left Column - Bio and Sample Videos */}
          <div>
            <div className="aspect-[4/3] overflow-hidden rounded-2xl bgGray">
              {celebrity?.avatarUrl[0] && celebrity?.avatarUrl[0].length > 0 ? (
                <img
                  src={celebrity?.avatarUrl[0]}
                  alt={celebrity?.name}
                  className="w-full h-full object-cover object-center"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-500">
                  <img
                    src="/images/celebrities/david.jpg"
                    alt="Celebrity Image"
                    className=" object-cover"
                  />
                </div>
              )}
            </div>
            <div className="mt-8">
              <h2 className="text-2xl font-bold tracking-tight text-white">
                About
              </h2>
              <p className="mt-4 text-base leading-7 text-gray-300">
                {celebrity.bio}
              </p>
            </div>

            {celebrity.voiceSampleUrl &&
              celebrity.voiceSampleUrl.length > 0 && (
                <div className="mt-12">
                  <h2 className="text-2xl font-bold tracking-tight text-white">
                    Voice Samples
                  </h2>
                  <div className="mt-4 space-y-4">
                    {celebrity.voiceSampleUrl.map((sample, index) => (
                      <div key={index} className="p-4 bgGray rounded-lg">
                        <audio controls className="w-full">
                          <source src={sample} type="audio/mp3" />
                          Your browser does not support the audio element.
                        </audio>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>

          {/* Right Column - Order Form */}
          <div className="rounded-2xl bgGray p-8 hid">
            {orderStatus.success ? (
              <div className="mt-4 p-4 bg-green-800 rounded-lg text-center">
                <p className="text-white">{orderStatus.message}</p>
              </div>
            ) : (
              <div className="w-full">
                <h2 className="text-2xl font-bold tracking-tight text-white">
                  Request a Video Message
                </h2>
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-gray-300">
                    <span className="text-white font-medium">Status:</span>{" "}
                    {celebrity.status === "approved"
                      ? "Available"
                      : "Currently Unavailable"}
                  </div>
                  <div>
                    <span className="text-white font-medium">Completed:</span>{" "}
                    <span className="text-gray-300">
                      {celebrity.completedRequests}/{celebrity.totalRequests}{" "}
                      requests
                    </span>
                  </div>
                </div>

                {orderStatus.success && (
                  <div className="mt-4 p-4 bg-green-800 rounded-lg text-center">
                    <p className="text-white">{orderStatus.message}</p>
                  </div>
                )}

                {orderStatus.success === false && orderStatus.message && (
                  <div className="mt-4 p-4 bg-red-800 rounded-lg text-center">
                    <p className="text-white">{orderStatus.message}</p>
                  </div>
                )}

                {celebrity.isPublic ? (
                  <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                    <div className="hidden">
                      <label
                        htmlFor="occasion"
                        className="block text-sm font-medium leading-6 text-white"
                      >
                        Occasion
                      </label>
                      <select
                        id="occasion"
                        name="occasion"
                        value={formData.occasion}
                        onChange={handleInputChange}
                        className="mt-2 block w-full rounded-md border-0 bg-gray-800 py-1.5 pl-3 pr-10 text-white ring-1 ring-inset ring-gray-700 focus:ring-2 focus:ring-white sm:text-sm sm:leading-6"
                      >
                        <option>Birthday</option>
                        <option>Anniversary</option>
                        <option>Graduation</option>
                        <option>Wedding</option>
                        <option>Other</option>
                      </select>
                    </div>

                    <div className="hidden">
                      <label
                        htmlFor="recipient"
                        className="block text-sm font-medium leading-6 text-white"
                      >
                        Recipient's Name
                      </label>
                      <input
                        type="text"
                        name="recipient"
                        id="recipient"
                        value={formData.recipient}
                        onChange={handleInputChange}
                        className="mt-2 block w-full rounded-md border-0 bg-gray-800 px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-white sm:text-sm sm:leading-6"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="message"
                        className="block text-sm font-medium leading-6 text-white"
                      >
                        Add Script
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows={4}
                        value={formData.message}
                        onChange={handleInputChange}
                        className="mt-2 block w-full rounded-md border-0 bg-gray-800 px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-white sm:text-sm sm:leading-6"
                        required
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="emotion"
                        className="block text-sm font-medium leading-6 text-white"
                      >
                        Emotion
                      </label>
                      <select
                        id="emotion"
                        name="emotion"
                        value={formData.emotion}
                        onChange={handleInputChange}
                        className="mt-2 block w-full rounded-md border-0 bg-gray-800 py-1.5 pl-3 pr-10 text-white ring-1 ring-inset ring-gray-700 focus:ring-2 focus:ring-white sm:text-sm sm:leading-6"
                      >
                        <option value="happy">Happy</option>
                        <option value="surprise">Surprise</option>
                        <option value="serious">Serious</option>
                        <option value="neutral">Neutral</option>
                      </select>
                    </div>

                    <div className="hidden">
                      <label
                        htmlFor="deliveryDate"
                        className="block text-sm font-medium leading-6 text-white"
                      >
                        Delivery Date
                      </label>
                      <input
                        type="date"
                        name="deliveryDate"
                        id="deliveryDate"
                        value={formData.deliveryDate}
                        onChange={handleInputChange}
                        className="mt-2 block w-full rounded-md border-0 bg-gray-800 px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-gray-700 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-white sm:text-sm sm:leading-6"
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`w-full rounded-md bg-white px-3.5 py-2.5 text-center text-sm font-semibold text-black shadow-sm ${
                        isSubmitting
                          ? "opacity-70 cursor-not-allowed"
                          : "hover:bg-gray-200"
                      } focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white`}
                    >
                      {isSubmitting
                        ? "Processing..."
                        : `Place Order - ${formattedPrice}`}
                    </button>
                  </form>
                ) : (
                  <div className="mt-8 p-4 bg-gray-800 rounded-lg text-center">
                    <p className="text-gray-300">
                      This celebrity is currently unavailable for new requests.
                    </p>
                    <button
                      type="button"
                      className="mt-4 rounded-md bg-gray-700 px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm hover:bg-gray-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
                      disabled
                    >
                      Unavailable
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {isLoginModalOpen && (
        <>
          {/* Overlay */}
          <div className="fixed inset-0 bg-black opacity-80 z-40 flex items-center justify-center">
            {/* Modal */}
            <div className="relative border bg-black rounded-lg shadow-xl w-full max-w-md mx-auto p-6 z-50">
              {/* Modal Content */}
              <div className="mt-4">
                <LoginForm setIsLoginModalOpen={setIsLoginModalOpen} />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
