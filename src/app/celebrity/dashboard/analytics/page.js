'use client';

import { useState } from 'react';

// Mock data for analytics
const mockAnalytics = {
  overview: {
    totalEarnings: 12500,
    totalRequests: 48,
    averageRating: 4.8,
    completionRate: 95,
  },
  monthlyStats: [
    { month: 'Jan', earnings: 2800, requests: 12 },
    { month: 'Feb', earnings: 3200, requests: 14 },
    { month: 'Mar', earnings: 3500, requests: 15 },
    { month: 'Apr', earnings: 3000, requests: 13 },
  ],
  greetingTypes: [
    { type: 'Birthday', count: 20, earnings: 5200 },
    { type: 'Anniversary', count: 12, earnings: 3600 },
    { type: 'Graduation', count: 8, earnings: 2400 },
    { type: 'Wedding', count: 6, earnings: 1800 },
    { type: 'Other', count: 2, earnings: 500 },
  ],
  recentReviews: [
    {
      id: 1,
      customer: '<PERSON>',
      rating: 5,
      comment: "Amazing personalized message! Made my mom's birthday special.",
      date: '2024-04-10',
    },
    {
      id: 2,
      customer: '<PERSON>',
      rating: 4,
      comment: 'Great energy and perfect timing. Would recommend!',
      date: '2024-04-08',
    },
    {
      id: 3,
      customer: '<PERSON>',
      rating: 5,
      comment: 'Exceeded expectations. The personal touch was wonderful.',
      date: '2024-04-05',
    },
  ],
};

export default function CelebrityAnalyticsPage() {
  const [timeRange, setTimeRange] = useState('month');
  const [analytics] = useState(mockAnalytics);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className='min-h-screen bg-[#0c0c0c]'>
      <div>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900'>Analytics</h1>
          <p className='mt-2 text-sm text-gray-600'>
            Track your performance and earnings
          </p>
        </div>

        {/* Time Range Selector */}
        <div className='mb-6'>
          <div className='flex space-x-4'>
            {['week', 'month', 'year', 'all'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 text-sm font-medium rounded-md ${
                  timeRange === range
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Overview Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          <div className='bg-white rounded-lg shadow p-6'>
            <h3 className='text-sm font-medium text-gray-500'>
              Total Earnings
            </h3>
            <p className='mt-2 text-3xl font-semibold text-gray-900'>
              {formatCurrency(analytics.overview.totalEarnings)}
            </p>
          </div>
          <div className='bg-white rounded-lg shadow p-6'>
            <h3 className='text-sm font-medium text-gray-500'>
              Total Requests
            </h3>
            <p className='mt-2 text-3xl font-semibold text-gray-900'>
              {analytics.overview.totalRequests}
            </p>
          </div>
          <div className='bg-white rounded-lg shadow p-6'>
            <h3 className='text-sm font-medium text-gray-500'>
              Average Rating
            </h3>
            <p className='mt-2 text-3xl font-semibold text-gray-900'>
              {analytics.overview.averageRating}
            </p>
          </div>
          <div className='bg-white rounded-lg shadow p-6'>
            <h3 className='text-sm font-medium text-gray-500'>
              Completion Rate
            </h3>
            <p className='mt-2 text-3xl font-semibold text-gray-900'>
              {analytics.overview.completionRate}%
            </p>
          </div>
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>
          {/* Monthly Performance */}
          <div className='bg-white rounded-lg shadow'>
            <div className='p-6'>
              <h2 className='text-lg font-medium text-gray-900 mb-4'>
                Monthly Performance
              </h2>
              <div className='space-y-4'>
                {analytics.monthlyStats.map((stat) => (
                  <div key={stat.month} className='flex items-center'>
                    <div className='w-16 text-sm font-medium text-gray-500'>
                      {stat.month}
                    </div>
                    <div className='flex-1'>
                      <div className='h-2 bg-gray-200 rounded-full'>
                        <div
                          className='h-2 bg-indigo-600 rounded-full'
                          style={{
                            width: `${
                              (stat.earnings /
                                Math.max(
                                  ...analytics.monthlyStats.map(
                                    (s) => s.earnings
                                  )
                                )) *
                              100
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <div className='w-32 text-right'>
                      <div className='text-sm font-medium text-gray-900'>
                        {formatCurrency(stat.earnings)}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {stat.requests} requests
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Greeting Type Performance */}
          <div className='bg-white rounded-lg shadow'>
            <div className='p-6'>
              <h2 className='text-lg font-medium text-gray-900 mb-4'>
                Greeting Type Performance
              </h2>
              <div className='overflow-x-auto'>
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead>
                    <tr>
                      <th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Type
                      </th>
                      <th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Count
                      </th>
                      <th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Earnings
                      </th>
                    </tr>
                  </thead>
                  <tbody className='divide-y divide-gray-200'>
                    {analytics.greetingTypes.map((type) => (
                      <tr key={type.type}>
                        <td className='px-4 py-3 text-sm text-gray-900'>
                          {type.type}
                        </td>
                        <td className='px-4 py-3 text-sm text-gray-900'>
                          {type.count}
                        </td>
                        <td className='px-4 py-3 text-sm text-gray-900'>
                          {formatCurrency(type.earnings)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Recent Reviews */}
          <div className='lg:col-span-2 bg-white rounded-lg shadow'>
            <div className='p-6'>
              <h2 className='text-lg font-medium text-gray-900 mb-4'>
                Recent Reviews
              </h2>
              <div className='space-y-4'>
                {analytics.recentReviews.map((review) => (
                  <div
                    key={review.id}
                    className='border-b border-gray-200 pb-4 last:border-0'
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center'>
                        <div className='text-sm font-medium text-gray-900'>
                          {review.customer}
                        </div>
                        <div className='ml-2 flex items-center'>
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating
                                  ? 'text-yellow-400'
                                  : 'text-gray-300'
                              }`}
                              fill='currentColor'
                              viewBox='0 0 20 20'
                            >
                              <path d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' />
                            </svg>
                          ))}
                        </div>
                      </div>
                      <div className='text-sm text-gray-500'>
                        {new Date(review.date).toLocaleDateString()}
                      </div>
                    </div>
                    <p className='mt-2 text-sm text-gray-600'>
                      {review.comment}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
