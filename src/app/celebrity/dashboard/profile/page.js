"use client";

import { useState } from "react";

// Mock data for celebrity profile
const initialProfile = {
  name: "<PERSON>",
  category: "Actor",
  bio: "Award-winning actor with 20 years of experience in film and television.",
  image: "https://via.placeholder.com/150",
  socialMedia: {
    instagram: "@johndoe",
    twitter: "@johndoe",
    youtube: "JohnDoeOfficial",
  },
  availability: {
    monday: { start: "09:00", end: "17:00", available: true },
    tuesday: { start: "09:00", end: "17:00", available: true },
    wednesday: { start: "09:00", end: "17:00", available: true },
    thursday: { start: "09:00", end: "17:00", available: true },
    friday: { start: "09:00", end: "17:00", available: true },
    saturday: { start: "10:00", end: "14:00", available: false },
    sunday: { start: "10:00", end: "14:00", available: false },
  },
  pricing: {
    basePrice: 99,
    rushDelivery: 50,
    customEffects: 25,
    bulkDiscount: {
      minRequests: 5,
      discount: 10, // percentage
    },
  },
  greetingTypes: [
    { id: "birthday", name: "Birthday", price: 99 },
    { id: "anniversary", name: "Anniversary", price: 149 },
    { id: "graduation", name: "Graduation", price: 129 },
    { id: "wedding", name: "Wedding", price: 199 },
  ],
};

export default function CelebrityProfilePage() {
  const [profile, setProfile] = useState(initialProfile);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState("profile");

  const handleProfileUpdate = (e) => {
    e.preventDefault();
    setIsEditing(false);
  };

  const handleAvailabilityUpdate = (day, field, value) => {
    setProfile((prev) => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: {
          ...prev.availability[day],
          [field]: value,
        },
      },
    }));
  };

  const handlePricingUpdate = (field, value) => {
    setProfile((prev) => ({
      ...prev,
      pricing: {
        ...prev.pricing,
        [field]: value,
      },
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your profile, availability, and pricing
          </p>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {["profile", "availability", "pricing"].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`${
                  activeTab === tab
                    ? "border-indigo-500 text-indigo-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>

        {/* Profile Tab */}
        {activeTab === "profile" && (
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Profile Information
                </h2>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="text-indigo-600 hover:text-indigo-900"
                >
                  {isEditing ? "Cancel" : "Edit Profile"}
                </button>
              </div>

              <form onSubmit={handleProfileUpdate}>
                <div className="space-y-6">
                  <div className="flex items-center space-x-6">
                    <div className="h-24 w-24">
                      <img
                        src={profile.image}
                        alt={profile.name}
                        className="h-24 w-24 rounded-full object-cover"
                      />
                    </div>
                    {isEditing && (
                      <button
                        type="button"
                        className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                      >
                        Change Photo
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Name
                      </label>
                      <input
                        type="text"
                        value={profile.name}
                        onChange={(e) =>
                          setProfile((prev) => ({
                            ...prev,
                            name: e.target.value,
                          }))
                        }
                        disabled={!isEditing}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Category
                      </label>
                      <select
                        value={profile.category}
                        onChange={(e) =>
                          setProfile((prev) => ({
                            ...prev,
                            category: e.target.value,
                          }))
                        }
                        disabled={!isEditing}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      >
                        <option value="Actor">Actor</option>
                        <option value="Musician">Musician</option>
                        <option value="Athlete">Athlete</option>
                        <option value="Influencer">Influencer</option>
                      </select>
                    </div>

                    <div className="sm:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Bio
                      </label>
                      <textarea
                        value={profile.bio}
                        onChange={(e) =>
                          setProfile((prev) => ({
                            ...prev,
                            bio: e.target.value,
                          }))
                        }
                        disabled={!isEditing}
                        rows={4}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Instagram
                      </label>
                      <input
                        type="text"
                        value={profile.socialMedia.instagram}
                        onChange={(e) =>
                          setProfile((prev) => ({
                            ...prev,
                            socialMedia: {
                              ...prev.socialMedia,
                              instagram: e.target.value,
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Twitter
                      </label>
                      <input
                        type="text"
                        value={profile.socialMedia.twitter}
                        onChange={(e) =>
                          setProfile((prev) => ({
                            ...prev,
                            socialMedia: {
                              ...prev.socialMedia,
                              twitter: e.target.value,
                            },
                          }))
                        }
                        disabled={!isEditing}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>
                  </div>

                  {isEditing && (
                    <div className="flex justify-end">
                      <button
                        type="submit"
                        className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                      >
                        Save Changes
                      </button>
                    </div>
                  )}
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Availability Tab */}
        {activeTab === "availability" && (
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Availability Settings
              </h2>
              <div className="space-y-6">
                {Object.entries(profile.availability).map(([day, schedule]) => (
                  <div key={day} className="flex items-center space-x-4">
                    <div className="w-32">
                      <label className="block text-sm font-medium text-gray-700 capitalize">
                        {day}
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={schedule.available}
                        onChange={(e) =>
                          handleAvailabilityUpdate(
                            day,
                            "available",
                            e.target.checked
                          )
                        }
                        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className="text-sm text-gray-500">Available</span>
                    </div>
                    {schedule.available && (
                      <>
                        <input
                          type="time"
                          value={schedule.start}
                          onChange={(e) =>
                            handleAvailabilityUpdate(
                              day,
                              "start",
                              e.target.value
                            )
                          }
                          className="rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                        />
                        <span className="text-gray-500">to</span>
                        <input
                          type="time"
                          value={schedule.end}
                          onChange={(e) =>
                            handleAvailabilityUpdate(day, "end", e.target.value)
                          }
                          className="rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                        />
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Pricing Tab */}
        {activeTab === "pricing" && (
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Pricing Configuration
              </h2>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Base Price (CBLX)
                  </label>
                  <input
                    type="number"
                    value={profile.pricing.basePrice}
                    onChange={(e) =>
                      handlePricingUpdate("basePrice", parseInt(e.target.value))
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Rush Delivery Fee ($)
                  </label>
                  <input
                    type="number"
                    value={profile.pricing.rushDelivery}
                    onChange={(e) =>
                      handlePricingUpdate(
                        "rushDelivery",
                        parseInt(e.target.value)
                      )
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Custom Effects Fee ($)
                  </label>
                  <input
                    type="number"
                    value={profile.pricing.customEffects}
                    onChange={(e) =>
                      handlePricingUpdate(
                        "customEffects",
                        parseInt(e.target.value)
                      )
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  />
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Bulk Discount
                  </h3>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Minimum Requests
                      </label>
                      <input
                        type="number"
                        value={profile.pricing.bulkDiscount.minRequests}
                        onChange={(e) =>
                          handlePricingUpdate("bulkDiscount", {
                            ...profile.pricing.bulkDiscount,
                            minRequests: parseInt(e.target.value),
                          })
                        }
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Discount (%)
                      </label>
                      <input
                        type="number"
                        value={profile.pricing.bulkDiscount.discount}
                        onChange={(e) =>
                          handlePricingUpdate("bulkDiscount", {
                            ...profile.pricing.bulkDiscount,
                            discount: parseInt(e.target.value),
                          })
                        }
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                      />
                    </div>
                  </div>
                </div>

                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Greeting Types
                  </h3>
                  <div className="space-y-4">
                    {profile.greetingTypes.map((type) => (
                      <div
                        key={type.id}
                        className="flex items-center justify-between"
                      >
                        <span className="text-sm font-medium text-gray-900">
                          {type.name}
                        </span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">$</span>
                          <input
                            type="number"
                            value={type.price}
                            onChange={(e) =>
                              setProfile((prev) => ({
                                ...prev,
                                greetingTypes: prev.greetingTypes.map((t) =>
                                  t.id === type.id
                                    ? { ...t, price: parseInt(e.target.value) }
                                    : t
                                ),
                              }))
                            }
                            className="w-20 rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
