'use client'

import { useState } from 'react'

// Mock data for greeting requests
const mockRequests = [
  {
    id: 1,
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    occasion: 'Birthday',
    recipient: '<PERSON>',
    message: 'Happy Birthday! Wishing you all the best on your special day.',
    tone: 'Fun and Energetic',
    specialInstructions: 'Include a birthday song at the end',
    status: 'pending',
    date: '2024-04-15',
    price: 99,
    rushDelivery: false,
    customEffects: false,
  },
  {
    id: 2,
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    occasion: 'Anniversary',
    recipient: '<PERSON>',
    message: "Happy 10th Anniversary! Here's to many more years of love and happiness.",
    tone: 'Romantic',
    specialInstructions: 'Include some romantic background music',
    status: 'in_progress',
    date: '2024-04-10',
    price: 149,
    rushDelivery: true,
    customEffects: true,
  },
  {
    id: 3,
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
    occasion: 'Graduation',
    recipient: '<PERSON>',
    message: 'Congratulations on your graduation! Your hard work has paid off.',
    tone: 'Inspirational',
    specialInstructions: 'Include graduation cap and diploma props',
    status: 'completed',
    date: '2024-04-05',
    price: 129,
    rushDelivery: false,
    customEffects: true,
  },
]

export default function CelebrityRequestsPage() {
  const [requests, setRequests] = useState(mockRequests)
  const [selectedRequest, setSelectedRequest] = useState(null)
  const [statusFilter, setStatusFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  const handleStatusUpdate = (requestId, newStatus) => {
    setRequests((prev) =>
      prev.map((request) =>
        request.id === requestId ? { ...request, status: newStatus } : request
      )
    )
  }

  const filteredRequests = requests.filter((request) => {
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter
    const matchesSearch =
      searchTerm === '' ||
      request.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.recipient.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.occasion.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesStatus && matchesSearch
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Greeting Requests</h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage and respond to greeting requests
          </p>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by customer, recipient, or occasion..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-4 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="rounded-md border border-gray-300 px-4 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        {/* Requests List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Occasion
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recipient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRequests.map((request) => (
                  <tr
                    key={request.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => setSelectedRequest(request)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {request.customer.name}
                      </div>
                      <div className="text-sm text-gray-500">{request.customer.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {request.occasion}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {request.recipient}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(request.date).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${request.price}
                      {request.rushDelivery && (
                        <span className="ml-1 text-xs text-indigo-600">(Rush)</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${getStatusColor(
                          request.status
                        )}`}
                      >
                        {request.status.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleStatusUpdate(request.id, 'in_progress')
                        }}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        Accept
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleStatusUpdate(request.id, 'completed')
                        }}
                        className="text-green-600 hover:text-green-900"
                      >
                        Complete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Request Details Modal */}
        {selectedRequest && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">Request Details</h3>
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Close</span>
                    <svg
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Customer</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.customer.name}</p>
                    <p className="text-sm text-gray-500">{selectedRequest.customer.email}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Occasion</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.occasion}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Recipient</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.recipient}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Message</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.message}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Tone</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.tone}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Special Instructions</h4>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedRequest.specialInstructions}
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Date</h4>
                      <p className="mt-1 text-sm text-gray-900">
                        {new Date(selectedRequest.date).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Price</h4>
                      <p className="mt-1 text-sm text-gray-900">${selectedRequest.price}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Close
                  </button>
                  {selectedRequest.status === 'pending' && (
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedRequest.id, 'in_progress')
                        setSelectedRequest(null)
                      }}
                      className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    >
                      Accept Request
                    </button>
                  )}
                  {selectedRequest.status === 'in_progress' && (
                    <button
                      onClick={() => {
                        handleStatusUpdate(selectedRequest.id, 'completed')
                        setSelectedRequest(null)
                      }}
                      className="rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    >
                      Mark as Completed
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 