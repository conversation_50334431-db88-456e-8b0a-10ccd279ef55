'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import useApiHook from '@/hooks/useApiHook';
import {
  DocumentTextIcon,
  ClockIcon,
  FlagIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

export default function GreetingDetailsPage() {
  const { id } = useParams();
  const { handleApiCall, isApiLoading } = useApiHook();
  const [greeting, setGreeting] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGreetingDetails = async () => {
      try {
        const response = await handleApiCall({
          method: 'GET',
          url: `digital-humans/${id}`,
        });
        setGreeting(response?.data);
      } catch (error) {
        console.error('Error fetching greeting details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGreetingDetails();
  }, [id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!greeting) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-white">Greeting not found</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0c0c0c] p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Greeting Details</h1>
          <p className="mt-2 text-sm text-gray-400">ID: {greeting._id}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Request Information */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#2a2a2a]">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <DocumentTextIcon className="h-6 w-6 mr-2 text-blue-500" />
              Request Information
            </h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-400">Script</p>
                <p className="text-white mt-1">{greeting.script}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Emotion</p>
                <p className="text-white mt-1">{greeting.emotion}</p>
              </div>
            </div>
          </div>

          {/* Status Information */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#2a2a2a]">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <ClockIcon className="h-6 w-6 mr-2 text-yellow-500" />
              Status Information
            </h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-400">Status</p>
                <p className="text-white mt-1">{greeting.status}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Processing Started</p>
                <p className="text-white mt-1">
                  {new Date(greeting.processingStartedAt).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Generation Attempts</p>
                <p className="text-white mt-1">{greeting.generationAttempts}</p>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#2a2a2a] md:col-span-2">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <FlagIcon className="h-6 w-6 mr-2 text-purple-500" />
              Additional Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-400">Flags</p>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center">
                    {greeting.flags?.includes('inappropriate') ? (
                      <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                    ) : (
                      <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    )}
                    <span className="text-white">Inappropriate Content</span>
                  </div>
                  <div className="flex items-center">
                    {greeting.flags?.includes('copyright') ? (
                      <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                    ) : (
                      <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    )}
                    <span className="text-white">Copyright Issues</span>
                  </div>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-400">Created At</p>
                <p className="text-white mt-1">
                  {new Date(greeting.createdAt).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 