'use client'

import { useState } from 'react'

// Mock data for schedule
const mockSchedule = {
  availability: {
    monday: { start: '09:00', end: '17:00', available: true },
    tuesday: { start: '09:00', end: '17:00', available: true },
    wednesday: { start: '09:00', end: '17:00', available: true },
    thursday: { start: '09:00', end: '17:00', available: true },
    friday: { start: '09:00', end: '17:00', available: true },
    saturday: { start: '10:00', end: '14:00', available: false },
    sunday: { start: '10:00', end: '14:00', available: false },
  },
  upcomingRequests: [
    {
      id: 1,
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      occasion: 'Birthday',
      recipient: '<PERSON>',
      date: '2024-04-15',
      time: '14:00',
      duration: 30,
      status: 'confirmed',
    },
    {
      id: 2,
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      occasion: 'Anniversary',
      recipient: '<PERSON>',
      date: '2024-04-16',
      time: '11:00',
      duration: 45,
      status: 'pending',
    },
    {
      id: 3,
      customer: {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      occasion: 'Graduation',
      recipient: '<PERSON> <PERSON>',
      date: '2024-04-17',
      time: '15:30',
      duration: 30,
      status: 'confirmed',
    },
  ],
  timeSlots: [
    '09:00',
    '09:30',
    '10:00',
    '10:30',
    '11:00',
    '11:30',
    '12:00',
    '12:30',
    '13:00',
    '13:30',
    '14:00',
    '14:30',
    '15:00',
    '15:30',
    '16:00',
    '16:30',
    '17:00',
  ],
}

export default function CelebritySchedulePage() {
  const [schedule, setSchedule] = useState(mockSchedule)
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedRequest, setSelectedRequest] = useState(null)

  const handleAvailabilityUpdate = (day, field, value) => {
    setSchedule((prev) => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: {
          ...prev.availability[day],
          [field]: value,
        },
      },
    }))
  }

  const getDaySchedule = (date) => {
    const day = date.toLocaleDateString('en-US', { weekday: 'lowercase' })
    return schedule.availability[day]
  }

  const getUpcomingRequestsForDate = (date) => {
    return schedule.upcomingRequests.filter(
      (request) => new Date(request.date).toDateString() === date.toDateString()
    )
  }

  const formatTime = (time) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Schedule</h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage your availability and upcoming requests
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Availability Settings */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Availability Settings</h2>
                <div className="space-y-4">
                  {Object.entries(schedule.availability).map(([day, schedule]) => (
                    <div key={day} className="flex items-center space-x-4">
                      <div className="w-24">
                        <label className="block text-sm font-medium text-gray-700 capitalize">
                          {day}
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={schedule.available}
                          onChange={(e) =>
                            handleAvailabilityUpdate(day, 'available', e.target.checked)
                          }
                          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                        />
                        <span className="text-sm text-gray-500">Available</span>
                      </div>
                      {schedule.available && (
                        <>
                          <input
                            type="time"
                            value={schedule.start}
                            onChange={(e) =>
                              handleAvailabilityUpdate(day, 'start', e.target.value)
                            }
                            className="rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                          />
                          <span className="text-gray-500">to</span>
                          <input
                            type="time"
                            value={schedule.end}
                            onChange={(e) =>
                              handleAvailabilityUpdate(day, 'end', e.target.value)
                            }
                            className="rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Daily Schedule */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium text-gray-900">Daily Schedule</h2>
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() =>
                        setSelectedDate(
                          new Date(selectedDate.setDate(selectedDate.getDate() - 1))
                        )
                      }
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <span className="sr-only">Previous day</span>
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                    </button>
                    <span className="text-sm font-medium text-gray-900">
                      {selectedDate.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </span>
                    <button
                      onClick={() =>
                        setSelectedDate(
                          new Date(selectedDate.setDate(selectedDate.getDate() + 1))
                        )
                      }
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <span className="sr-only">Next day</span>
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <div className="space-y-4">
                  {schedule.timeSlots.map((time) => {
                    const daySchedule = getDaySchedule(selectedDate)
                    const isAvailable =
                      daySchedule.available &&
                      time >= daySchedule.start &&
                      time <= daySchedule.end
                    const request = getUpcomingRequestsForDate(selectedDate).find(
                      (r) => r.time === time
                    )

                    return (
                      <div
                        key={time}
                        className={`flex items-center p-4 rounded-lg ${
                          request
                            ? 'bg-indigo-50 border border-indigo-200'
                            : isAvailable
                            ? 'bg-white border border-gray-200'
                            : 'bg-gray-50 border border-gray-200'
                        }`}
                      >
                        <div className="w-24 text-sm font-medium text-gray-900">
                          {formatTime(time)}
                        </div>
                        <div className="flex-1">
                          {request ? (
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {request.occasion} for {request.recipient}
                              </div>
                              <div className="text-sm text-gray-500">
                                {request.customer.name} • {request.duration} minutes
                              </div>
                            </div>
                          ) : isAvailable ? (
                            <div className="text-sm text-gray-500">Available</div>
                          ) : (
                            <div className="text-sm text-gray-500">Unavailable</div>
                          )}
                        </div>
                        {request && (
                          <button
                            onClick={() => setSelectedRequest(request)}
                            className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                          >
                            View Details
                          </button>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Request Details Modal */}
        {selectedRequest && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">Request Details</h3>
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Close</span>
                    <svg
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="px-6 py-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Customer</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.customer.name}</p>
                    <p className="text-sm text-gray-500">{selectedRequest.customer.email}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Occasion</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.occasion}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Recipient</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.recipient}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Date</h4>
                      <p className="mt-1 text-sm text-gray-900">
                        {new Date(selectedRequest.date).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Time</h4>
                      <p className="mt-1 text-sm text-gray-900">
                        {formatTime(selectedRequest.time)}
                      </p>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Duration</h4>
                    <p className="mt-1 text-sm text-gray-900">{selectedRequest.duration} minutes</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Status</h4>
                    <p className="mt-1 text-sm text-gray-900 capitalize">{selectedRequest.status}</p>
                  </div>
                </div>
              </div>
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setSelectedRequest(null)}
                    className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Close
                  </button>
                  {selectedRequest.status === 'pending' && (
                    <button
                      onClick={() => {
                        // TODO: Implement status update logic
                        setSelectedRequest(null)
                      }}
                      className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    >
                      Confirm Request
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 