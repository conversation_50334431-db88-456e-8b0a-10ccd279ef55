"use client";

import { useEffect, useState } from "react";
import useApiHook from "@/hooks/useApiHook";

export default function MyVideos() {
  const { handleApiCall, isApiLoading } = useApiHook();
  const [videoRequests, setVideoRequests] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        const response = await handleApiCall({
          method: "GET",
          url: "digital-humans/my-requests",
        });
        setVideoRequests(response?.data);
      } catch (error) {
        console.error("Error fetching video requests:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, []);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-[#0c0c0c] p-6">
      <div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">My Videos</h1>
          <p className="text-gray-400 mt-2">View all your generated videos</p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        ) : videoRequests.length === 0 ? (
          <div className="text-center py-12 bgGray rounded-lg">
            <p className="text-gray-400 text-lg">No videos found</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {videoRequests.map((video) => (
              <div
                key={video._id}
                className="bgGray rounded-lg overflow-hidden shadow-lg"
              >
                {video.videoUrl ? (
                  <div className="aspect-video">
                    <video
                      src={video.videoUrl}
                      className="w-full h-full object-cover"
                      controls
                      preload="metadata"
                    />
                  </div>
                ) : (
                  <div className="aspect-video bg-gray-800 flex justify-center items-center">
                    <p className="text-gray-500">Video not available</p>
                  </div>
                )}
                <div className="p-4">
                  <p className="text-white font-medium truncate">
                    {video.script}
                  </p>
                  <div className="mt-2 flex justify-between items-center">
                    <span className="text-sm text-gray-400">
                      {formatDate(video.createdAt)}
                    </span>
                    <span className="px-2 py-1 bg-blue-900 text-blue-200 text-xs rounded-full">
                      {video.emotion}
                    </span>
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      Duration:{" "}
                      {video.videoDuration
                        ? `${video.videoDuration.toFixed(1)}s`
                        : "N/A"}
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded ${
                        video.status === "COMPLETED"
                          ? "bg-green-900 text-green-200"
                          : "bg-yellow-900 text-yellow-200"
                      }`}
                    >
                      {video.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
