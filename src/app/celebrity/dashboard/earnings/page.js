'use client'

import { useState } from 'react'

// Mock data for earnings
const mockEarnings = {
  totalEarnings: 12500,
  pendingBalance: 2500,
  lastPayout: {
    date: '2024-03-01',
    amount: 3000,
    status: 'completed',
  },
  monthlyStats: [
    { month: 'Jan', earnings: 2800, requests: 28 },
    { month: 'Feb', earnings: 3200, requests: 32 },
    { month: 'Mar', earnings: 3500, requests: 35 },
    { month: 'Apr', earnings: 3000, requests: 30 },
  ],
  recentPayments: [
    {
      id: 1,
      date: '2024-03-15',
      amount: 1500,
      status: 'completed',
      description: 'March 1-15 Earnings',
      paymentMethod: 'Bank Transfer',
    },
    {
      id: 2,
      date: '2024-03-01',
      amount: 1500,
      status: 'completed',
      description: 'February 16-28 Earnings',
      paymentMethod: 'Bank Transfer',
    },
    {
      id: 3,
      date: '2024-02-15',
      amount: 1600,
      status: 'completed',
      description: 'February 1-15 Earnings',
      paymentMethod: 'Bank Transfer',
    },
  ],
  greetingStats: [
    { type: 'Birthday', count: 45, earnings: 4500 },
    { type: 'Anniversary', count: 25, earnings: 3750 },
    { type: 'Graduation', count: 15, earnings: 1950 },
    { type: 'Wedding', count: 10, earnings: 2000 },
  ],
}

export default function CelebrityEarningsPage() {
  const [timeRange, setTimeRange] = useState('month')

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Earnings Dashboard</h1>
          <p className="mt-2 text-sm text-gray-600">
            Track your earnings, payments, and performance
          </p>
        </div>

        {/* Earnings Overview */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Earnings</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              ${mockEarnings.totalEarnings.toLocaleString()}
            </p>
            <p className="mt-2 text-sm text-gray-600">All time</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Pending Balance</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              ${mockEarnings.pendingBalance.toLocaleString()}
            </p>
            <p className="mt-2 text-sm text-gray-600">Next payout: April 1, 2024</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Last Payout</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              ${mockEarnings.lastPayout.amount.toLocaleString()}
            </p>
            <p className="mt-2 text-sm text-gray-600">
              {new Date(mockEarnings.lastPayout.date).toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="mb-8">
          <div className="flex justify-end">
            <div className="inline-flex rounded-md shadow-sm">
              {['week', 'month', 'year', 'all'].map((range) => (
                <button
                  key={range}
                  onClick={() => setTimeRange(range)}
                  className={`${
                    timeRange === range
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  } relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 first:rounded-l-md last:rounded-r-md focus:z-10 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500`}
                >
                  {range.charAt(0).toUpperCase() + range.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Monthly Stats Chart */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Monthly Performance</h2>
            <div className="h-64">
              <div className="flex items-end h-48 space-x-2">
                {mockEarnings.monthlyStats.map((stat) => (
                  <div key={stat.month} className="flex-1">
                    <div
                      className="bg-indigo-600 rounded-t"
                      style={{
                        height: `${(stat.earnings / 4000) * 100}%`,
                      }}
                    />
                    <div className="text-center mt-2">
                      <p className="text-sm font-medium text-gray-900">${stat.earnings}</p>
                      <p className="text-xs text-gray-500">{stat.month}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Greeting Type Stats */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Greeting Type Performance</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requests
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Earnings
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Average
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mockEarnings.greetingStats.map((stat) => (
                    <tr key={stat.type}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {stat.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {stat.count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${stat.earnings.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${(stat.earnings / stat.count).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Payment History */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Payment History</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Method
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mockEarnings.recentPayments.map((payment) => (
                    <tr key={payment.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(payment.date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${payment.amount.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex rounded-full px-2 text-xs font-semibold leading-5 bg-green-100 text-green-800">
                          {payment.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {payment.paymentMethod}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 