"use client";
import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser, updateUser } from "@/redux/slices/auth/slice";
import { useRouter } from "next/navigation";
import { selectAuth } from "@/redux/slices/auth/selectors";
import useApiHook from "@/hooks/useApiHook";

export default function CelebrityHome() {
  const [showLogoutDropdown, setShowLogoutDropdown] = useState(false);
  const { handleApiCall, isApiLoading } = useApiHook();
  const [showLoading, setShowLoading] = useState(false);
  const dropdownRef = useRef(null);
  const router = useRouter();
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const celebId = auth?.userInfo?.user?.celebrityProfile?._id;
  const dispatch = useDispatch();
  // Add state for profile visibility
  const [isPublic, setIsPublic] = useState(
    user?.celebrityProfile?.isPublic || false
  );

  const [dashboardData, setDashboardData] = useState({
    digitalHumans: { total: 0, percentChange: "0.0", isIncreasing: false },
    requests: { total: 0, percentChange: "0.0", isIncreasing: false },
    reviews: { totalCount: 0, percentChange: "0.0", isIncreasing: false },
    subscribers: { total: 0, percentChange: "0.0", isIncreasing: false },
  });

  const dashboardItems = [
    {
      label: dashboardData.digitalHumans.total,
      icon: "/images/home/<USER>",
      desc: "Greetings created",
      bg: "gradient1",
      url: "/",
      percentChange: dashboardData.digitalHumans.percentChange,
      isIncreasing: dashboardData.digitalHumans.isIncreasing,
    },
    {
      label: dashboardData.requests.total,
      icon: "/images/home/<USER>",
      desc: "Requests",
      bg: "gradient2",
      url: "/",
      percentChange: dashboardData.requests.percentChange,
      isIncreasing: dashboardData.requests.isIncreasing,
    },
    {
      label: dashboardData.reviews.totalCount,
      icon: "/images/home/<USER>",
      desc: "Reviews",
      bg: "gradient3",
      url: "/",
      percentChange: dashboardData.reviews.percentChange,
      isIncreasing: dashboardData.reviews.isIncreasing,
    },
    {
      label: dashboardData.subscribers.total,
      icon: "/images/home/<USER>",
      desc: "Subscribers",
      bg: "gradient4",
      url: "/",
      percentChange: dashboardData.subscribers.percentChange,
      isIncreasing: dashboardData.subscribers.isIncreasing,
    },
  ];

  const fetchCelebrityDashboard = async () => {
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities/${celebId}/dashboard`,
      });

      if (resp.status === 200) {
        setDashboardData({
          digitalHumans: {
            total: resp.data.digitalHumans?.total || 0,
            percentChange: resp.data.digitalHumans?.percentChange || "0.0",
            isIncreasing: resp.data.digitalHumans?.isIncreasing || false,
          },
          requests: {
            total: resp.data.requests?.total || 0,
            percentChange: "0.0",
            isIncreasing: false,
          },
          reviews: {
            totalCount: resp.data.reviews?.totalCount || 0,
            percentChange: "0.0",
            isIncreasing: false,
          },
          subscribers: {
            total: resp.data.subscribers?.total || 0,
            percentChange: resp.data.subscribers?.percentChange || "0.0",
            isIncreasing: resp.data.subscribers?.isIncreasing || false,
          },
        });
      }
    } catch (error) {
      console.error("Dashboard fetch error:", error);
    }
  };

  // Function to toggle profile visibility
  const toggleVisibility = async () => {
    // Set optimistic UI update
    setIsPublic(!isPublic);
    setShowLoading(true);
    try {
      const response = await handleApiCall({
        method: "POST",
        url: `/celebrities/${celebId}/update-profile`,
        data: {
          isPublic: !isPublic,
        },
      });

      if (response.status === 200 || response.status === 201) {
        setShowLoading(false);
        dispatch(updateUser({ user: response?.data }));
        console.error("Failed to update profile visibility");
      }
    } catch (error) {
      // Revert on error
      setIsPublic(isPublic);
      setShowLoading(false);
      console.error("Error updating profile visibility:", error);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    fetchCelebrityDashboard();

    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLogoutDropdown(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [celebId]);

  const handleLogout = () => {
    dispatch(logoutUser());
    router.push("/auth/start");
  };

  return (
    <div className="flex flex-col items-center py-8 px-5 pageBgGradient h-screen">
      {/* Header */}
      <div className="w-full max-w-md flex flex-col gap-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2 opacity-0 cursor-pointer">
            <img src="/images/home/<USER>" alt="Logo" className="w-5" />
            <img src="/images/home/<USER>" alt="Logo" className="w-5" />
          </div>
          <img src="/images/home/<USER>" alt="Logo" className="w-24" />
          <div
            className="flex items-center gap-2 cursor-pointer relative"
            ref={dropdownRef}
          >
            <img
              src="/images/home/<USER>"
              alt="Options"
              className="w-5 logoutDropdown"
              onClick={() => setShowLogoutDropdown(!showLogoutDropdown)}
            />
            {showLogoutDropdown && (
              <div className="absolute right-0 top-8 px-0 bg-white rounded-lg shadow-lg z-10 overflow-hidden cursor-pointer">
                <button
                  onClick={handleLogout}
                  className="w-full cursor-pointer text-left px-4 py-2 text-gray-800 hover:bg-gray-100 flex items-center gap-2 transition-colors text-sm font-bold"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </button>
              </div>
            )}
            <img
              src="/images/home/<USER>"
              alt="Notifications"
              className="w-5"
            />
          </div>
        </div>
        {/* Profile */}
        <div className="flex items-center gap-4">
          <img
            src={user?.profilePhotoUrl || "/images/home/<USER>"}
            alt="Profile"
            className={`w-12 h-12 rounded-full object-cover ${user?.profilePhotoUrl ? "border-1 border-[##999999]" :""} `}
          />
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-semibold text-white">
                Hello, {user?.firstName}
              </span>
              <img
                src="/images/home/<USER>"
                alt="verified"
                className="w-5"
              />
            </div>

            {/* Visibility Toggle */}
            {showLoading ? (
              <div className="h-4 mt-2">
                <img
                  src="/images/home/<USER>"
                  alt="loading"
                  className="w-4 animate-spin"
                />
              </div>
            ) : (
              <div className="flex items-center gap-2 mt-1">
                <button
                  onClick={toggleVisibility}
                  className={`relative inline-flex h-5 w-9 cursor-pointer items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    isPublic ? "bg-green-700" : "bg-gray-400"
                  }`}
                  disabled={isApiLoading}
                >
                  <span className="sr-only">Toggle visibility</span>
                  <span
                    className={`${
                      isPublic ? "translate-x-4" : "translate-x-1"
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                  />
                </button>
                <span className="text-xs text-gray-400">
                  {isPublic ? "Visible to the public" : "Private"}
                </span>
              </div>
            )}
          </div>
        </div>
        {/* Earnings */}
       <div className="grayGradientBg rounded-xl shadow-lg px-5 py-3 mt-2 flex flex-col items-start">
  <span className="text-white text-xs">
    Your earnings this month
  </span>
  <span className="text-[2rem] leading-9 font-bold text-white mt-2">
    <span className="text-xl pr-3">$CLBX</span>
    {user?.celebrityProfile?.totalRatings ||
      dashboardData?.earnings?.balance ||
      "0"}
  </span>
  <button 
    onClick={() => router.push('/wallet-connect')}
    className="w-[311px] h-[32px] self-center mt-3 py-1 text-white text-center font-medium rounded-[24px] border border-white bg-[rgba(242,244,248,0.40)] shadow-[0.996px_0.996px_1.668px_0px_rgba(255,255,255,0.25)_inset,_-0.498px_-0.996px_0.996px_0px_rgba(0,0,0,0.45)_inset,_4.729px_5.476px_10.18px_0px_rgba(0,0,0,0.30),_13.192px_18.17px_22.351px_0px_rgba(0,0,0,0.30)]"
  >
    Connect Wallet
  </button>
</div>

        {/* Verification Pending */}
        <div className="bg-white/30 border border-white rounded-xl p-4 flex items-center gap-4 mt-4">
          <img
            src="/images/home/<USER>"
            alt="greeting"
            className="w-16"
          />
          <div>
            <span className="block text-white text-sm font-semibold">
              Create voice greetings
            </span>
            <span className="block text-sm font-light text-white pr-20">
              Record short soundbites to showcase your voice
            </span>
          </div>
          <a href="/">
            <img
              src="/images/home/<USER>"
              alt="greeting"
              className=" h-6"
            />
          </a>
        </div>
        {/* Stats Cards */}
        <div className="grid grid-cols-2 gap-4 mt-4">
          {dashboardItems.map((item, index) => (
            <div
              key={index}
              className={`${item.bg} rounded-xl p-5 flex flex-col justify-end items-start relative h-40 text-black`}
            >
              <div className="absolute top-2 left-2 rounded-full p-2">
                <img src={item.icon} className="w-12" alt={item.desc} />
              </div>
              <div className="absolute top-5 right-4 rounded-full px-4 py-1 bg-white text-sm hidden">
                <p
                  className={
                    item.isIncreasing ? "text-green-600" : "text-red-600"
                  }
                >
                  {item.isIncreasing ? "+" : "-"}
                  {Math.abs(parseFloat(item.percentChange)).toFixed(1)}%
                </p>
              </div>
              <span className="text-xl font-semibold">{item.label}</span>
              <span className="text-sm font-light">{item.desc}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
