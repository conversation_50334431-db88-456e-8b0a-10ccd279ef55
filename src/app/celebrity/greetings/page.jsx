"use client"
import CongratsGreeting from '@/components/celebrity/CongratsGreeting';
import Projects from '@/components/celebrity/Projects';
import Requests from '@/components/celebrity/Requests';
import Uploads from '@/components/celebrity/Uploads';
import React, { useState, useEffect } from 'react';
import { ChevronLeft, Search, Filter, MoreHorizontal, Video } from 'lucide-react';
import { SampleGreetingService } from '@/services/SampleGreetingService';
import { useSelector } from 'react-redux';
import { selectAuth } from '@/redux/slices/auth/selectors';


export default function CelebrityGreeting() {
  const [activeTab, setActiveTab] = useState("Requests");
  const [expandedId, setExpandedId] = useState(null);
  const [expandedProjectId, setExpandedProjectId] = useState(null);
  const [showSubmitted, setShowSubmitted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("Most requested");
  const [greetings, setGreetings] = useState([]);
  const [loading, setLoading] = useState(true);
  
  const auth = useSelector(selectAuth);
  const celebrityId = auth?.userInfo?.user?.celebrityProfile?._id || 
                     auth?.userInfo?.user?._id;
  
  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
  };
  
  const handleCelebrityClick = (id) => {
    console.log(`Celebrity ${id} clicked`);
  };
  
  const handleBackClick = () => {
    console.log('Back button clicked');
  };
  
  const handleSearchClick = () => {
    console.log('Search button clicked');
  };
  
  const handleOptionsClick = (e, id) => {
    e.stopPropagation();
    setExpandedId(expandedId === id ? null : id);
    console.log(`Options for celebrity ${id} clicked`);
  };
  
  const handleVideoRequest = () => {
    console.log('Video greeting requested');
  };

  // Fetch the greetings when component mounts
  useEffect(() => {
    const fetchGreetings = async () => {
      if (!celebrityId) {
        setLoading(false);
        return;
      }
      
      try {
        const response = await SampleGreetingService.getSampleGreetings(celebrityId);
        console.log('Fetched greetings:', response.data);
        if (response?.data) {
          setGreetings(Array.isArray(response.data) ? response.data : [response.data]);
        }
      } catch (error) {
        console.error('Error fetching greetings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGreetings();
  }, [celebrityId]);

  if (showSubmitted) {
    return (
      <CongratsGreeting setShowSubmitted={setShowSubmitted} />
    );
  }
  
  // Fallback sample data if no greetings are fetched
const sampleCelebrities = [
  {
    id: '1',
    name: 'Jean Mark Smith',
    imageUrl: '/images/photos/image1.jpg',
    tags: [
      { text: 'Birthday' },
      { text: 'Friendly' }
    ]
  },
  {
    id: '2',
    name: 'Cooper Anderson',
    imageUrl: '/images/photos/image2.jpg',
    tags: [
      { text: 'Anniversary' },
      { text: 'Funny' }
    ]
  },
  {
    id: '3',
    name: 'James Wright',
    imageUrl: '/images/photos/image3.jpg',
    tags: [
      { text: 'Just Because' },
      { text: 'Confident' }
    ]
  },
  {
    id: '4',
    name: 'Jean Mark Smith',
    imageUrl: '/images/photos/image4.jpg',
    tags: [
      { text: 'Birthday' },
      { text: 'Friendly' }
    ]
  },
  {
    id: '5',
    name: 'Cooper Anderson',
    imageUrl: '/images/photos/image5.jpg',
    tags: [
      { text: 'Anniversary' },
      { text: 'Funny' }
    ]
  },
  {
    id: '6',
    name: 'James Wright',
    imageUrl: '/images/photos/image6.jpg',
    tags: [
      { text: 'Just Because' },
      { text: 'Confident' }
    ]
  }
];
const categories = ["Most requested", "Birthdays", "Top performing"];

  return (
  <div className="min-h-screen bg-black flex flex-col items-center py-6 px-4 text-white" style={{ fontFamily: 'AG Book Pro, sans-serif' }}>
      <div className="w-full max-w-md flex flex-col gap-4">
        <div className="flex items-center justify-between w-full py-2">
          <button 
            className="p-1 rounded-full hover:bg-white/10 transition-colors"
            onClick={handleBackClick}
          >
            <ChevronLeft size={24} className="text-white" />
          </button>
          
          <p className="font-semibold text-white">Your greetings</p>
          
          <button 
            className="p-1 rounded-full hover:bg-white/10 transition-colors"
            onClick={handleSearchClick}
          >
            <Search size={24} className="text-white" />
          </button>
        </div>
        
        {/* Filter Section */}
        <div className="w-full mt-4">
          <div className="flex justify-between items-center mb-3">
            <h2 className="text-base font-semibold text-white">Fan Requests</h2>
            <button className="p-1">
              <Filter size={20} className="text-white" />
            </button>
          </div>
          
          <div className="flex overflow-x-auto pb-4 hide-scrollbar">
            <div className="flex gap-2 items-center">
              {categories.map((category, index) => (
                <button
                  key={index}
                  className={`flex justify-center items-center text-xs rounded-full border border-white/30 px-3 py-1.5 whitespace-nowrap transition-all duration-200 ${
                    selectedCategory === category 
                      ? 'bg-white/20 text-white' 
                      : 'bg-black/40 text-white/80 hover:bg-white/10'
                  }`}
                  onClick={() => handleCategorySelect(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {loading && (
          <div className="w-full flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-3 w-full mt-2">
  {(loading ? sampleCelebrities : greetings).map((celebrity) => (
    <div 
      key={celebrity.id || celebrity._id}
      className="relative rounded-xl overflow-hidden bg-black/40 w-full aspect-[4/5] cursor-pointer transform transition-transform duration-200 hover:scale-[1.02]"
      onClick={() => handleCelebrityClick(celebrity.id || celebrity._id)}
    >
      <div className="absolute inset-0 w-full h-full">
        <img 
          src={celebrity.imageUrl || celebrity.avatarImageUrl} 
          alt={celebrity.name || celebrity.title} 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-transparent to-black/80"></div>
      </div>

      <div className="absolute top-2 right-2 z-10">
        <div className="relative">
          <button 
            className="p-1 rounded-full bg-black/40 backdrop-blur-sm text-white hover:bg-black/60 transition-colors"
            onClick={(e) => handleOptionsClick(e, celebrity.id || celebrity._id)}
          >
            <MoreHorizontal size={18} />
          </button>
          
          {expandedId === (celebrity.id || celebrity._id) && (
            <div className="absolute top-full right-0 mt-1 w-36 bg-black border border-gray-800 rounded-md shadow-lg overflow-hidden z-20">
              <div className="flex flex-col text-sm">
                <button className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white">
                  View details
                </button>
                <button className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white">
                  Report
                </button>
                <button className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white">
                  Display on profile
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="absolute bottom-0 left-0 w-full p-3 z-10">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-6 h-6 rounded-full overflow-hidden border border-white/30">
            <img 
              src={celebrity.imageUrl || celebrity.avatarImageUrl} 
              alt={celebrity.name || celebrity.title} 
              className="w-full h-full object-cover" 
            />
          </div>
          <p className="text-white text-sm font-medium truncate">
            {celebrity.name || celebrity.title}
          </p>
        </div>

        <div className="flex flex-wrap gap-1.5">
          {(celebrity.tags || []).map((tag, index) => (
            <div 
              key={index}
              className="px-2 py-0.5 rounded-full text-[10px] border border-white/30 text-white/90"
            >
              {typeof tag === 'string' ? tag : tag.text}
            </div>
          ))}
        </div>
      </div>
    </div>
  ))}
</div>

      </div>
      
      <div className="fixed bottom-0 left-0 right-0 p-4">
        <button 
          className="flex items-center justify-center gap-2 w-full py-3 px-4 bg-purple-600 rounded-full text-white font-medium transition-all duration-200 hover:bg-purple-700 active:scale-[0.98]"
          onClick={handleVideoRequest}
        >
          <Video size={18} />
          <span>Video greetings for celebrity</span>
        </button>
      </div>
    </div>
  );
} 








