import Link from 'next/link'

export default function Terms() {
  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-4 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              Terms of Service
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              Last updated: March 15, 2024
            </p>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-4 sm:py-16">
        <div className="mx-auto max-w-3xl">
          <div className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-bold text-white">1. Agreement to Terms</h2>
            <p className="text-gray-300">
              By accessing or using CelebGreetings, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using or accessing this platform.
            </p>

            <h2 className="text-2xl font-bold text-white mt-12">2. Use License</h2>
            <p className="text-gray-300">
              Permission is granted to temporarily use CelebGreetings for personal, non-commercial purposes. This license does not include:
            </p>
            <ul className="list-disc pl-6 text-gray-300">
              <li>Modifying or copying the materials</li>
              <li>Using the materials for commercial purposes</li>
              <li>Attempting to reverse engineer any software</li>
              <li>Removing any copyright or proprietary notations</li>
              <li>Transferring the materials to another person</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-12">3. User Accounts</h2>
            <p className="text-gray-300">
              When you create an account with us, you must provide accurate and complete information. You are responsible for:
            </p>
            <ul className="list-disc pl-6 text-gray-300">
              <li>Maintaining the security of your account</li>
              <li>All activities that occur under your account</li>
              <li>Notifying us of any unauthorized use</li>
              <li>Ensuring you exit from your account at the end of each session</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-12">4. Video Message Services</h2>
            <p className="text-gray-300">
              Our platform facilitates the creation and delivery of personalized video messages. By using this service, you agree to:
            </p>
            <ul className="list-disc pl-6 text-gray-300">
              <li>Provide accurate information for video messages</li>
              <li>Respect celebrity content rights</li>
              <li>Use the service for appropriate purposes</li>
              <li>Not request inappropriate content</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-12">5. Payment Terms</h2>
            <p className="text-gray-300">
              All payments are processed securely through our payment partners. You agree to:
            </p>
            <ul className="list-disc pl-6 text-gray-300">
              <li>Provide accurate payment information</li>
              <li>Pay all fees associated with your use of the service</li>
              <li>Understand that all payments are non-refundable unless specified</li>
              <li>Accept our pricing and payment terms</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-12">6. Intellectual Property</h2>
            <p className="text-gray-300">
              The platform and its original content, features, and functionality are owned by CelebGreetings and are protected by international copyright, trademark, and other intellectual property laws.
            </p>

            <h2 className="text-2xl font-bold text-white mt-12">7. Limitation of Liability</h2>
            <p className="text-gray-300">
              In no event shall CelebGreetings be liable for any damages arising out of the use or inability to use our platform, even if we have been notified of the possibility of such damages.
            </p>

            <h2 className="text-2xl font-bold text-white mt-12">8. Disclaimer</h2>
            <p className="text-gray-300">
              The materials on our platform are provided on an 'as is' basis. CelebGreetings makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation:
            </p>
            <ul className="list-disc pl-6 text-gray-300">
              <li>Implied warranties of merchantability</li>
              <li>Fitness for a particular purpose</li>
              <li>Non-infringement of intellectual property</li>
              <li>Accuracy of materials</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-12">9. Governing Law</h2>
            <p className="text-gray-300">
              These terms shall be governed by and construed in accordance with the laws of the United States, without regard to its conflict of law provisions.
            </p>

            <h2 className="text-2xl font-bold text-white mt-12">10. Changes to Terms</h2>
            <p className="text-gray-300">
              We reserve the right to modify these terms at any time. We will notify users of any changes by updating the "Last updated" date of these terms.
            </p>

            <h2 className="text-2xl font-bold text-white mt-12">11. Contact Information</h2>
            <p className="text-gray-300">
              If you have any questions about these Terms, please contact us at:
            </p>
            <ul className="list-none pl-6 text-gray-300">
              <li>Email: <EMAIL></li>
              <li>Phone: +****************</li>
              <li>Address: 545 Mavis Island, Chicago, IL 99191</li>
            </ul>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-[#0c0c0c]">
        <div className="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="relative isolate overflow-hidden bgGray px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Need Help Understanding?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              If you have any questions about our terms of service, our team is here to help.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/contact"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Contact Us
              </Link>
              <Link href="/privacy" className="text-sm font-semibold leading-6 text-white">
                View Privacy Policy <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 