"use client"
import LandingPage from '../components/home/<USER>'
import { selectAuth } from "@/redux/slices/auth/selectors";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { useEffect } from 'react';
export default function Home() {
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const router = useRouter();
  useEffect(() => {
    if(auth?.isLogin)
    {
      if (user?.role === "celebrity") {
        router.push("/celebrity/home");
      } else if (user?.role === "customer") {
      router.push("/customer/home");  
      }
    }
  }, []);
  return (
    <div>
      <LandingPage />
    </div>
  )
}
