import Link from 'next/link'

export default function About() {
  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-4 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              About Us
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              We're on a mission to create meaningful connections between celebrities and their fans through personalized video messages.
            </p>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-6 sm:py-32">
        <div className="mx-auto max-w-2xl lg:max-w-none">
          <div className="grid grid-cols-1 gap-y-16 lg:gap-x-8">
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-white">Our Story</h2>
              <p className="mt-6 text-lg leading-8 text-gray-300">
              droppGroup is a leading global Web3 technology company revolutionizing the AI and blockchain landscapes. We specialize in digitally transforming major global organizations and providing cutting-edge platforms for the broader creator economy.
              </p>
              <p className="mt-6 text-lg leading-8 text-gray-300">
              We proudly serve a prestigious portfolio of corporate clients and partners, including the Government of Saudi Arabia, Saudi Aramco, AWS, Cisco and NVIDIA. Our solutions are designed to enhance value and drive community growth.
              </p>
            </div>

          </div>
        </div>
      </div>

      {/* Values Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-6 sm:py-32">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-white">Our Values</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            What drives us forward
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {[
              {
                name: 'Authenticity',
                description: 'We believe in genuine connections. Every video message is authentic and personal, creating real moments between celebrities and fans.',
              },
              {
                name: 'Accessibility',
                description: "We're making celebrity interactions more accessible to everyone. Our platform connects fans with their favorite celebrities in a meaningful way.",
              },
              {
                name: 'Innovation',
                description: "We're constantly innovating to improve the experience for both celebrities and fans, making the process seamless and enjoyable.",
              },
            ].map((value) => (
              <div key={value.name} className="flex flex-col">
                <dt className="text-xl font-semibold leading-7 text-white">{value.name}</dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-300">
                  <p className="flex-auto">{value.description}</p>
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* Team Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-6 sm:py-32">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-white">Our Team</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Meet the people behind CelebGreetings
          </p>
        </div>
        <ul className="mx-auto mt-20 grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3 justify-center">
          {[
            {
              name: 'JFaisal Al Monai',
              role: 'Co-Founder & Chief Executive Officer',
              imageUrl: '/images/team/faisal.webp',
              bio: 'Former tech executive with a passion for connecting people through technology.',
            },
            {
              name: 'Gurps Rai',
              role: 'CTO & Co-founder',
              imageUrl: '/images/team/gurps.webp',
              bio: 'Tech innovator with experience in building scalable platforms.',
            },
            {
              name: 'Christopher Kelly',
              role: 'Co-Founder & President',
              imageUrl: '/images/team/christopher.webp',
              bio: 'Entertainment industry veteran with extensive celebrity network.',
            },
          ].map((person) => (
            <li key={person.name} className='text-center'>
              <div className="overflow-hidden rounded-2xl ">
                <img
                  src={person.imageUrl}
                  alt={person.name}
                  className="object-cover object-center m-auto"
                />
              </div>
              <h3 className="mt-6 text-lg font-semibold leading-8 text-white text">{person.name}</h3>
              <p className="text-base leading-7 text-gray-300">{person.role}</p>
              <p className="mt-4 text-base leading-7 text-gray-300 hidden">{person.bio}</p>
            </li>
          ))}
        </ul>
      </div>

      {/* CTA Section */}
      <div className="bg-[#0c0c0c]">
        <div className="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="relative isolate overflow-hidden bgGray px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Join Our Mission
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              Whether you're a celebrity looking to connect with fans or a fan wanting to get a personalized message, we'd love to have you on board.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/celebrity"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Get Started
              </Link>
              <Link href="/contact" className="text-sm font-semibold leading-6 text-white">
                Contact Us <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 