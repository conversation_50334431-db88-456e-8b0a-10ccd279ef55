"use client";

import { LoaderVariants } from "@/components/common/Loader";
import Step1 from "@/components/VerifyIdentity/Step1";
import Step2 from "@/components/VerifyIdentity/Step2";
import Step3 from "@/components/VerifyIdentity/Step3";
import Step4 from "@/components/VerifyIdentity/Step4";
import Step5 from "@/components/VerifyIdentity/Step5";
import Step6 from "@/components/VerifyIdentity/Step6";
import Step8 from "@/components/VerifyIdentity/Step8";
import useAuthGuard from "@/hooks/useAuthGuard";
import { Form, Formik } from "formik";
import { Loader } from "lucide-react";
import Image from "next/image";
import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { submitKycDocument } from "@/redux/slices/identity-verification/thunks";
import { resetState } from "@/redux/slices/identity-verification/slice";
import { selectAuth } from "@/redux/slices/auth/selectors";
import {
  selectIdentityVerification,
  selectIdentityVerificationSuccess,
} from "@/redux/slices/identity-verification/selectors";
import useToaster from "@/hooks/useToaster";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";

const page = () => {
  const { showErrorToast, showSuccessToast } = useToaster();
  const handleApiError = useApiErrorHandler();
  const isLoading = useAuthGuard();
  const [currentStep, setCurrentStep] = useState(0);
  const dispatch = useDispatch();
  const auth = useSelector(selectAuth);
  const identityVerification = useSelector(selectIdentityVerification);
  const verificationSuccess = useSelector(selectIdentityVerificationSuccess);
  const user = auth?.userInfo?.user;
  const lastErrorMessageRef = useRef("");
  const [loading, setLoading] = useState(false);

  const steps = [
    {
      title: "Verify Your Identity",
      description:
        "To keep our platform secure and authentic, please complete the steps below",
      component: Step1,
    },
    {
      title: "Government-issued ID",
      description: "Select the country where your ID was issued",
      component: Step2,
    },
    {
      camera: true,
      component: Step3,
    },
    {
      title: "ID document",
      description:
        "Make sure all information in the document is visible and easy to read",
      component: Step4,
    },
    {
      camera: true,
      component: Step5,
    },
    {
      title: "Terms of Use & Rights Waiver",
      description:
        "To protect your identity and ensure your content is used responsibly, please review and confirm the following:",
      component: Step6,
    },
    {
      title: "Verification Complete",
      description: "",
      component: Step8,
    },
  ];

  useEffect(() => {
    localStorage.removeItem("identityVerificationError");
    lastErrorMessageRef.current = "";
    dispatch(resetState());
  }, [dispatch]);

  useEffect(() => {
    const handleNextStep = () => {
      lastErrorMessageRef.current = "";

      if (currentStep === 3) {
        setCurrentStep(4);
      } else {
        setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
      }
    };

    const handlePrevStep = () => {
      lastErrorMessageRef.current = "";

      if (currentStep === 3) {
        setCurrentStep(2);
      } else {
        setCurrentStep((prev) => Math.max(prev - 1, 0));
      }
    };

    document.addEventListener("nextStep", handleNextStep);
    document.addEventListener("prevStep", handlePrevStep);

    return () => {
      document.removeEventListener("nextStep", handleNextStep);
      document.removeEventListener("prevStep", handlePrevStep);
    };
  }, [currentStep, steps.length]);

  useEffect(() => {
    if (verificationSuccess) {
      setCurrentStep(steps.length - 1);
    } else if (identityVerification.error) {
      showErrorToast(
        `Verification failed: ${
          identityVerification.error.message || "Please try again"
        }`
      );
    }
  }, [verificationSuccess, identityVerification.error, steps.length]);

  const progress = ((currentStep + 1) / steps.length) * 100;
  const isCameraStep = steps[currentStep].camera;
  const StepComponent = steps[currentStep].component;

  const initialValues = {
    idType: { label: "National ID", value: "national_id" },
    country: "United Kingdom",
    idImage: null,
    selfieVideo: null,
    terms: {
      authorize: false,
      confirm: false,
      agree: false,
    },
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      if (!user || !user.id) {
        showErrorToast("User information not found. Please log in again.");
        return;
      }

      if (!values.idType) {
        showErrorToast("Please select an ID document type");
        return;
      }

      let hasValidDocType = false;
      if (typeof values.idType === "object" && values.idType.value) {
        hasValidDocType = true;
      } else if (
        typeof values.idType === "string" &&
        values.idType.trim() !== ""
      ) {
        hasValidDocType = true;
      }

      if (!hasValidDocType) {
        showErrorToast(
          "Invalid document type format. Please select a document type again."
        );
        return;
      }

      if (!values.country) {
        showErrorToast("Please select a country");
        return;
      }

      if (!values.idImage) {
        if (currentStep >= 3) {
          showErrorToast("Please capture an image of your ID document");
          setCurrentStep(2);
        }
        return;
      }

      if (!values.selfieVideo) {
        if (currentStep >= 5) {
          showErrorToast("Please record a selfie video");
          setCurrentStep(4);
        }
        return;
      }

      if (
        values.selfieVideo &&
        typeof values.selfieVideo === "string" &&
        !values.selfieVideo.startsWith("data:")
      ) {
        showErrorToast("Invalid selfie video format. Please record again.");
        setCurrentStep(4);
        return;
      }

      if (
        !values.terms.authorize ||
        !values.terms.confirm ||
        !values.terms.agree
      ) {
        showErrorToast("Please accept all terms and conditions");
        return;
      }
      const celebrityId =
        user.celebrityProfile?.id || user.celebrityProfile?._id;
      const token = auth?.userInfo?.accessToken;
      if (token) {
        localStorage.setItem("accessToken", token);
      }

      let documentType = "national_id";

      if (values.idType) {
        if (typeof values.idType === "object" && values.idType.value) {
          documentType = values.idType.value;
        } else if (typeof values.idType === "string") {
          switch (values.idType) {
            case "Passport":
              documentType = "passport";
              break;
            case "Driver's License":
            case "Driving license":
              documentType = "drivers_license";
              break;
            case "National ID":
            case "ID document":
            default:
              documentType = "national_id";
              break;
          }
        }
      }

      const kycData = {
        celebrityId: celebrityId,
        documentType: documentType,
        country: values.country,
        document: values.idImage,
        token: token,
      };

      const result = await dispatch(submitKycDocument(kycData));

      if (submitKycDocument.fulfilled.match(result)) {
        showSuccessToast("Identity verification submitted successfully!");
      } else {
        console.error("Error submitting identity verification:", result.error);

        if (result.error && result.error.message) {
          const errorPayload = result.error.payload;
          if (errorPayload && errorPayload.data) {
            if (errorPayload.data.message) {
              showErrorToast(`Error: ${errorPayload.data.message}`);
            } else if (errorPayload.data.error) {
              showErrorToast(`Error: ${errorPayload.data.error}`);
            } else {
              showErrorToast(`Error: ${result.error.message}`);
            }
          } else {
            showErrorToast(`Error: ${result.error.message}`);
          }
        } else {
          showErrorToast(
            "Failed to submit identity verification. Please try again."
          );
        }
      }
    } catch (error) {
      handleApiError(error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleNext = () => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader variant={LoaderVariants.rotatingLines} size={48} />
      </div>
    );
  }

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ values, errors, touched }) => (
        <Form className="min-h-screen bg-gradient-custom text-white flex flex-col items-center w-full overflow-x-hidden relative">
          {!isCameraStep && currentStep !== steps.length - 1 && (
            <>
              <div className="pt-10 w-full flex justify-between items-center mb-4 max-w-md">
                <button
                  type="button"
                  onClick={handlePrev}
                  className="text-sm cursor-pointer"
                  disabled={currentStep === 0}
                >
                  <Image
                    src="/left-arrow.svg"
                    width={20}
                    height={20}
                    alt="Back"
                    className={
                      currentStep === 0
                        ? "opacity-50 cursor-not-allowed"
                        : "cursor-pointer"
                    }
                  />
                </button>
                <h2 className="text-base font-medium w-full text-center">
                  Verify Your Identity
                </h2>
              </div>

              <div className="w-full max-w-md bg-gray-900 rounded-full h-2 mb-6">
                <div
                  className="bg-[#7C7C7C] h-2 rounded-full"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </>
          )}

          {isCameraStep ? (
            <StepComponent values={values} errors={errors} touched={touched} />
          ) : currentStep === steps.length - 1 ? (
            <StepComponent
              values={values}
              errors={errors}
              touched={touched}
              setCurrentStep={setCurrentStep}
            />
          ) : (
            <div className="px-6">
              <div className="bg-white text-black rounded-2xl shadow-xl w-full max-w-md p-4 sm:p-6 mb-safe">
                <div className="mb-4 sm:mb-6">
                  <h3 className="text-center text-xl font-medium mb-0 leading-[160%]">
                    {steps[currentStep].title}
                  </h3>
                  <p className="text-center text-xs text-[#6C7278] tracking-[-2%] leading-[160%]">
                    {steps[currentStep].description}
                  </p>
                </div>
                <StepComponent
                  values={values}
                  errors={errors}
                  touched={touched}
                  loading={loading}
                />

                {currentStep !== 1 &&
                  currentStep !== 3 &&
                  currentStep !== 4 &&
                  currentStep !== 5 &&
                  currentStep !== 6 && (
                    <div className="mt-8 flex justify-center w-full sticky bottom-0 pb-4 sm:pb-0 sm:static">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          handleNext();
                        }}
                        className="flex w-full max-w-[287px] justify-center rounded-lg text-white py-4 cursor-pointer"
                        style={{
                          borderRadius: "8px",
                          border: "1px solid #000",
                          background:
                            "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
                          boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)",
                        }}
                      >
                        {currentStep === 0 ? "Accept and Proceed" : "Continue"}
                      </button>
                    </div>
                  )}
              </div>
            </div>
          )}
        </Form>
      )}
    </Formik>
  );
};

export default page;
