"use client";

import React, { useEffect, useState } from "react";
import CelebrityDetail from "@/components/celebrity/CelebrityDetail";
import { useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import useAuthGuard from "@/hooks/useAuthGuard";
import { LoaderVariants, Loader } from "@/components/common/Loader";
import CustomerDetail from "@/components/CustomerDetail";
import useApiHook from "@/hooks/useApiHook";
import PageLoader from "@/components/common/PageLoader";

const page = () => {
  const { handleApiCall, isApiLoading } = useApiHook();
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const role = user?.role;
  const celebrity = user?.celebrityProfile;
  const [categories, setCategories] = useState([]);
  const isLoading = useAuthGuard();
  const [celebrities, setCelebrities] = useState([]);

  useEffect(() => {
    fetchCelebrities();
  }, []);

  const celebrityMap = {
    _id: celebrity?._id,
    avatarUrl: [user?.profilePhotoUrl],
    name: `${user?.firstName} ${user?.lastName}`,
    price: celebrity?.price,
    bio: celebrity?.bio,
    profession: celebrity?.profession,
    verified: celebrity?.isVerified,
    socialLinks: celebrity?.socialLinks,
    ratingSum: celebrity?.ratingSum,
    review: "4",
    profession: celebrity?.profession,
    bio: celebrity?.bio,
    socialLinks: celebrity?.socialLinks,
    categories: celebrity?.categories,
    usageCount: celebrity?.usageCount,
    userId: {
      firstName: user?.firstName,
      lastName: user?.lastName,
    },
  };

  const fetchCelebrities = async () => {
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities`,
      });

      if (resp.status === 200) {
        setCelebrities(resp.data);
      }
    } catch (error) {
      console.error("Dashboard fetch error:", error);
    }
  };

  const getAllCategoris = async () => {
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/categories/`,
      });
      if (resp.status === 200) {
        setCategories(resp.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchCelebrities();
    getAllCategoris();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader variant={LoaderVariants.rotatingLines} size={48} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-custom-2 text-white">
      {isApiLoading && <PageLoader />}
      {role === "celebrity" ? (
        <CelebrityDetail
          celebrity={celebrityMap}
          categories={categories}
          isProfile={true}
        />
      ) : (
        <CustomerDetail celebrities={celebrities} user={user} />
      )}
    </div>
  );
};

export default page;
