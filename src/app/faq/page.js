'use client';

import { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

const faqSections = [
  {
    title: 'Getting Started',
    questions: [
      {
        question: 'How do I request a celebrity greeting?',
        answer: 'To request a celebrity greeting, simply navigate to the celebrity\'s profile, select your preferred package, and follow the checkout process. You\'ll be able to provide the script and any specific requirements during the order process.'
      },
      {
        question: 'What types of greetings can I request?',
        answer: 'You can request various types of greetings including birthday wishes, congratulations, thank you messages, and personalized messages. Each celebrity may have different offerings available.'
      },
      {
        question: 'How long does it take to receive my greeting?',
        answer: 'Processing times vary depending on the celebrity\'s schedule and the complexity of your request. Typically, greetings are delivered within 24-48 hours after the celebrity accepts the request.'
      }
    ]
  },
  {
    title: 'Pricing & Payments',
    questions: [
      {
        question: 'How much does a celebrity greeting cost?',
        answer: 'Pricing varies by celebrity and the type of greeting requested. Basic greetings start at $99, while premium packages with additional features may cost more. You can view specific pricing on each celebrity\'s profile.'
      },
      {
        question: 'What payment methods do you accept?',
        answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and other secure payment methods. All transactions are encrypted and secure.'
      },
      {
        question: 'Is there a refund policy?',
        answer: 'Yes, we offer a 100% satisfaction guarantee. If you\'re not satisfied with your greeting, you can request a refund within 7 days of receiving your video. Please note that processing fees may apply.'
      }
    ]
  },
  {
    title: 'For Celebrities',
    questions: [
      {
        question: 'How do I become a celebrity on the platform?',
        answer: 'To join as a celebrity, you\'ll need to apply through our verification process. This includes providing proof of identity, professional credentials, and agreeing to our terms of service. Our team will review your application within 2-3 business days.'
      },
      {
        question: 'How do I manage my greeting requests?',
        answer: 'As a celebrity, you\'ll have access to a dashboard where you can view, accept, and manage all greeting requests. You can set your availability, customize your offerings, and track your earnings.'
      },
      {
        question: 'How do I get paid?',
        answer: 'Payments are processed automatically after each completed greeting. You can set up your preferred payment method in your account settings. Payments are typically processed within 3-5 business days.'
      }
    ]
  },
  {
    title: 'Technical Support',
    questions: [
      {
        question: 'What if I have technical issues?',
        answer: 'If you encounter any technical issues, please contact our support team through the help center. We typically respond within 24 hours and can assist with any platform-related problems.'
      },
      {
        question: 'How do I download my greeting video?',
        answer: 'Once your greeting is ready, you\'ll receive a notification. You can then download the video directly from your dashboard or through the link provided in your email.'
      },
      {
        question: 'What video formats are supported?',
        answer: 'Our platform supports all major video formats including MP4, MOV, and AVI. Videos are typically delivered in HD quality (1080p) unless specified otherwise in your package.'
      }
    ]
  }
];

export default function FAQPage() {
  const [openSections, setOpenSections] = useState({});

  const toggleSection = (sectionIndex) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionIndex]: !prev[sectionIndex]
    }));
  };

  return (
    <div className="min-h-screen bg-[#0c0c0c] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h1>
          <p className="text-gray-400 text-lg">
            Find answers to common questions about our celebrity greeting service
          </p>
        </div>

        <div className="space-y-6">
          {faqSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="bg-[#1a1a1a] rounded-lg overflow-hidden">
              <button
                onClick={() => toggleSection(sectionIndex)}
                className="w-full cursor-pointer px-6 py-4 flex items-center justify-between text-left bg-[#2a2a2a] hover:bg-[#333333] transition-colors"
              >
                <h2 className="text-xl font-semibold text-white">{section.title}</h2>
                <ChevronDownIcon
                  className={`h-6 w-6 text-gray-400 transform transition-transform ${
                    openSections[sectionIndex] ? 'rotate-180' : ''
                  }`}
                />
              </button>

              {openSections[sectionIndex] && (
                <div className="px-6 py-4 space-y-4">
                  {section.questions.map((item, index) => (
                    <div key={index} className="border-b border-gray-700 last:border-0 pb-4 last:pb-0">
                      <h3 className="text-lg font-medium text-white mb-2">{item.question}</h3>
                      <p className="text-gray-400">{item.answer}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-gray-400">
            Still have questions?{' '}
            <a href="/contact" className="text-indigo-500 hover:text-indigo-400">
              Contact our support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
} 