"use client";

import { useState } from "react";

// Mock data for subscription plans
const subscriptionPlans = [
  {
    id: "basic",
    name: "Basic",
    price: 9.99,
    interval: "month",
    features: [
      "5 greeting requests per month",
      "Standard video quality",
      "Basic customer support",
      "Email notifications",
    ],
  },
  {
    id: "premium",
    name: "Premium",
    price: 19.99,
    interval: "month",
    features: [
      "15 greeting requests per month",
      "HD video quality",
      "Priority customer support",
      "Email & SMS notifications",
      "Early access to new celebrities",
      "Custom video effects",
    ],
    popular: true,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: 49.99,
    interval: "month",
    features: [
      "Unlimited greeting requests",
      "4K video quality",
      "24/7 dedicated support",
      "Custom branding options",
      "API access",
      "Advanced analytics",
      "Team management",
    ],
  },
];

// Mock data for payment history
const mockPaymentHistory = [
  {
    id: 1,
    date: "2024-03-15",
    amount: 19.99,
    status: "completed",
    description: "Premium Plan - Monthly Subscription",
    paymentMethod: "Visa ending in 4242",
  },
  {
    id: 2,
    date: "2024-02-15",
    amount: 19.99,
    status: "completed",
    description: "Premium Plan - Monthly Subscription",
    paymentMethod: "Visa ending in 4242",
  },
  {
    id: 3,
    date: "2024-01-15",
    amount: 19.99,
    status: "completed",
    description: "Premium Plan - Monthly Subscription",
    paymentMethod: "Visa ending in 4242",
  },
];

export default function CustomerSubscriptionsPage() {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [billingInterval, setBillingInterval] = useState("month");

  const handleSubscribe = (planId) => {};

  const handleCancelSubscription = () => {};

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Subscription Plans
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Choose a plan that best fits your needs
          </p>
        </div>

        {/* Billing Interval Toggle */}
        <div className="mb-8 flex justify-center">
          <div className="relative bg-white rounded-lg p-1 flex">
            <button
              onClick={() => setBillingInterval("month")}
              className={`relative w-32 rounded-md py-2 text-sm font-medium ${
                billingInterval === "month"
                  ? "bg-indigo-600 text-white"
                  : "text-gray-700 hover:text-gray-900"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval("year")}
              className={`relative w-32 rounded-md py-2 text-sm font-medium ${
                billingInterval === "year"
                  ? "bg-indigo-600 text-white"
                  : "text-gray-700 hover:text-gray-900"
              }`}
            >
              Yearly
              <span className="absolute -top-2 -right-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                Save 20%
              </span>
            </button>
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {subscriptionPlans.map((plan) => (
            <div
              key={plan.id}
              className={`bg-white rounded-lg shadow-lg overflow-hidden ${
                plan.popular ? "ring-2 ring-indigo-600" : ""
              }`}
            >
              {plan.popular && (
                <div className="bg-indigo-600 text-white text-center py-1 text-sm font-medium">
                  Most Popular
                </div>
              )}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  {plan.name}
                </h3>
                <p className="mt-4">
                  <span className="text-4xl font-bold text-gray-900">
                    $
                    {billingInterval === "year"
                      ? (plan.price * 0.8 * 12).toFixed(2)
                      : plan.price}
                  </span>
                  <span className="text-gray-500">/{billingInterval}</span>
                </p>
                <ul className="mt-6 space-y-4">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <svg
                        className="h-6 w-6 text-green-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="ml-3 text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button
                  onClick={() => handleSubscribe(plan.id)}
                  className={`mt-8 w-full rounded-md px-4 py-2 text-sm font-medium ${
                    plan.popular
                      ? "bg-indigo-600 text-white hover:bg-indigo-700"
                      : "bg-white text-indigo-600 border border-indigo-600 hover:bg-indigo-50"
                  }`}
                >
                  Subscribe Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Payment History */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Payment History
          </h2>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Method
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mockPaymentHistory.map((payment) => (
                    <tr key={payment.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {payment.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {payment.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${payment.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex rounded-full px-2 text-xs font-semibold leading-5 bg-green-100 text-green-800">
                          {payment.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {payment.paymentMethod}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Current Subscription */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Current Subscription
          </h2>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Premium Plan</p>
              <p className="text-sm text-gray-500">
                Next billing date: April 15, 2024
              </p>
            </div>
            <button
              onClick={handleCancelSubscription}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Cancel Subscription
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
