"use client";

import { useState } from "react";
import Link from "next/link";

// Mock data for completed greetings
const mockCompletedGreetings = [
  {
    id: 1,
    celebrity: {
      id: 1,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Actor",
    },
    date: "2024-03-15",
    occasion: "birthday",
    recipientName: "<PERSON>",
    price: 99,
    videoUrl: "https://example.com/video1",
    duration: "1:30",
    rating: 5,
    review: "Amazing personalized message!",
  },
  {
    id: 2,
    celebrity: {
      id: 2,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Musician",
    },
    date: "2024-03-10",
    occasion: "anniversary",
    recipientName: "<PERSON>",
    price: 149,
    videoUrl: "https://example.com/video2",
    duration: "2:15",
    rating: 4,
    review: "Great experience, very professional",
  },
  {
    id: 3,
    celebrity: {
      id: 3,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Athlete",
    },
    date: "2024-03-05",
    occasion: "graduation",
    recipientName: "<PERSON> White",
    price: 199,
    videoUrl: "https://example.com/video3",
    duration: "1:45",
    rating: 5,
    review: "Perfect for the occasion!",
  },
];

export default function CustomerHistoryPage() {
  const [sortBy, setSortBy] = useState("date");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredGreetings = mockCompletedGreetings
    .filter((greeting) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        greeting.celebrity.name.toLowerCase().includes(searchLower) ||
        greeting.recipientName.toLowerCase().includes(searchLower) ||
        greeting.occasion.toLowerCase().includes(searchLower)
      );
    })
    .sort((a, b) => {
      if (sortBy === "date") return new Date(b.date) - new Date(a.date);
      if (sortBy === "price") return b.price - a.price;
      if (sortBy === "rating") return b.rating - a.rating;
      return 0;
    });

  const handleDownload = (videoUrl) => {
    // TODO: Implement video download
  };

  const handleShare = (videoUrl) => {
    // TODO: Implement video sharing
    if (navigator.share) {
      navigator.share({
        title: "Check out this celebrity greeting!",
        url: videoUrl,
      });
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(videoUrl);
      alert("Video URL copied to clipboard!");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Greeting History</h1>
          <p className="mt-2 text-sm text-gray-600">
            View and manage your completed celebrity greetings
          </p>
        </div>

        {/* Search and Sort Controls */}
        <div className="mb-6 space-y-4 sm:flex sm:space-y-0 sm:space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by celebrity, recipient, or occasion..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border border-gray-300 px-4 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <div className="sm:w-48">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="block w-full rounded-md border border-gray-300 px-4 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
              <option value="date">Sort by Date</option>
              <option value="price">Sort by Price</option>
              <option value="rating">Sort by Rating</option>
            </select>
          </div>
        </div>

        {/* Greetings Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredGreetings.map((greeting) => (
            <div
              key={greeting.id}
              className="bg-white rounded-lg shadow overflow-hidden"
            >
              <div className="aspect-w-16 aspect-h-9">
                <img
                  src={greeting.celebrity.image}
                  alt={greeting.celebrity.name}
                  className="w-full h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {greeting.celebrity.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {greeting.celebrity.category}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-indigo-600">
                      ${greeting.price}
                    </p>
                    <div className="flex items-center">
                      <span className="text-yellow-400">★</span>
                      <span className="ml-1 text-sm text-gray-600">
                        {greeting.rating}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Recipient:</span>{" "}
                    {greeting.recipientName}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Occasion:</span>{" "}
                    {greeting.occasion.charAt(0).toUpperCase() +
                      greeting.occasion.slice(1)}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Date:</span> {greeting.date}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Duration:</span>{" "}
                    {greeting.duration}
                  </p>
                </div>

                {greeting.review && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 italic">
                      "{greeting.review}"
                    </p>
                  </div>
                )}

                <div className="mt-6 flex space-x-3">
                  <Link
                    href={greeting.videoUrl}
                    className="flex-1 text-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Watch Video
                  </Link>
                  <button
                    onClick={() => handleDownload(greeting.videoUrl)}
                    className="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Download
                  </button>
                  <button
                    onClick={() => handleShare(greeting.videoUrl)}
                    className="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    Share
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results Message */}
        {filteredGreetings.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900">
              No greetings found
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              Try adjusting your search criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
