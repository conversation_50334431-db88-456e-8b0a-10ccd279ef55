"use client";

import { useEffect, useState } from "react";
import useApiHook from "@/hooks/useApiHook";
import { DocumentTextIcon, XMarkIcon } from "@heroicons/react/24/outline";

export default function CustomerDashboardPage() {
  const { handleApiCall, isApiLoading } = useApiHook();

  const [videoRequests, setVideoRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedGreeting, setSelectedGreeting] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(false);

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        const response = await handleApiCall({
          method: "GET",
          url: "digital-humans/my-requests",
        });
        setVideoRequests(response?.data);
      } catch (error) {
        console.error("Error fetching video requests:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return "Processing";
    const date = new Date(dateString);
    const formattedDate = date.toISOString().split("T")[0];
    return formattedDate;
  };

  // Get the first video request from the array (assuming there's at least one)
  const videoRequest = videoRequests.length > 0 ? videoRequests[0] : null;

  const handleViewDetails = async (id) => {
    setDetailsLoading(true);
    setShowDetails(true);
    try {
      const response = await handleApiCall({
        method: "GET",
        url: `digital-humans/${id}`,
      });
      setSelectedGreeting(response?.data);
    } catch (error) {
      console.error("Error fetching greeting details:", error);
    } finally {
      setDetailsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0c0c0c]">
      <div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <p className="mt-2 text-sm text-white">
            Overview of your greeting requests and earnings
          </p>
        </div>

        {/* Stats Grid */}
        <div className=" grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 hidden">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">
              Total Greetings
            </h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              {videoRequests?.length}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">
              Pending Requests
            </h3>
            <p className="mt-2 text-3xl font-semibold text-yellow-600">
              {videoRequests?.pendingGreetings}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
            <p className="mt-2 text-3xl font-semibold text-green-600">
              ${videoRequests?.totalRevenue}
            </p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">
              Average Rating
            </h3>
            <p className="mt-2 text-3xl font-semibold text-indigo-600">
              {videoRequests?.averageRating}
            </p>
          </div>
        </div>

        {/* Recent Greetings */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">
                Recent Greetings
              </h2>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Emotion
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {videoRequests.map((greeting, index) => (
                  <tr key={greeting._id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(greeting?.completedAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          greeting.status === "COMPLETED"
                            ? "bg-green-100 text-green-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {greeting.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {greeting.emotion}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewDetails(greeting._id)}
                        className="text-indigo-600 font-bold hover:text-indigo-900 cursor-pointer"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Details Popup */}
      {showDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-[#1a1a1a] rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-white">
                  Greeting Details
                </h2>
                <button
                  onClick={() => {
                    setShowDetails(false);
                    setSelectedGreeting(null);
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
              {detailsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
                </div>
              ) : selectedGreeting ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-6">
                  {/* Request Information */}
                  <div className="bg-[#2a2a2a] rounded-lg p-6">
                    <h3 className="text-base md:text-xl font-semibold text-white mb-4 flex items-center">
                      <DocumentTextIcon className="h-6 w-6 mr-2 text-blue-500" />
                      Request Information
                    </h3>
                    <div className="space-y-2 md:space-y-4">
                      <div>
                        <p className="text-sm text-gray-400">Script</p>
                        <p className="text-white mt-1">
                          {selectedGreeting.script}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Emotion</p>
                        <p className="text-white mt-1">
                          {selectedGreeting.emotion}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Status</p>
                        <p className="text-white mt-1">
                          {selectedGreeting.status}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">
                          Processing Started
                        </p>
                        <p className="text-white mt-1">
                          {selectedGreeting.processingStartedAt
                            ? new Date(
                                selectedGreeting.processingStartedAt
                              ).toLocaleString()
                            : "N/A"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">
                          Generation Attempts
                        </p>
                        <p className="text-white mt-1">
                          {selectedGreeting.generationAttempts}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Video Player */}
                  <div className="bg-[#2a2a2a] rounded-lg p-6">
                    {selectedGreeting.videoUrl ? (
                      <video
                        className="w-full h-auto rounded-lg mb-4"
                        controls
                        src={selectedGreeting.videoUrl}
                      ></video>
                    ) : (
                      <div className="flex items-center justify-center h-48 bg-gray-800 rounded-lg">
                        <p className="text-gray-400">
                          Video processing in progress...
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-white">Failed to load greeting details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
