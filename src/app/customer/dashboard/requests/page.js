"use client";

import { useState } from "react";
import Link from "next/link";
import GreetingRequestForm from "@/components/GreetingRequestForm";

// Mock data for requests
const mockRequests = [
  {
    id: 1,
    celebrity: {
      id: 1,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Actor",
    },
    status: "pending",
    date: "2024-03-15",
    occasion: "birthday",
    recipientName: "<PERSON> Smith",
    price: 99,
  },
  {
    id: 2,
    celebrity: {
      id: 2,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Musician",
    },
    status: "completed",
    date: "2024-03-10",
    occasion: "anniversary",
    recipientName: "<PERSON>",
    price: 149,
    videoUrl: "https://example.com/video1",
  },
  {
    id: 3,
    celebrity: {
      id: 3,
      name: "<PERSON>",
      image: "https://via.placeholder.com/150",
      category: "Athlete",
    },
    status: "in_progress",
    date: "2024-03-14",
    occasion: "graduation",
    recipientName: "<PERSON>",
    price: 199,
  },
];

export default function CustomerRequestsPage() {
  const [selectedCelebrity, setSelectedCelebrity] = useState(null);
  const [filter, setFilter] = useState("all");

  const filteredRequests = mockRequests.filter((request) => {
    if (filter === "all") return true;
    return request.status === filter;
  });

  const handleSubmitRequest = (formData) => {
    // TODO: Implement request submission
    setSelectedCelebrity(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            My Greeting Requests
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            View and manage your greeting requests
          </p>
        </div>

        {/* Filter Controls */}
        <div className="mb-6">
          <div className="flex space-x-4">
            <button
              onClick={() => setFilter("all")}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === "all"
                  ? "bg-indigo-600 text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              All Requests
            </button>
            <button
              onClick={() => setFilter("pending")}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === "pending"
                  ? "bg-indigo-600 text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              Pending
            </button>
            <button
              onClick={() => setFilter("in_progress")}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === "in_progress"
                  ? "bg-indigo-600 text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              In Progress
            </button>
            <button
              onClick={() => setFilter("completed")}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                filter === "completed"
                  ? "bg-indigo-600 text-white"
                  : "bg-white text-gray-700 hover:bg-gray-50"
              }`}
            >
              Completed
            </button>
          </div>
        </div>

        {/* Request List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Celebrity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recipient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Occasion
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRequests.map((request) => (
                  <tr key={request.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-10 flex-shrink-0">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={request.celebrity.image}
                            alt={request.celebrity.name}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {request.celebrity.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {request.celebrity.category}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {request.recipientName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {request.occasion.charAt(0).toUpperCase() +
                        request.occasion.slice(1)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {request.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                          request.status === "completed"
                            ? "bg-green-100 text-green-800"
                            : request.status === "in_progress"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {request.status
                          .split("_")
                          .map(
                            (word) =>
                              word.charAt(0).toUpperCase() + word.slice(1)
                          )
                          .join(" ")}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${request.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {request.status === "completed" ? (
                        <Link
                          href={request.videoUrl}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          View Video
                        </Link>
                      ) : (
                        <button
                          onClick={() =>
                            setSelectedCelebrity(request.celebrity)
                          }
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          View Details
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* New Request Form Modal */}
        {selectedCelebrity && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  Request Greeting from {selectedCelebrity.name}
                </h2>
                <button
                  onClick={() => setSelectedCelebrity(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Close</span>
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
              <GreetingRequestForm
                celebrity={selectedCelebrity}
                onSubmit={handleSubmitRequest}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
