"use client";
import { useParams } from "next/navigation";
import CelebrityDetail from "@/components/celebrity/CelebrityDetail";
import useApiHook from "@/hooks/useApiHook";
import PageLoader from "@/components/common/PageLoader";
import { useEffect, useState } from "react";

const CelebrityPage = () => {
  const { id } = useParams();
  const { handleApiCall, isApiLoading } = useApiHook();
  const [celebData, setCelebData] = useState(null);
  const [error, setError] = useState(false);

  const fetchCelebrity = async () => {
    if (!id) return;
    try {
      setError(false);
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities/${id}`,
      });

      if (resp.status === 200) {
        setCelebData(resp.data);
      }
    } catch (error) {
      console.error("Celebrity fetch error:", error);
      setError(true);
    }
  };

  useEffect(() => {
    fetchCelebrity();
  }, [id]);

  if (isApiLoading) return <PageLoader />;

  if (error || (!celebData && !isApiLoading)) {
    return (
      <div className="flex justify-center items-center h-screen text-white">
        Celebrity not found
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-custom-2 text-white">
      <CelebrityDetail
        celebrity={celebData}
        isProfile={false}
        fetchCelebrity={fetchCelebrity}
      />
    </div>
  );
};

export default CelebrityPage;
