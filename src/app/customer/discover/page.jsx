"use client";
import AllCelebrities from "@/components/celebrity/AllCelebrities";
import { LoaderVariants } from "@/components/common/Loader";
import PageLoader from "@/components/common/PageLoader";
import GreetingsList from "@/components/greetings/GreetingsList";
import useApiHook from "@/hooks/useApiHook";
import useAuthGuard from "@/hooks/useAuthGuard";
import { Loader, Search } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function page() {
  const isLoading = useAuthGuard();
  const { handleApiCall, isApiLoading } = useApiHook();
  const [celebrities, setCelebrities] = useState([]);
  const fetchCelebrities = async () => {
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities`,
      });

      if (resp.status === 200) {
        setCelebrities(resp.data);
      }
    } catch (error) {
      console.error("Dashboard fetch error:", error);
    }
  };

  useEffect(() => {
    fetchCelebrities();
  }, []);
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader variant={LoaderVariants.rotatingLines} size={48} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-custom-2 text-white flex flex-col items-center p-6">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="w-full flex justify-between items-center my-4 ">
          <h2 className="text-base font-medium w-full text-center">Discover</h2>
        </div>

        {/* Search Bar */}
        <div className="w-full flex items-center gap-2 px-[26.25px] py-[17.25px] mb-4 rounded-lg border border-[#ffffff50] bg-gradient-to-b from-white/5 to-transparent shadow-[0px_1px_2px_rgba(0,0,0,0.48)]">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search"
            className="outline-none w-full"
          />
        </div>

        {/* Trending Celebrities */}
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-base font-normal">Explore celebrities</h2>
          <Link href="" className="underline text-xs">
            View all
          </Link>
        </div>

        <AllCelebrities celebrities={celebrities} />

        {/* Trending Voices */}
        <h2 className="text-base font-normal my-3 ">Trending Greetings</h2>
        <GreetingsList />
      </div>
      {isApiLoading && <PageLoader />}
    </div>
  );
}
