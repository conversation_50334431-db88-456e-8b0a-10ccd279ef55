"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { useParams } from "next/navigation";
import useApiHook from "@/hooks/useApiHook";
import useAuthGuard from "@/hooks/useAuthGuard";
import PageLoader from "@/components/common/PageLoader";
import { useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import RegenerateModal from "@/components/customer/ReGenerateModal";
import Success from "@/components/Profile/Success";
import ShowPreview from "@/components/VideoCreation/ShowPreview";

const MAX_SLIDE = 220; // max horizontal drag in px (adjust as needed)

const page = ({ onPreview }) => {
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const isLoading = useAuthGuard();
  const { handleApiCall, isApiLoading } = useApiHook();
  const [celebrity, setCelebrity] = useState({});
  const [isSlideComplete, setIsSlideComplete] = useState(false);
  const [isShowControls, setIsShowControls] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);
  const [isRegenerate, setIsRegenerate] = useState(false);
  const [modalMode, setModalMode] = useState("regenerate");
  const [isEditing, setIsEditing] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showSuccessGreeting, setShowSuccessGreeting] = useState(false);

  const { id } = useParams();
  const [dragX, setDragX] = useState(0);
  const sliderRef = useRef(null);
  const draggingRef = useRef(false);
  const startXRef = useRef(0);

  const fetchCelebrity = async () => {
    if (!id) return;
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities/${id}`,
      });

      if (resp.status === 200) {
        setCelebrity(resp.data);
      }
    } catch (error) {
      console.error("Celebrity fetch error:", error);
    }
  };

  // Mouse/touch handlers
  const onDragStart = (e) => {
    draggingRef.current = true;
    startXRef.current =
      e.type === "touchstart" ? e.touches[0].clientX : e.clientX;
    e.preventDefault();
  };

  const onDragMove = (e) => {
    if (!draggingRef.current) return;
    const currentX = e.type === "touchmove" ? e.touches[0].clientX : e.clientX;
    let deltaX = currentX - startXRef.current;
    if (deltaX < 0) deltaX = 0;
    if (deltaX > MAX_SLIDE) deltaX = MAX_SLIDE;
    setDragX(deltaX);
    e.preventDefault();
  };

  const onDragEnd = () => {
    draggingRef.current = false;
    if (dragX >= MAX_SLIDE) {
      setIsSlideComplete(true);
      if (onPreview) onPreview();
    } else {
      setDragX(0);
    }
  };

  useEffect(() => {
    fetchCelebrity();
  }, [id]);

  console.log(isShowControls);

  return (
    <>
      {(isApiLoading || isLoading) && <PageLoader />}
      <div className="relative max-w-md mx-auto h-screen select-none overflow-hidden">
        {isSlideComplete ? (
          <>
            <video
              autoPlay
              loop
              muted
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
              src="/videos/preview.mp4" // Replace with your actual video path
            />

            {/* Overlay Controls */}
            <div className="absolute top-12 left-6 flex flex-col gap-3 z-50 text-white pr-12 w-full">
              {/* Back arrow */}
              <button
                onClick={() => {
                  setIsSlideComplete(false);
                  setDragX(0);
                }}
                aria-label="Back"
              >
                <Image
                  src="/left-arrow.svg"
                  width={20}
                  height={20}
                  alt="left-arrow"
                />
              </button>

              {/* Celebrity info with info icon */}
              <div className="mb-4 flex justify-between items-center w-full">
                <div className="flex gap-[10px] items-center">
                  <div className="w-12 h-12 rounded-full overflow-hidden border border-white/40">
                    <Image
                      src={
                        celebrity?.avatarUrl
                          ? celebrity.avatarUrl[0]
                          : "/person.svg"
                      }
                      alt="Celebrity Avatar"
                      width={55}
                      height={55}
                      objectFit="cover"
                    />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-xs opacity-70">connecting...</span>
                    <span className="font-bold text-lg">{`${celebrity?.userId?.firstName} ${celebrity?.userId?.lastName}`}</span>
                  </div>
                </div>
                <button
                  className="opacity-70 cursor-pointer"
                  onClick={() => setIsShowControls(!isShowControls)}
                >
                  <Image
                    src="/images/infoCircleOutlined.svg"
                    width={24}
                    height={24}
                    alt="info-circle"
                  />{" "}
                </button>
              </div>

              {/* Controls row */}
              {isShowControls && (
                <div className="flex items-center gap-8">
                  {/* Camera On / off */}
                  <button
                    className="flex flex-col items-center gap-1 cursor-pointer select-none"
                    onClick={() => setIsCameraOff(!isCameraOff)}
                  >
                    <div
                      className={`p-3 rounded-full bg-opacity-20 ${
                        isCameraOff ? "bg-black" : "bg-white"
                      }`}
                    >
                      <Image
                        src={`${
                          isCameraOff
                            ? "/images/videocam_off.svg"
                            : "/images/Video.svg"
                        }`}
                        width={20}
                        height={20}
                        alt="camera"
                      />
                    </div>
                    <span className="text-xs opacity-80">
                      Camera {isCameraOff ? "off" : "on"}
                    </span>
                  </button>

                  {/* Regenerate greeting */}
                  <button
                    className="flex flex-col items-center gap-1 cursor-pointer select-none"
                    onClick={() => setIsRegenerate(true)}
                  >
                    <div className="p-3 rounded-full bg-black bg-opacity-60">
                      <Image
                        src="/images/AI.svg"
                        width={20}
                        height={20}
                        alt="re-gemerate"
                      />{" "}
                    </div>
                    <span className="text-xs opacity-80">
                      Regenerate greeting
                    </span>
                  </button>

                  {/* Download preview */}
                  <button className="flex flex-col items-center gap-1 cursor-pointer select-none">
                    <div className="p-3 rounded-full bg-black bg-opacity-60">
                      <Image
                        src="/images/download.svg"
                        width={20}
                        height={20}
                        alt="download"
                      />{" "}
                    </div>
                    <span className="text-xs opacity-80">Download preview</span>
                  </button>

                  {/* Share preview */}
                  <button className="flex flex-col items-center gap-1 cursor-pointer select-none">
                    <div className="p-3 rounded-full bg-black bg-opacity-60">
                      <Image
                        src="/images/Share.svg"
                        width={20}
                        height={20}
                        alt="share"
                      />{" "}
                    </div>
                    <span className="text-xs opacity-80">Share preview</span>
                  </button>

                  {/* Looks great */}
                  <button
                    className="flex flex-col items-center gap-1 cursor-pointer select-none"
                    onClick={() => setShowCheckout(true)}
                  >
                    <div className="p-3 rounded-full bg-green-400">
                      <Image
                        src="/images/Check.svg"
                        width={20}
                        height={20}
                        alt="check"
                      />{" "}
                    </div>
                    <span className="text-xs opacity-80">Looks great</span>
                  </button>
                </div>
              )}
            </div>

            {/* Small user preview video bottom-right */}
            {!isCameraOff && (
              <div className="absolute bottom-46 right-6 w-30 h-26  shadow-lg z-20">
                <Image
                  src={`${user?.profilePhotoUrl}`}
                  width={120}
                  height={100}
                  className="rounded-lg"
                  alt="profile-photo"
                />
              </div>
            )}

            {/* Checkout Bottom Sheet Overlay */}
            {showCheckout && (
              <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto bg-gradient-to-t from-[#EAEAEA] to-[#EAEAEA] rounded-t-3xl p-6 shadow-lg z-50 select-none font-sans">
                {/* Header */}
                <div className="mb-6 flex items-center gap-4">
                  <button
                    onClick={() => setShowCheckout(false)}
                    aria-label="Back"
                  >
                    <Image
                      src="/right-arrow.svg"
                      alt="Arrow-Right"
                      width={12}
                      height={5}
                    />
                  </button>
                  <h2 className="text-base font-medium flex-1 text-center">
                    Checkout
                  </h2>
                  <div style={{ width: "24px" }} /> {/* spacer */}
                </div>

                {/* Payment form */}
                <div>
                  <h1 className="text-base font-medium text-[#212529] mb-4">
                    Payment{" "}
                  </h1>
                  <div className="flex flex-col gap-3 bg-[#F5F5F5] rounded-xl p-4 mb-6 shadow-sm">
                    <label className="flex items-center gap-2 text-[#212529] text-xs">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="25"
                        height="25"
                        viewBox="0 0 25 25"
                        fill="none"
                      >
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M20.6419 5.67383C20.8702 5.67826 21.0542 5.86224 21.0586 6.09049V19.4238C21.0586 19.6586 20.8683 19.8488 20.6336 19.8488H3.97526C3.7438 19.8443 3.55859 19.6553 3.55859 19.4238V6.09049C3.56303 5.86224 3.747 5.67826 3.97526 5.67383H20.6419ZM20.6419 4.42383H3.97526C3.05479 4.42383 2.30859 5.17002 2.30859 6.0905V19.4238C2.30859 20.3443 3.05479 21.0905 3.97526 21.0905H20.6419C21.5624 21.0905 22.3086 20.3443 22.3086 19.4238V6.09049C22.3086 5.17002 21.5624 4.42383 20.6419 4.42383Z"
                          fill="#212529"
                        />
                        <path
                          d="M3.27344 8.58203H21.1984"
                          stroke="#212529"
                          stroke-width="1.25"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M3.30078 11.9238H21.2258"
                          stroke="#212529"
                          stroke-width="1.25"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M6.47656 16.9238H11.4766"
                          stroke="#212529"
                          stroke-width="1.25"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M16.4766 16.9238H18.1432"
                          stroke="#212529"
                          stroke-width="1.25"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                      Add Credit / Debit Card
                    </label>

                    <input
                      type="text"
                      placeholder="Card Holder’s Name"
                      className="w-full rounded-md p-3 bg-white"
                    />
                    <input
                      type="text"
                      placeholder="Card Number"
                      className="w-full rounded-md p-3 bg-white"
                    />

                    <div className="flex flex-col gap-3">
                      <label className="text-[#212529] text-xs">
                        Expiry Date
                      </label>
                      <div className="grid grid-cols-2 gap-3">
                        <input
                          type="text"
                          placeholder="Month"
                          className="flex-1 rounded-md p-3 bg-white"
                        />
                        <input
                          type="text"
                          placeholder="Year"
                          className="flex-1 rounded-md p-3 bg-white"
                        />
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <input
                        type="text"
                        placeholder="Security Code"
                        className="flex-1 rounded-md p-3 bg-white max-w-[150px]"
                      />
                      <button aria-label="Info">
                        <Image
                          src="/images/infoCircleOutlined.svg"
                          width={18}
                          height={18}
                          alt="info"
                          className="invert"
                        />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Subtotal and Checkout Button */}
                <div className="flex justify-between items-center mb-4">
                  <p className="text-sm text-gray-900 font-semibold">
                    Subtotal
                  </p>
                  <p className="font-bold text-lg text-gray-900">$CLBX 250</p>
                </div>

                <button
                  className="mt-6 flex w-full justify-center rounded-lg text-white p-3 grayButton cursor-pointer"
                  onClick={() => {
                    setShowCheckout(false);
                    setShowSuccessGreeting(true);
                  }}
                >
                  Checkout
                </button>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Static Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: `url(${user?.profilePhotoUrl})`,
              }}
            />

            {/* Top-left overlay */}
            <div className="absolute top-12 left-6 flex items-center gap-3 text-white z-10">
              <div className="w-12 h-12 rounded-full overflow-hidden border border-white/40">
                <Image
                  src={
                    celebrity?.avatarUrl
                      ? celebrity.avatarUrl[0]
                      : "/person.svg"
                  }
                  alt="Celebrity Avatar"
                  width={55}
                  height={55}
                  objectFit="cover"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-xs opacity-70">connecting...</span>
                <span className="font-bold text-lg">{`${celebrity?.userId?.firstName} ${celebrity?.userId?.lastName}`}</span>
              </div>
            </div>

            {/* Slide to preview */}
            <div
              ref={sliderRef}
              className="absolute bottom-12 left-1/2 transform -translate-x-1/2 flex items-center rounded-full bg-[#00000060] px-6 py-2 max-w-xs w-full z-10"
              style={{ userSelect: "none" }}
              onMouseMove={onDragMove}
              onMouseUp={onDragEnd}
              onMouseLeave={onDragEnd}
              onTouchMove={onDragMove}
              onTouchEnd={onDragEnd}
              onTouchCancel={onDragEnd}
            >
              {/* Draggable Phone Icon */}
              <div
                className="cursor-pointer flex items-center justify-center p-2 rounded-[60px] border border-[rgba(216,216,216,0.05)] bg-gradient-to-br from-[rgba(248,251,255,0.04)] to-[rgba(255,255,255,0)] backdrop-blur-[15.12px]"
                style={{
                  userSelect: "none",
                  touchAction: "none",
                  transform: `translateX(${dragX}px)`,
                  transition: draggingRef.current
                    ? "none"
                    : "transform 0.3s ease",
                  boxShadow: `inset 0 0 ${
                    8 + (dragX / MAX_SLIDE) * 12
                  }px rgba(17,255,0,${
                    0.3 + (dragX / MAX_SLIDE) * 0.5
                  }), 12px 6px 24px rgba(0,0,0,0.08)`,
                  width: "64px",
                  height: "64px",
                  pointerEvents: dragX >= MAX_SLIDE ? "none" : "auto",
                }}
                onMouseDown={onDragStart}
                onTouchStart={onDragStart}
              >
                {/* Background glowing green circle SVG */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="56"
                  height="56"
                  viewBox="0 0 73 74"
                  fill="none"
                  className="relative"
                >
                  <g
                    style={{ mixBlendMode: "plus-lighter" }}
                    opacity="0.5"
                    clipPath="url(#clip0_520_8711)"
                    filter="url(#filter0_f_520_8711)"
                  >
                    <circle cx="36.2949" cy="36.9238" r="28" fill="#11FF00" />
                  </g>
                  <defs>
                    <filter
                      id="filter0_f_520_8711"
                      x="-6.04981"
                      y="-5.41992"
                      width="84.6895"
                      height="84.6885"
                      filterUnits="userSpaceOnUse"
                      colorInterpolationFilters="sRGB"
                    >
                      <feFlood floodOpacity="0" result="BackgroundImageFix" />
                      <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="BackgroundImageFix"
                        result="shape"
                      />
                      <feGaussianBlur
                        stdDeviation="9.07373"
                        result="effect1_foregroundBlur_520_8711"
                      />
                    </filter>
                    <clipPath id="clip0_520_8711">
                      <rect
                        width="48.3932"
                        height="48.3932"
                        fill="white"
                        transform="translate(12.0977 12.7275)"
                      />
                    </clipPath>
                  </defs>
                </svg>

                {/* Foreground call icon SVG */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 41 41"
                  fill="none"
                  className="absolute z-50"
                >
                  <path
                    d="M12.7832 28.002C17.6699 32.8887 23.7695 36.7383 28.7969 36.7383C31.0996 36.7383 33.0684 35.9473 34.6855 34.207C35.582 33.2578 36.25 32.0273 36.25 30.8496C36.25 30.0059 35.9336 29.1973 35.0723 28.5996L29.7109 24.8203C28.9727 24.3105 28.3223 24.0293 27.707 24.0293C26.916 24.0293 26.1777 24.4512 25.3691 25.2422L24.0859 26.4902C23.8926 26.6836 23.6641 26.7539 23.418 26.7539C23.1719 26.7539 22.9434 26.666 22.75 26.5781C21.7129 26.0332 19.8145 24.5391 18.0391 22.7461C16.2461 20.9531 14.7168 19.0898 14.207 18.0176C14.1191 17.8242 14.0312 17.5957 14.0312 17.3848C14.0312 17.1562 14.1016 16.9453 14.2949 16.752L15.543 15.4336C16.3164 14.5898 16.7383 13.8516 16.7383 13.0781C16.7383 12.4453 16.4746 11.7949 15.9473 11.0566L12.2559 5.83594C11.6406 4.97461 10.7793 4.58789 9.83008 4.58789C8.72266 4.58789 7.52734 5.13281 6.50781 6.13477C4.82031 7.76953 4.08203 9.79102 4.08203 12.0234C4.08203 17.0508 7.87891 23.0977 12.7832 28.002Z"
                    fill="white"
                  />
                </svg>
              </div>

              {/* Slide Text */}
              <span className="ml-4 text-white font-medium text-base whitespace-nowrap select-none">
                Slide to preview
              </span>
            </div>
          </>
        )}
        {isRegenerate && (
          <RegenerateModal
            onClose={() => {
              setIsRegenerate(false);
              setIsEditing(false);
              setModalMode("regenerate");
            }}
            onConfirm={() => {
              console.log("Greeting regenerated");
              setIsRegenerate(false);
              // Add regenerate greeting logic here
            }}
            modalMode={modalMode}
            setModalMode={setModalMode}
            isEditing={isEditing}
            setIsEditing={setIsEditing}
          />
        )}

        {showSuccessGreeting && (
          <ShowPreview
            isGreeting={true}
            celebrity={celebrity}
            setShowCheckout={setShowCheckout}
          />
        )}
      </div>
    </>
  );
};

export default page;
