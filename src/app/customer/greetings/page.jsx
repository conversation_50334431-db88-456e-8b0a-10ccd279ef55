"use client"
import InTheStudio from '@/components/customer/InTheStudio';
import MyGreetings from '@/components/customer/MyGreetings';
import React, { useState } from 'react';

const TABS = ["In the Studio", "My Greetings"];

export default function CelebrityGreeting() {
  const [activeTab, setActiveTab] = useState("In the Studio");
  const [expandedId, setExpandedId] = useState(null);
  const [showSubmitted, setShowSubmitted] = useState(false);
  
  return (
    <div className="flex flex-col items-center py-8 px-5 greetingsPageBgGradient h-screen">
     
      <div className="w-full max-w-md flex flex-col gap-4">
         {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <img src="/images/home/<USER>" alt="Logo" className="w-6" />
          </div>
          <img src="/images/home/<USER>" alt="Logo" className=" w-32" />
          <div className="flex items-center gap-2">
            <img src="/images/home/<USER>" alt="Logo" className="w-6" />
          </div>
        </div>
        {/* Tabs */}
        <div className="flex w-full mb-4 bg-[#383838] rounded-lg">
          {TABS.map(tab => (
            <button
              key={tab}
              className={`flex-1 py-2 cursor-pointer rounded-md text-sm font-semibold transition-all ${activeTab === tab ? 'bg-white text-black shadow' : 'text-white/70'}`}
              onClick={() => setActiveTab(tab)}
              disabled={activeTab === tab}
            >
              {tab}
            </button>
          ))}
        </div>
        {/* In the Studio */}
        {activeTab === "In the Studio" && (
          <InTheStudio />
        )}

        {/* My Greetings */}
        {activeTab === "My Greetings" && (
          <MyGreetings setExpandedId={setExpandedId} expandedId={expandedId} />
        )}

      </div>

    </div>
  );
} 