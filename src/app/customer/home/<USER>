"use client";
import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser } from "@/redux/slices/auth/slice";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { selectAuth } from "@/redux/slices/auth/selectors";
import useApiHook from "@/hooks/useApiHook";
import AllCelebrities from "@/components/celebrity/AllCelebrities";
import GreetingsList from "@/components/greetings/GreetingsList";
import PageLoader from "@/components/common/PageLoader";

export default function CustomerHome() {
  const { handleApiCall, isApiLoading } = useApiHook();
  const [showLogoutDropdown, setShowLogoutDropdown] = useState(false);
  const [celebrities, setCelebrities] = useState([]);
  const dropdownRef = useRef(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  // Close dropdown when clicking outside

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLogoutDropdown(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    dispatch(logoutUser());
    router.push("/auth/start");
  };

  const fetchCelebrities = async () => {
    try {
      const resp = await handleApiCall({
        method: "GET",
        url: `/celebrities`,
      });

      if (resp.status === 200) {
        setCelebrities(resp.data);
      }
    } catch (error) {
      console.error("Dashboard fetch error:", error);
    }
  };

  useEffect(() => {
    fetchCelebrities();
  }, []);

  return (
    <div className="flex flex-col items-center py-8 px-5 pageBgGradient h-screen text-white mb-24">
      <div className="w-full max-w-md flex flex-col gap-4 relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2 opacity-0 cursor-pointer">
            <img src="/images/home/<USER>" alt="Logo" className="w-5" />
            <img src="/images/home/<USER>" alt="Logo" className="w-5" />
          </div>
          <img src="/images/home/<USER>" alt="Logo" className="w-24" />
          <div
            className="flex items-center gap-2 cursor-pointer relative"
            ref={dropdownRef}
          >
            <img
              src="/images/home/<USER>"
              alt="Options"
              className="w-5 logoutDropdown"
              onClick={() => setShowLogoutDropdown(!showLogoutDropdown)}
            />
            {showLogoutDropdown && (
              <div className="absolute right-0 top-8 px-0 bg-white rounded-lg shadow-lg z-10 overflow-hidden cursor-pointer">
                <button
                  onClick={handleLogout}
                  className="w-full cursor-pointer text-left px-4 py-2 text-gray-800 hover:bg-gray-100 flex items-center gap-2 transition-colors text-sm font-bold"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </button>
              </div>
            )}
            <img
              src="/images/home/<USER>"
              alt="Notifications"
              className="w-5"
            />
          </div>
        </div>
        {/* Profile */}
        <div className="flex items-center gap-4">
          <img
            src={user?.profilePhotoUrl || "/images/home/<USER>"}
            alt="Profile"
            className="w-12 h-12 rounded-full object-cover border-1 border-[##999999]"
          />
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-semibold text-white">
                {user?.firstName}
              </span>
              <img
                src="/images/home/<USER>"
                alt="verified"
                className="w-5"
              />
            </div>
          </div>
        </div>
        {/* Earnings */}

        {/* Verification Pending */}
        <div className="bg-white/30 border border-white rounded-xl p-4 flex items-center gap-4 mt-2">
          <img
            src="/images/home/<USER>"
            alt="greeting"
            className="w-16"
          />
          <div>
            <span className="block text-white text-sm font-semibold">
              Finish creating your profile
            </span>
            <span className="block text-sm font-light text-white pr-20">
              Let customers know who you are
            </span>
          </div>
          <a href="/">
            <img
              src="/images/home/<USER>"
              alt="greeting"
              className=" h-6"
            />
          </a>
        </div>
        {/* Explore Celebs */}
        <div className="flex justify-between items-center">
          <h2 className="text-base font-normal">Explore celebrities</h2>
          <Link href="" className="underline text-xs">
            View all
          </Link>
        </div>
        <AllCelebrities celebrities={celebrities} />

        {/* Recent Greetings */}
        <h2 className="text-base font-normal mb-3">Your recent greetings</h2>

        <GreetingsList />
      </div>
      {isApiLoading && <PageLoader />}
    </div>
  );
}
