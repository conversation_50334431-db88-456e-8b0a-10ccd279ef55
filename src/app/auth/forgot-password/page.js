"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { forgotPassword } from "@/redux/slices/auth/thunks"; // Assuming you have an action for this
import useToaster from "@/hooks/useToaster";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email").required("Email is required"),
});

export default function ForgotPassword() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { showSuccessToast, showErrorToast } = useToaster();
  const handleApiError = useApiErrorHandler();

  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const resp = await dispatch(forgotPassword({ email: values.email }));
        if (resp?.payload.status === 201) {
          showSuccessToast("Password reset email sent!");
        } else {
          showErrorToast(
            resp?.payload?.data?.message || "Failed to send reset email!"
          );
        }
      } catch (err) {
        handleApiError(err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div className="bg-[#0c0c0c] min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-white">
          Forgot your password?
        </h2>
        <p className="mt-2 text-center text-sm text-gray-300">
          Enter your email address and we’ll send you a link to reset your
          password.
        </p>
      </div>

      <div className="px-3 mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bgGray py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={formik.handleSubmit}>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium leading-6 text-white"
              >
                Email address
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  className="block w-full rounded-md border-0  bg-black px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-black placeholder:text-gray-400 focus:ring-2 sm:text-sm sm:leading-6"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.email}
                />
                {formik.touched.email && formik.errors.email && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.email}
                  </p>
                )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={formik.isSubmitting}
                className="flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white cursor-pointer"
              >
                {formik.isSubmitting ? "Sending email..." : "Send Reset Link"}
              </button>
            </div>
          </form>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-300">
              Remembered your password?{" "}
              <Link
                href="/auth/start"
                className="font-medium text-white hover:text-gray-200"
              >
                Go back to login
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
