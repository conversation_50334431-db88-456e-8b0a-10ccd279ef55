"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import useApiHook from "@/hooks/useApiHook";
import useToaster from "@/hooks/useToaster";
import { Loader } from "@/components/common/Loader";
import { LoaderVariants } from "@/components/common/Loader";
import { useRouter } from "next/navigation";
import { useState } from "react";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";

const passwordRules = /((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;

export default function Register() {
  const router = useRouter();
  const { handleApiCall, isApiLoading } = useApiHook();
  const { showSuccessToast } = useToaster();
  const [selectedRole, setSelectedRole] = useState("customer");
  const handleApiError = useApiErrorHandler();

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required("First name is required"),
      lastName: Yup.string().required("Last name is required"),
      email: Yup.string().email("Invalid email").required("Email is required"),
      password: Yup.string()
        .min(8, "Minimum 8 characters")
        .max(30, "Maximum 30 characters")
        .matches(
          passwordRules,
          "Password must contain uppercase, lowercase, and a number or special character"
        )
        .required("Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("password")], "Passwords must match")
        .required("Confirm password is required"),
    }),
    onSubmit: async (values) => {
      try {
        const payload = {
          firstName: values.firstName,
          lastName: values.lastName,
          email: values.email,
          password: values.password,
          role: selectedRole,
        };

        const resp = await handleApiCall({
          method: "POST",
          url: "/auth/register",
          data: payload,
        });

        if (resp.status === 201) {
          showSuccessToast("Account created successfully!");
          router.push("/auth/login");
        }
      } catch (error) {
        handleApiError(error);
      }
    },
  });

  return (
    <div className=" min-h-screen flex flex-col justify-center py-4 px-6 text-black">
      <div className="w-full mt-20">
        <img
          src="/images/home/<USER>"
          alt="Landing Page"
          className="max-w-60 m-auto"
        />
      </div>

      <div className="bg-white px-3 mt-5 max-w-sm rounded-lg">
        <div className=" py-6 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Role Selection Tabs */}
          <div className="flex mb-6 rounded-md  p-1 bg-black/10 font-semibold shadow-sm">
            <button
              type="button"
              style={
                selectedRole === "customer"
                  ? { boxShadow: "0 1px 2px #888" }
                  : {}
              }
              className={`flex-1 py-2 px-4 rounded-md text-sm cursor-pointer ${
                selectedRole === "customer"
                  ? "bg-white text-black"
                  : "text-[#919191] hover:text-white"
              }`}
              onClick={() => setSelectedRole("customer")}
            >
              Customer
            </button>

            <button
              type="button"
              style={
                selectedRole === "celebrity"
                  ? { boxShadow: "0 1px 2px #888" }
                  : {}
              }
              className={`flex-1 py-2 px-4 rounded-md text-sm cursor-pointer ${
                selectedRole === "celebrity"
                  ? "bg-white text-black"
                  : "text-[#919191] hover:text-white"
              }`}
              onClick={() => setSelectedRole("celebrity")}
            >
              Celebrity
            </button>
          </div>

          <form onSubmit={formik.handleSubmit} className="space-y-4">
            {/* Rest of your form fields remain the same */}
            <div className="w-full grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="firstName"
                  className="block text-xs font-medium clrGray"
                >
                  First name
                </label>
                <div className="mt-2">
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.firstName}
                    className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                  />
                  {formik.touched.firstName && formik.errors.firstName && (
                    <p className="text-red-500 text-xs mt-1 font-bold">
                      {formik.errors.firstName}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="lastName"
                  className="block text-xs font-medium clrGray"
                >
                  Last name
                </label>
                <div className="mt-2">
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    value={formik.values.lastName}
                    className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                  />
                  {formik.touched.lastName && formik.errors.lastName && (
                    <p className="text-red-500 text-xs mt-1 font-bold">
                      {formik.errors.lastName}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-xs font-medium clrGray"
              >
                Email address
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  type="email"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.email}
                  className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                />
                {formik.touched.email && formik.errors.email && (
                  <p className="text-red-500 text-xs mt-1 font-bold">
                    {formik.errors.email}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-xs font-medium clrGray"
              >
                Password
              </label>
              <div className="mt-2">
                <input
                  id="password"
                  name="password"
                  type="password" 
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.password}
                  className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                />
                {formik.touched.password && formik.errors.password && (
                  <p className="text-red-500 text-xs mt-1 font-bold">
                    {formik.errors.password}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-xs font-medium clrGray"
              >
                Confirm password
              </label>
              <div className="mt-2">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.confirmPassword}
                  className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                />
                {formik.touched.confirmPassword &&
                  formik.errors.confirmPassword && (
                    <p className="text-red-500 text-xs mt-1 font-bold">
                      {formik.errors.confirmPassword}
                    </p>
                  )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="flex w-full justify-center rounded-lg text-white p-3 grayButton hover:scale-105 mt-4"
              >
                {isApiLoading ? (
                  <Loader
                    variant={LoaderVariants.rotatingLines}
                    size={25}
                    color="white"
                  />
                ) : (
                  "Register"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
