"use client";
import LoginForm from "@/components/auth/LoginForm";
import Register from "@/components/auth/Register";
import { useState } from "react";



export default function Start() {
  const [selectedAction, setSelectedAction] = useState("login");
  return (
    <div className=" min-h-screen flex flex-col justify-center py-4 px-6 text-black">
      <div className="w-full mt-20">
        <img
          src="/images/home/<USER>"
          alt="Landing Page"
          className="max-w-60 m-auto"
        />
      </div>

      <div className="bg-white px-2 mt-5 max-w-sm rounded-lg m-auto w-full">
        <div className=" py-6 px-4 sm:rounded-lg w-full relative">
          {/* Action Selection Tabs */}
          <div className="flex mb-6 rounded-md  p-1 bg-black/10 font-bold shadow-sm">
            <button
              type="button"
              style={
                selectedAction === "login"
                  ? { boxShadow: "0 1px 2px #888" }
                  : {}
              }
              className={`flex-1 py-2 px-4 rounded-md text-sm cursor-pointer ${
                selectedAction === "login"
                  ? "bg-white text-black"
                  : "text-[#919191] hover:text-black"
              }`}
              onClick={() => setSelectedAction("login")}
            >
              Log In
            </button>

            <button
              type="button"
              style={
                selectedAction === "register"
                  ? { boxShadow: "0 1px 2px #888" }
                  : {}
              }
              className={`flex-1 py-2 px-4 rounded-md text-sm cursor-pointer ${
                selectedAction === "register"
                  ? "bg-white text-black"
                  : "text-[#919191] hover:text-black"
              }`}
              onClick={() => setSelectedAction("register")}
            >
              Sign Up
            </button>
          </div>
        {selectedAction === "login" ? (
          <div className="w-full">
            <LoginForm />
          </div>
            
        ) : (
          <div className="w-full">
            <Register />
          </div>
        )}
      
        </div>
      </div>
    </div>
  );
}
