"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import { useRouter, useSearchParams } from "next/navigation";
import { useDispatch } from "react-redux";
import { resetPassword } from "@/redux/slices/auth/thunks"; // Assuming you have a resetPassword action
import useToaster from "@/hooks/useToaster";
import { useState, useEffect } from "react";
import Link from "next/link";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";

const validationSchema = Yup.object({
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password"), null], "Passwords must match")
    .required("Confirm Password is required"),
});

export default function ResetPassword() {
  const handleApiError = useApiErrorHandler();
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  const { showSuccessToast, showErrorToast } = useToaster();

  const [token, setToken] = useState("");

  useEffect(() => {
    const tokenFromParams = searchParams.get("token");
    if (tokenFromParams) {
      setToken(tokenFromParams);
    } else {
      router.push("/login");
    }
  }, [searchParams, router]);

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const resp = await dispatch(
          resetPassword({ token, password: values.password })
        );
        if (resp?.payload.status === 200) {
          showSuccessToast("Password reset successfully!");
          router.push("/login");
        } else {
          showErrorToast(
            resp?.payload?.data?.message || "Failed to reset password"
          );
        }
      } catch (err) {
        handleApiError(err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div className="bg-[#0c0c0c] min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-white">
          Reset your password
        </h2>
        <p className="mt-2 text-center text-sm text-gray-300">
          Enter your new password below.
        </p>
      </div>

      <div className="px-3 mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bgGray py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={formik.handleSubmit}>
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium leading-6 text-white"
              >
                New Password
              </label>
              <div className="mt-2">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  className="block w-full rounded-md border-0  bg-black px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-black placeholder:text-gray-400 focus:ring-2 sm:text-sm sm:leading-6"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.password}
                />
                {formik.touched.password && formik.errors.password && (
                  <p className="text-red-500 text-sm mt-1">
                    {formik.errors.password}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium leading-6 text-white"
              >
                Confirm Password
              </label>
              <div className="mt-2">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  className="block w-full rounded-md border-0  bg-black px-3.5 py-2 text-white shadow-sm ring-1 ring-inset ring-black placeholder:text-gray-400 focus:ring-2 sm:text-sm sm:leading-6"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.confirmPassword}
                />
                {formik.touched.confirmPassword &&
                  formik.errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {formik.errors.confirmPassword}
                    </p>
                  )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={formik.isSubmitting}
                className="flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                {formik.isSubmitting
                  ? "Resetting password..."
                  : "Reset Password"}
              </button>
            </div>
          </form>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-300">
              Remembered your password?{" "}
              <Link
                href="/auth/start"
                className="font-medium text-white hover:text-gray-200"
              >
                Go back to login
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
