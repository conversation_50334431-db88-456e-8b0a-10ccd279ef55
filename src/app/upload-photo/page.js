"use client"
import React, { useRef, useState, useEffect } from 'react';
import { ChevronLeft, Camera, Grid, X, Mic } from 'lucide-react';
import { useRouter } from 'next/navigation';
import useSampleGreetings from '@/hooks/useSampleGreetings';
import SampleGreetingService from '@/services/SampleGreetingService';

const UploadPhotoPage = () => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [currentScreen, setCurrentScreen] = useState('select');
  const [selectedPreview, setSelectedPreview] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [greetingId, setGreetingId] = useState(null);
  const router = useRouter();
  const { uploadAvatar, updateCategoryId, updateTones } = useSampleGreetings();

  useEffect(() => {
    // Check if we're updating an existing greeting
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const id = params.get('greetingId');
      if (id) {
        setIsUpdating(true);
        setGreetingId(id);
      }
    }
  }, []);
  const images = [
    "/images/photos/image1.jpg",
    "/images/photos/image2.jpg",
    "/images/photos/image3.jpg",
    "/images/photos/image4.jpg",
    "/images/photos/image5.jpg",
    "/images/photos/image6.jpg",
  ];

  const toggleImageSelection = (image) => {
    if (selectedImages.includes(image)) {
      setSelectedImages(selectedImages.filter(img => img !== image));
    } else {
      setSelectedImages([...selectedImages, image]);
    }
    setSelectedPreview(image);

    fetch(image)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], "avatar.jpg", { type: "image/jpeg" });
        uploadAvatar(file);

        // If we're updating an existing greeting, upload the avatar directly
        if (isUpdating && greetingId) {
          handleAvatarUpdate(file);
        }
      })
      .catch(err => console.error("Error converting image to file:", err));
  };

  const handleAvatarUpdate = async (file) => {
    try {
      if (greetingId && file) {
        const formData = new FormData();
        formData.append('avatar', file);
        await SampleGreetingService.updateSampleGreeting(greetingId, formData);

        // Navigate back to video preview after update
        router.push(`/video-preview`);
      }
    } catch (error) {
      console.error("Error updating avatar:", error);
    }
  };
const fileInputRef = useRef(null);
const cameraInputRef = useRef(null);

const handleRecentsClick = () => {
  if (fileInputRef.current) {
    fileInputRef.current.click();
  }
};

const handleCameraClick = () => {
  if (cameraInputRef.current) {
    cameraInputRef.current.click();
  }
};
  const handleNext = () => {
    if (currentScreen === 'select' && selectedImages.length > 0) {
      setCurrentScreen('preview');
    } else if (currentScreen === 'preview') {
      setCurrentScreen('success');
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleImageUpload = (event) => {
    const files = Array.from(event.target.files);

    // Set default values for categoryId and tones
    updateCategoryId("6821b350e1e3e7a1e71bdbe6"); // Specific category ID
    updateTones(["neutral", "friendly"]); // Default tones

    // Store the actual file object in Redux
    if (files.length > 0) {
      const avatarFile = files[0];
      uploadAvatar(avatarFile);
      console.log("Avatar file set from upload:", avatarFile);

      // Create URLs for UI preview
      const newImages = files.map(file => URL.createObjectURL(file));
      setSelectedImages([...selectedImages, ...newImages]);
      setSelectedPreview(newImages[0]);
    }
  };

  if (currentScreen === 'success') {
    return (
      <div className="min-h-screen bg-black text-white p-6 flex flex-col items-center justify-center">
        <button onClick={() => setCurrentScreen('select')} className="absolute top-4 right-4">
          <X size={24} />
        </button>
        <h2 className="text-2xl font-bold mb-4">Photo Uploaded</h2>
        <p className="text-center text-gray-400 mb-8">
          This image will help create your personalized video greetings — clear, confident, and totally you.
        </p>
        <div>

               <div className="text-center px-6">
            <div className="mb-8 relative">
              {/* Large ellipse background */}
              <img
                src="/images/home/<USER>"
                alt="ellipse"
                className="w-84 h-84 mx-auto absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0"
              />
              {/* Camera image positioned on top */}
              <img
                src="/CameraLarge.png"
                alt="Selected Photo"
                className="w-34 h-34 object-cover rounded-full mb-4 mx-auto relative z-10"
              />
              </div>
              </div>

        </div>
        <h3 className="font-medium mb-4">What's next?</h3>
        <div className="space-y-3 w-full mb-8">
          <div className="flex items-center gap-2">
            <span>🎙 You’ll now record a short script</span>
          </div>
          <div className="flex items-center gap-2">
            <span>🧠 We’ll use this to power your video greetings</span>
          </div>
          <div className="flex items-center gap-2">
            <span>🕒 You can always change your photo later</span>
          </div>
        </div>
        <button
          className="  w-full py-3 px-6 rounded-lg
        border border-black
        text-white font-medium
        shadow-[0px_1px_2px_0px_rgba(0,0,0,0.48)]
        bg-black
        bg-gradient-to-b from-white/20 to-transparent
        transition-all duration-200
        hover:from-white/30 active:from-white/10"
         onClick={() => router.push('/record-video')}
        >
          Continue to recording
        </button>
      </div>
    );
  }

  return (
    <div className={`${currentScreen === 'preview' ? 'h-screen overflow-hidden' : 'min-h-screen'} bg-black text-white`}>
      <div className="absolute top-0 left-0 right-0 z-20 flex flex-col px-4 pt-5 pb-4 bg-transparent">
        <div className="flex items-center justify-between">
          <button
            className="p-1 hover:bg-white/10 rounded-full"
            onClick={handleBack}
          >
            <ChevronLeft size={20} />
          </button>
          <span className="text-lg font-medium">Upload photo</span>
          <button
            className={`text-sm ${selectedImages.length > 0 ? 'text-white' : 'text-gray-500'}`}
            onClick={handleNext}
            disabled={selectedImages.length === 0}
          >
            Next
          </button>
        </div>
      </div>

      <div className={`relative ${currentScreen === 'preview' ? 'h-screen w-full' : 'aspect-square w-full'}
        bg-neutral-900 overflow-hidden ${currentScreen === 'preview' ? '' : 'mb-6 mt-14'}`}>
        <img
          src={selectedPreview || images[0]}
          alt="Main upload preview"
          className="w-full h-full object-cover"
        />
        <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-black/70 to-transparent" />

        {currentScreen === 'preview' && (
          <>
            <div className="absolute bottom-0 left-0 right-0 h-60 bg-gradient-to-t from-black/80 to-transparent" />

            <div className="absolute bottom-28 left-0 right-0 flex justify-center">
              <div className="flex gap-3">
                {selectedImages.map((img, index) => (
                  <button
                    key={img}
                    onClick={() => setSelectedPreview(img)}
                    className={`rounded-full overflow-hidden border-2
                      ${selectedPreview === img ? 'w-20 h-20 border-white' : 'w-[68px] h-[68px] border-transparent'}`}
                  >
                    <img src={img} alt={`Selected ${index + 1}`} className="w-full h-full object-cover" />
                  </button>
                ))}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Upload button - fixed at bottom */}
      <div className="absolute bottom-8 left-0 right-0 flex justify-center px-6">
        <button
          className="w-[320px] h-[50px] px-6 py-1.5 flex justify-center items-center gap-2.5 flex-shrink-0 rounded-lg border border-black bg-black bg-gradient-to-b from-white/20 to-transparent shadow-[0px_1px_2px_0px_rgba(0,0,0,0.48)] text-white font-medium"
          onClick={handleNext}
        >
          Upload photo
        </button>
      </div>

      {currentScreen === 'select' && (
        <>
          <div className="flex justify-between items-center px-4">
            <div className="flex items-center space-x-2">
              <h2 className="text-base font-medium" onClick={handleRecentsClick}>Recents</h2>
              <button onClick={handleRecentsClick}>
    <ChevronLeft size={16} className="rotate-180 text-gray-400" />
  </button>
            </div>
            <div className="flex gap-2">
              <button
                className={`flex items-center justify-center h-10 rounded-full text-white text-sm ${
                  selectedImages.length > 0 ? 'w-16 bg-gray-500' : 'w-10 bg-neutral-800'
                }`}
              >
                <img src="/change-photo.svg" alt="Change photo" className="w-5 h-5 mr-1" />
                {selectedImages.length > 0 && <span>{selectedImages.length}/3</span>}
              </button>
              <label
                onClick={handleCameraClick}
                className="flex items-center justify-center w-10 h-10 bg-neutral-800 rounded-full cursor-pointer">
                <input
                  ref={cameraInputRef}
                  type="file"
                  accept="image/*"
                  capture="environment"
                  className="hidden"
                  onChange={handleImageUpload}
                />
                <img src="/camera.svg" alt="Take photo" className="w-5 h-5" />
              </label>

              {/* Hidden input for gallery access (Recents) */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleImageUpload}
              />
            </div>
          </div>


<div className="px-4 mt-2">
  <div className="grid grid-cols-3 gap-1">
    {images.map((image, index) => (
      <button
        key={index}
        onClick={() => toggleImageSelection(image)}
        className="relative aspect-square"
      >
        <img
          src={image}
          alt={`Recent ${index + 1}`}
          className="w-full h-full object-cover rounded-sm"
        />
        {selectedImages.includes(image) && (
          <div className="absolute inset-0 bg-white/30 flex items-center justify-center">
            <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
              <span className="text-black text-sm">
                {selectedImages.indexOf(image) + 1}
              </span>
            </div>
          </div>
        )}
      </button>
    ))}
  </div>
</div>
          </>
        )}
      </div>
  );
}

export default UploadPhotoPage;