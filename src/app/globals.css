@import 'tailwindcss';

@font-face {
  font-family: 'AG Book Pro';
  src: url('/public/fonts/AGBookStdBlack.woff2') format('woff2'),
       url('/public/fonts/AGBookStdBlack.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

body {
  background: #000000;
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  background-image: url('/images/common/bg-animation.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bgGray {
  background-color: #151516;
}

.clrGray {
  color: #6c7278;
}

.pageBgGradient {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.57) 0%, #000 100%), #777;
}

.previewpageBgGradient {
  background: linear-gradient(180deg, #000 0%, #333 100%);
}

.greetingsPageBgGradient {
  background: linear-gradient(180deg, #000 0%, #333 100%),
    linear-gradient(180deg, rgba(0, 0, 0, 0.57) 0%, #000 100%), #777;
}
.navbarBgGradient {
  background: linear-gradient(180deg, #000 0%, #242424 100%), #000;
}

.grayGradientBg {
  border-radius: 8px;
  background: linear-gradient(
    180deg,
    rgba(44, 44, 44, 0.8) 0%,
    rgba(0, 0, 0, 0.8) 23.08%,
    rgba(44, 44, 44, 0.8) 100%
  );
  box-shadow: 4.729px 5.476px 10.18px 0px rgba(0, 0, 0, 0.3),
    13.192px 18.17px 22.351px 0px rgba(0, 0, 0, 0.3),
    0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset,
    -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset;
}

.btnGrayGradient {
  background: linear-gradient(180deg, rgba(214, 214, 214, 0) 0%, #a7a7a7 100%),
    #f2f2f2;
  box-shadow: 0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset,
    -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset,
    0px 1px 2px 0px rgba(16, 24, 40, 0.05);
}

.grayButton {
  background-image: linear-gradient(#2f2f2f, #151515, #030303);
}

.whiteButton {
  background-image: linear-gradient(#d6d6d600, #d6d6d600, #e2e1e1);
}

.activeBgWhite {
  background-image: linear-gradient(#f8f8f8, #ebebeb);
}

.navbarBg {
  background-image: linear-gradient(#000000, #242424);
}

.bg-gradient-custom {
  background: linear-gradient(180deg, #000000 0%, #666666 100%);
}

.profile-gradient {
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,       
    rgba(0, 0, 0, 0.6) 50%      
  );
}

.bg-gradient-custom-2 {
  background: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.2) 100%
    ),
    linear-gradient(180deg, rgba(0, 0, 0, 0.57) 0%, #000 100%),
    linear-gradient(0deg, #777 0%, #777 100%),
    linear-gradient(180deg, #e5e5e5 0%, #fff 100%);
}

.react-tel-input .form-control {
  height: 50px !important;
  width: 100% !important;
}
.phoneDiv {
  margin-right: 0 !important;
}

.gradient1 {
  background: #d4d4d4;
}
.gradient2 {
  background: #e3d1e5;
}
.gradient3 {
  background: #e8e3d6;
}
.gradient4 {
  background: #d0e9d9;
}
.waveBg {
  background-image: url('/images/home/<USER>');
  background-repeat: no-repeat;
  background-size: 100%;
}

input[type="date"]::-webkit-calendar-picker-indicator {
          filter: invert(0.8);
          cursor: pointer;
        }

        /*Custom ScrollBar*/
/* width */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
/* Track */
::-webkit-scrollbar-track {
  background: #1b1b1b;
}
/* Handle */
::-webkit-scrollbar-thumb {
  background: #302f2f;
  border-radius: 5px;
}
/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #858484;
}

.myscrollbar {
  ::-webkit-scrollbar-thumb {
    background: black !important;
  }
}

/*Custom ScrollBar*/