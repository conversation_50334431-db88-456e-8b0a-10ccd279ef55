import Link from 'next/link'

export default function Pricing() {
  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-4 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              Simple, Transparent Pricing
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              Choose the perfect plan for your needs. All plans include our satisfaction guarantee and secure payment processing.
            </p>
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-4 sm:py-16 w-full">
        <div className="mx-auto grid  grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[
            {
              name: 'Basic',
              price: '$99',
              description: 'Perfect for personal occasions',
              features: [
                '30-second video message',
                'Standard delivery (3-5 days)',
                'Basic personalization',
                'Email delivery',
                'Download option',
                'Customer support'
              ]
            },
            {
              name: 'Premium',
              price: '$199',
              description: 'Most popular for special occasions',
              features: [
                '60-second video message',
                'Priority delivery (1-3 days)',
                'Advanced personalization',
                'Email & social media delivery',
                'Download option',
                'Priority customer support',
                'Social media sharing',
                'HD quality video'
              ]
            },
            {
              name: 'Enterprise',
              price: 'Custom',
              description: 'For businesses and special events',
              features: [
                'Custom video length',
                'Express delivery',
                'Full personalization',
                'Multiple delivery options',
                'Download & streaming',
                '24/7 dedicated support',
                'Social media sharing',
                '4K quality video',
                'Custom branding options'
              ]
            }
          ].map((tier) => (
            <div
              key={tier.name}
              className="flex flex-col justify-between rounded-3xl bgGray p-8 ring-1 ring-gray-800 xl:p-10 w-full"
            >
              <div className='w-full'>
                <div className="flex items-center justify-between gap-x-4">
                  <h3 className="text-lg font-semibold leading-8 text-white">{tier.name}</h3>
                </div>
                <p className="mt-4 text-sm leading-6 text-gray-300">{tier.description}</p>
                <p className="mt-6 flex items-baseline gap-x-1">
                  <span className="text-4xl font-bold tracking-tight text-white">{tier.price}</span>
                  {tier.price !== 'Custom' && (
                    <span className="text-sm font-semibold leading-6 text-gray-300">/video</span>
                  )}
                </p>
                <ul className="mt-8 space-y-3 text-sm leading-6 text-gray-300">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <svg className="h-6 w-5 flex-none text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <Link
                href="/celebrity"
                className="mt-8 block rounded-md bg-white px-3 py-2 text-center text-sm font-semibold leading-6 text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Get started
              </Link>
            </div>
          ))}
        </div>
      </div>

    </div>
  )
} 