"use client";

import React, { useState, useEffect } from "react";
import VoiceGreeting from "@/components/VoiceGreeting/VoiceGreeting";

export default function VoiceGreetingPage() {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  useEffect(() => {
    const detectKeyboard = () => {
      const viewportHeight = window.innerHeight;
      const windowHeight = window.outerHeight;


      setIsKeyboardVisible(viewportHeight < windowHeight * 0.8);
    };

    window.addEventListener('resize', detectKeyboard);

    detectKeyboard();

    return () => {
      window.removeEventListener('resize', detectKeyboard);
    };
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <div className="flex-1">
        <VoiceGreeting />
      </div>
    </div>
  );
}
