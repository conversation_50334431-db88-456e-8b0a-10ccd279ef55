import Link from 'next/link'

export default function HowItWorks() {
  return (
    <div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-gradient-to-b from-gray-900/20">
        <div className="mx-auto max-w-7xl px-6 py-8 sm:py-16 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
              How It Works
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-300">
              Getting a personalized video message from your favorite celebrity is simple and straightforward. Follow these steps to make your special occasion even more memorable.
            </p>
          </div>
        </div>
      </div>

      {/* Steps Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-10 sm:py-16">
        <div className="mx-auto max-w-2xl lg:max-w-none">
          <div className="grid grid-cols-1 gap-y-8 gap-x-16 lg:grid-cols-3">
            {[
              {
                number: '01',
                title: 'Choose a Celebrity',
                description: 'Browse our extensive list of verified celebrities. Each profile includes their availability, pricing, and sample videos. Find the perfect celebrity for your occasion.',
                features: [
                  'Browse by category or search by name',
                  'View celebrity profiles and sample videos',
                  'Check availability and pricing',
                  'Read reviews from other customers'
                ]
              },
              {
                number: '02',
                title: 'Place Your Order',
                description: 'Select your occasion, add personal details, and place your order. You can include specific requests or messages you want the celebrity to mention in their video.',
                features: [
                  'Select occasion type',
                  'Add recipient details',
                  'Include personal message',
                  'Choose delivery date'
                ]
              },
              {
                number: '03',
                title: 'Receive Your Video',
                description: 'The celebrity will create a personalized video message just for you. You\'ll receive it within the specified delivery time, ready to share with your loved one.',
                features: [
                  'Get notified when video is ready',
                  'Download or share directly',
                  'Keep video forever',
                  'Share on social media'
                ]
              }
            ].map((step) => (
              <div key={step.number} className="flex flex-col">
                <div className="flex items-center gap-x-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white">
                    <span className="text-lg font-semibold text-black">{step.number}</span>
                  </div>
                  <h3 className="text-xl font-semibold leading-7 text-white">{step.title}</h3>
                </div>
                <p className="mt-4 text-base leading-7 text-gray-300">{step.description}</p>
                <ul className="mt-6 space-y-3">
                  {step.features.map((feature) => (
                    <li key={feature} className="flex gap-x-3">
                      <svg className="h-6 w-5 flex-none text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clipRule="evenodd" />
                      </svg>
                      <span className="text-base leading-7 text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-10 sm:py-16">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-white">Frequently Asked Questions</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Everything you need to know
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
            {[
              {
                question: 'How long does it take to receive my video?',
                answer: 'Most videos are delivered within 3-5 business days. You can check the estimated delivery time on each celebrity\'s profile before placing your order.'
              },
              {
                question: 'Can I request specific content in the video?',
                answer: 'Yes! When placing your order, you can include specific messages, inside jokes, or requests that you\'d like the celebrity to mention in their video.'
              },
              {
                question: 'What if I\'m not satisfied with the video?',
                answer: 'We offer a 100% satisfaction guarantee. If you\'re not completely satisfied with your video, we\'ll work with the celebrity to create a new one or provide a full refund.'
              },
              {
                question: 'How do I share the video with others?',
                answer: 'Once you receive your video, you can download it, share it via email, or post it directly to social media. The video is yours to keep and share as you wish.'
              }
            ].map((faq) => (
              <div key={faq.question} className="flex flex-col">
                <dt className="text-base font-semibold leading-7 text-white">{faq.question}</dt>
                <dd className="mt-2 text-base leading-7 text-gray-300">{faq.answer}</dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-[#0c0c0c]">
        <div className="mx-auto max-w-7xl py-16 sm:px-6 sm:py-16 lg:px-8">
          <div className="relative isolate overflow-hidden bgGray px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to Get Started?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              Browse our selection of celebrities and find the perfect match for your special occasion.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/celebrity"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Browse Celebrities
              </Link>
              <Link href="/contact" className="text-sm font-semibold leading-6 text-white">
                Contact Support <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 