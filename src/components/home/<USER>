"use client"
import Link from 'next/link'
import Image from 'next/image'

export default function Page() {
  return (
<div className="bg-[#0c0c0c]">
      {/* Hero Section */}
      <div className="relative isolate overflow-hidden bg-[#0c0c0c]">
        <div className="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
          <div className="lg:grid lg:grid-cols-2 lg:gap-x-8 lg:items-center">
            {/* Left side - Text content */}
            <div className="mx-auto max-w-2xl lg:mx-0">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Get Personalized Video Messages from Your Favorite Celebrities
              </h1>
              <p className="mt-6 text-lg leading-8 text-gray-300">
                Make any occasion special with a personalized video message from your favorite celebrity. Perfect for birthdays, anniversaries, graduations, and more.
              </p>
              <div className="mt-10 flex items-center gap-x-6">
                <Link
                  href="/celebrity"
                  className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
                >
                  Browse Celebrities
                </Link>
                <Link href="/how-it-works" className="text-sm font-semibold leading-6 text-white">
                  Learn more <span aria-hidden="true">→</span>
                </Link>
              </div>
            </div>

            {/* Right side - Celebrity image */}
            <div className="mt-16 lg:mt-0">
              <div className="relative">
                <div className="relative mx-auto w-full max-w-lg">
                  <div className="relative w-full aspect-[4/4] rounded-2xl overflow-hidden">
                    <Image
                      src="/images/celebrities/Ronaldo.png" // You'll need to add this image to your public folder
                      alt="Celebrity"
                      fill
                      className=" object-cover"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#0c0c0c] to-transparent opacity-60" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="mx-auto max-w-7xl px-6 lg:px-8 py-4 sm:py-16">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-white">How It Works</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Everything you need to get started
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            Getting a personalized video message from your favorite celebrity is easy. Follow these simple steps to make your special occasion even more memorable.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            {[
              {
                name: 'Choose a Celebrity',
                description: 'Browse our extensive list of celebrities and select your favorite. Each celebrity has their own profile with pricing and availability.',
              },
              {
                name: 'Place Your Order',
                description: 'Select your occasion, add personal details, and place your order. You can include specific requests or messages you want the celebrity to mention.',
              },
              {
                name: 'Receive Your Video',
                description: 'The celebrity will create a personalized video message just for you. You\'ll receive it within the specified delivery time.',
              },
            ].map((feature) => (
              <div key={feature.name} className="flex flex-col">
                <dt className="flex items-center gap-x-3 text-base font-semibold leading-7 text-white">
                  {feature.name}
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-300">
                  <p className="flex-auto">{feature.description}</p>
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-[#0c0c0c]">
        <div className="mx-auto max-w-7xl py-24 sm:px-6 sm:py-32 lg:px-8">
          <div className="relative isolate overflow-hidden bgGray px-6 py-24 text-center shadow-2xl sm:rounded-3xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Make Someone's Day Special
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              Surprise your loved ones with a personalized video message from their favorite celebrity. Perfect for any occasion.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/celebrity"
                className="rounded-md bg-white px-3.5 py-2.5 text-sm font-semibold text-black shadow-sm hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white"
              >
                Get Started
              </Link>
              <Link href="/how-it-works" className="text-sm font-semibold leading-6 text-white">
                Learn more <span aria-hidden="true">→</span>
              </Link>
            </div>
            <svg
              viewBox="0 0 1024 1024"
              className="absolute left-1/2 top-1/2 -z-10 h-[64rem] w-[64rem] -translate-x-1/2 [mask-image:radial-gradient(closest-side,white,transparent)]"
              aria-hidden="true"
            >
              <circle cx={512} cy={512} r={512} fill="url(#827591b1-ce8c-4110-b064-7cb85a0b1217)" fillOpacity="0.7" />
              <defs>
                <radialGradient id="827591b1-ce8c-4110-b064-7cb85a0b1217">
                  <stop stopColor="#7775D6" />
                  <stop offset={1} stopColor="#E935C1" />
                </radialGradient>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </div>
   
  )
}
