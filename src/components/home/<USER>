"use client";

import { useEffect, useState } from "react";
import { User, Star } from "lucide-react";
import Button from "../common/Button";

export default function Page() {
  const [showIcon, setShowIcon] = useState(false);
  const [showText, setShowText] = useState(false);
  const [showBtn, setShowBtn] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setShowIcon(true);
    }, 1000);

    setTimeout(() => {
      setShowText(true);
    }, 2000);

    setTimeout(() => {
      setShowBtn(true);
    }, 3000);
  }, []);

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/image.png')",
        }}
      >
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 w-full h-full flex flex-col items-center justify-between text-white px-4 py-8 sm:px-6 sm:py-12">

        {/* Top Section */}
        <div className="flex-1 flex flex-col items-center justify-start pt-12 sm:pt-16">
          <div className={`${showText ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"} transition-all duration-1000 ease-out`}>
            <p className="text-sm sm:text-base md:text-lg font-medium text-center tracking-wide">
              Exclusive Videos from Icons
            </p>
          </div>
        </div>

        {/* Center Section - Logo */}
        <div className="flex-1 flex flex-col items-center justify-center">
          <div className={`${showIcon ? "opacity-100" : "opacity-0"} relative w-72 h mt-10 flex items-center justify-center transition-all duration-1000 ease-out delay-300`}>
            <img
              src="/images/home/<USER>"
              alt="ellipse"
              className="max-w-64 m-auto absolute left-0 right-0 top-0 bottom-0 z-20"
            />
            <img
              src="/images/home/<USER>"
              alt="Landing Page"
              className="w-60 m-auto relative z-10"
            />
          </div>

          <div className={`${showText ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"} transition-all duration-1000 ease-out delay-700 mt-8`}>
            <p className="text-sm sm:text-base md:text-lg font-medium text-center tracking-wide">
              Made Just for You
            </p>
          </div>
        </div>

        <div className={`${showBtn ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"} transition-all duration-1000 ease-out delay-1000 w-full max-w-sm space-y-3`}>

          <Button
            onClick={() => window.location.href = "/auth/start?role=fan"}
            className="bg-black border border-white/20 "
          >
            <div className="flex items-center justify-center gap-2">
              <User size={18} className="text-white" />
              <span className="text-white font-medium">Subscribe as a Fan</span>
            </div>
          </Button>

          <Button
            onClick={() => window.location.href = "/auth/start?role=celebrity"}
            className="bg-white/90 hover:bg-white border border-white/30"
          >
            <div className="flex items-center justify-center gap-2">
              <Star size={18} className="text-black" />
              <span className="text-black font-medium">Join as a Celebrity</span>
            </div>
          </Button>

        </div>
      </div>
    </div>
  );
}
