'use client';

const RecordButton = ({ isRecording, onClick }) => {
  return (
    <button
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      className="w-20 h-20 rounded-full border-2 border-white flex items-center justify-center p-[10px] bg-black/30"
      style={{ fontFamily: '"AG Book Pro", sans-serif' }}
    >
      {isRecording ? (
        <div className="w-12 h-12 rounded-full bg-red-600"></div>
      ) : (
        <div className="w-full h-full rounded-full bg-white"></div>
      )}
    </button>
  );
};

export default RecordButton;
