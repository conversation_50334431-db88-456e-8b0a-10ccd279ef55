'use client';

const Timer = ({ time }) => {
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  return (
    <div className="absolute bottom-48 left-0 right-0 flex justify-center">
      <div
        className="bg-black/50 px-4 py-2 rounded-full text-white"
        style={{ fontFamily: '"AG Book Pro", sans-serif' }}
      >
        {formatTime(time)}
      </div>
    </div>
  );
};

export default Timer;
