'use client';

import React, { useRef, useEffect } from 'react';

const ScrollingScript = ({ scripts, currentScriptIndex, recordingTime }) => {
  const scriptContainerRef = useRef(null);

  useEffect(() => {
    if (scriptContainerRef.current) {
      const scrollAmount = recordingTime * 10;
      scriptContainerRef.current.scrollTop = scrollAmount;
    }
  }, [recordingTime]);

  return (
    <div className="absolute top-6 left-4 z-10 text-white" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
      <div
        ref={scriptContainerRef}
        className="w-[342px] h-[128px] overflow-hidden"
        style={{ scrollBehavior: 'smooth', fontFamily: '"AG Book Pro", sans-serif' }}
      >
        {scripts.map((script, index) => {
          const lines = script.split('\n');
          const scriptItems = [];

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line.startsWith('🎙') || line.startsWith('😄') ||
                line.startsWith('🔥') || line.startsWith('🧘') ||
                line.startsWith('🎉') || line.startsWith('😎') ||
                line.startsWith('👋')) {
              // This is a tone indicator
              const parts = line.split(' ');
              const emoji = parts[0];
              const tone = parts.slice(1).join(' ');

              const content = i + 1 < lines.length ? lines[i + 1] : '';

              scriptItems.push({ emoji, tone, content });
            }
          }

          return (
            <div
              key={index}
              className={`mb-6 ${index === currentScriptIndex ? 'opacity-100' : 'opacity-50'}`}
            >
              {scriptItems.map((item, itemIndex) => (
                <div key={itemIndex} className="mb-4">
                  <div className="flex items-center mb-1">
                    <span className="text-lg mr-2">{item.emoji}</span>
                    <span className="text-xs text-white/80">{item.tone}</span>
                  </div>
                  <p className="text-lg font-medium leading-tight text-white drop-shadow-md">{item.content}</p>
                </div>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ScrollingScript;
