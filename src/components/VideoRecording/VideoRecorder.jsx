'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import Webcam from 'react-webcam';
import RecordButton from './RecordButton';
import Timer from './Timer';
import CountdownTimer from './CountdownTimer';
import ScrollingScript from './ScrollingScript';
import iconUser from "../../../public/iconuser.png";
import { useRouter } from 'next/navigation';
import useSampleGreetings from '@/hooks/useSampleGreetings';
import SampleGreetingService from '@/services/SampleGreetingService';
import { Loader, LoaderVariants } from '@/components/common/Loader';
const VideoRecorder = ({ currentStep, onStepChange, isUpdating = false, greetingId = null }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [videoUrl, setVideoUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [showCountdown, setShowCountdown] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { uploadVideo, submitSampleGreeting, userId, video } = useSampleGreetings();

  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const videoRef = useRef(null);
  const timerRef = useRef(null);
  const recordedChunksRef = useRef([]);
  const router=useRouter();

  const scripts = [
    `🎙 Calm, friendly tone
Hey, it's French Montana. I'm officially part of CelebrityX — the AI-powered platform that brings fans closer than ever.

😄 Light, upbeat tone
It's kinda wild. You type a message… and boom — my digital twin says it, just the way I would.

🔥 Confident, hype tone
You need energy? I got you.

🧘 Chill, relaxed tone
Want something low-key and personal? I can keep it calm too.

🎉 Excited, joyful tone
Let's test it out…

😎 Playful or signature tone
From shoutouts to pep talks to just-for-fun messages — we're doing it all.

👋 Warm sign-off tone
I'm ready when you are. This is French Montana, and I can't wait to speak for you — in my own voice — right here on CelebrityX.`
  ];

  const videoConstraints = {
    width: 1280,
    height: 720,
    facingMode: "user"
  };

  const handleBack=()=>{
    router.back();
  }
  useEffect(() => {
    if (currentStep === 3 && videoUrl && videoRef.current) {
      videoRef.current.src = videoUrl;
      videoRef.current.loop = true;
      videoRef.current.autoplay = true;
      videoRef.current.muted = false;
      videoRef.current.controls = false;

      setIsPlaying(true);

      const videoElement = videoRef.current;

      const handlePlay = () => {
        setIsPlaying(true);
      };

      const handlePause = () => {
        setIsPlaying(false);
      };

      videoElement.addEventListener('play', handlePlay);
      videoElement.addEventListener('pause', handlePause);

      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.play().catch(err => {
            console.error("Error playing video:", err);
            setIsPlaying(false);
          });
        }
      }, 300);

      return () => {
        if (videoElement) {
          videoElement.removeEventListener('play', handlePlay);
          videoElement.removeEventListener('pause', handlePause);
        }
      };
    }
  }, [currentStep, videoUrl]);

  const handleDataAvailable = useCallback(({ data }) => {
    if (data.size > 0) {
      recordedChunksRef.current.push(data);
    }
  }, []);

  const startRecording = useCallback(() => {

    // Show countdown first
    setShowCountdown(true);
    onStepChange(2);

    // After countdown completes, start actual recording
    const startActualRecording = () => {
      setShowCountdown(false);
      setIsRecording(true);
      recordedChunksRef.current = [];

      const stream = webcamRef.current?.stream;
      if (!stream) {
        console.error("No webcam stream available");
        return;
      }

      try {
        // Try to use the supported MIME types in order of preference
        const preferredMimeTypes = [
          "video/mp4",
          "video/webm;codecs=vp8,opus",
          "video/webm"
        ];
        
        let selectedOption = null;
        for (const mimeType of preferredMimeTypes) {
          if (MediaRecorder.isTypeSupported && MediaRecorder.isTypeSupported(mimeType)) {
            selectedOption = { mimeType };
            break;
          }
        }
        
        if (selectedOption) {
          mediaRecorderRef.current = new MediaRecorder(stream, selectedOption);
        } else {
          // Fallback to default browser implementation
          mediaRecorderRef.current = new MediaRecorder(stream);
        }
      } catch (error) {
        try {
          // Last resort - try without options
          mediaRecorderRef.current = new MediaRecorder(stream);
        } catch (e2) {
          alert("Your browser doesn't support video recording. Please try a different browser.");
          return;
        }
      }

      mediaRecorderRef.current.ondataavailable = handleDataAvailable;
      mediaRecorderRef.current.onstop = () => {
        if (recordedChunksRef.current.length > 0) {
          // Always use MP4 for better compatibility with backend
          const mimeType = "video/mp4";
          
          // Create the blob
          const blob = new Blob(recordedChunksRef.current, { type: mimeType });
          
          // Convert blob to File object with mp4 extension for backend compatibility
          const videoFile = new File([blob], "recorded-video.mp4", { type: mimeType });
          
          // Store the video file in Redux
          uploadVideo(videoFile);
          
          // Create URL for preview
          const url = URL.createObjectURL(blob);
          setVideoUrl(url);
          onStepChange(3);
        } else {
          // If no chunks, go back to step 1
          onStepChange(1);
          onStepChange(1);
        }
      };

      mediaRecorderRef.current.start(100);

      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => {
          const next = prev + 1;
          if (next >= 60) {
            stopRecording();
          }
          return next;
        });
      }, 1000);
    };

    setTimeout(startActualRecording, 3000); 

  }, [handleDataAvailable, onStepChange]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      setIsRecording(false);

      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Request final data and stop recording
      try {
        if (mediaRecorderRef.current.state === "recording") {
          mediaRecorderRef.current.requestData();
          setTimeout(() => {
            mediaRecorderRef.current.stop();
          }, 200);
        } else {
          console.warn("MediaRecorder not in recording state");
          onStepChange(3);
        }
      } catch (err) {
        console.error("Failed to stop recorder:", err);

        // Try to create video from existing chunks
        if (recordedChunksRef.current.length > 0) {
          try {
            const blob = new Blob(recordedChunksRef.current, { type: "video/webm" });
            const url = URL.createObjectURL(blob);
            setVideoUrl(url);
            onStepChange(3);
          } catch (blobErr) {
            console.error("Error creating blob:", blobErr);
            onStepChange(1);
          }
        } else {
          onStepChange(1);
        }
      }
    }
  }, [isRecording, onStepChange]);

  const renderContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <>
            <div className="absolute top-4 left-0 right-0 flex justify-between px-4 z-10">
              <button className="w-6 h-6" onClick={handleBack}>
                <img src="/images/home/<USER>" alt="Back" className="w-6 h-6" />
              </button>
              <div className="text-center text-white text-sm font-medium">
                Record a Video
              </div>
              <div className="w-6 h-6"></div> {/* Empty div for flex alignment */}
            </div>

            <div className="absolute top-16 left-4 z-10">
              <div className="bg-black/30 px-3 py-1 rounded-md text-white text-xs">
                Script appears here
              </div>
            </div>

            <div className="absolute bottom-10 left-0 right-0 flex justify-center">
              <div className="z-10 relative">
                <RecordButton isRecording={false} onClick={startRecording} />
              </div>
            </div>
          </>
        );

      case 2:
        return (
          <>
            {showCountdown ? (
              <CountdownTimer onComplete={() => setShowCountdown(false)} />
            ) : (
              <>
                {/* Use the new ScrollingScript component instead of ScriptDisplay */}
                <ScrollingScript
                  scripts={scripts}
                  currentScriptIndex={0}
                  recordingTime={recordingTime}
                />
                <Timer time={recordingTime} />
                <div className="absolute bottom-10 left-0 right-0 flex justify-center space-x-8 items-center">
                  <button
                    onClick={() => {
                      clearInterval(timerRef.current);
                      timerRef.current = null;
                      mediaRecorderRef.current?.stop();
                      recordedChunksRef.current = [];
                      setIsRecording(false);
                      onStepChange(1);
                    }}
                    className="w-12 h-12 rounded-full bg-black flex items-center justify-center z-10"
                  >
                    <img src="/delete.svg" alt="Delete" className="w-6 h-6" />
                  </button>

                  <div className="z-10 relative" onClick={(e) => { e.stopPropagation(); stopRecording(); }}>
                    <RecordButton isRecording={true} onClick={stopRecording} />
                  </div>

                  <button
                    onClick={(e) => { e.stopPropagation(); stopRecording(); }}
                    className="w-12 h-12 rounded-full bg-black flex items-center justify-center z-10 relative"
                  >
                    <img src="/Pause.svg" alt="Pause" className="w-6 h-6" />
                  </button>
                </div>
              </>
            )}
          </>
        );

      case 3:
        return (
          <>
            <div className="absolute top-4 left-0 right-0 flex justify-between px-4 z-10">
              <button
                onClick={() => {
                  setVideoUrl(null);
                  onStepChange(1);
                }}
                className="w-6 h-6"
              >
                <img src="/images/close.svg" alt="Close" className="w-6 h-6" />
              </button>
              <div className="text-center text-white text-sm font-medium">
                Preview Video
              </div>
              {isSubmitting ? (
                <div className="w-16 h-6">
                  <Loader variant={LoaderVariants.circular} size={6} circularLoadersize={4} />
                </div>
              ) : (
                <button
                  onClick={async () => {
                    try {
                      setIsSubmitting(true);
                      
                      if (isUpdating && greetingId) {
                        // If updating an existing greeting, use the update API
                        const formData = new FormData();
                        if (video) {
                          formData.append('video', video);
                          await SampleGreetingService.updateSampleGreeting(greetingId, formData);
                          // Navigate back to video preview after update
                          router.push('/video-preview');
                        }
                      } else {
                        // For new greetings, use the existing flow
                        const result = await submitSampleGreeting(userId);
                        onStepChange(4);
                      }
                    } catch (error) {
                      console.error("Error submitting video:", error);
                    } finally {
                      setIsSubmitting(false);
                    }
                  }}
                  className="text-white text-sm"
                >
                  {isUpdating ? 'Update' : 'Submit'}
                </button>
              )}
            </div>

            <div className="absolute bottom-10 left-0 right-0 flex justify-center space-x-8 items-center">
              <button
                onClick={() => {
                  setVideoUrl(null);
                  onStepChange(1);
                }}
                className="w-12 h-12 rounded-full bg-black flex items-center justify-center z-10 relative"
              >
                <img src="/delete.svg" alt="Delete" className="w-6 h-6" />
              </button>

              <div className="w-20 h-20 rounded-full border-2 border-white flex items-center justify-center z-10 relative bg-black">
                <div className="w-12 h-12 rounded-full bg-white"></div>
              </div>

              <button
                onClick={() => {
                  const video = videoRef.current;
                  if (video) {
                    if (video.paused) {
                      video.play().catch(e => console.error("Play error:", e));
                    } else {
                      video.pause();
                    }
                  }
                }}
                className="w-12 h-12 rounded-full bg-black flex items-center justify-center z-10 relative"
              >
                <img
                  src={isPlaying ? "/Pause.svg" : "/Play.svg"}
                  alt={isPlaying ? "Pause" : "Play"}
                  className="w-6 h-6"
                />
              </button>
            </div>
          </>
        );

      case 4:
        return (
          <div className="flex flex-col items-center justify-center h-full p-6 text-center relative" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
            <div className="absolute top-4 left-0 right-0 flex justify-between px-4 z-10">
              <div className="w-6 h-6"></div> {/* Empty div for flex alignment */}
              <div className="text-center text-white text-sm font-medium">
                Submission Complete
              </div>
              <button 
                onClick={() => router.push('/')}
                className="w-6 h-6"
              >
                <img src="/images/close.svg" alt="Close" className="w-6 h-6" />
              </button>
            </div>

            <h2 className="text-xl font-bold mb-6">Sample Greeting Submitted</h2>
            <div className="w-20 h-20 overflow-hidden mb-6">
              <img
                src="/iconuser.png"
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
            <p className="text-white mb-4 font-light text-sm">
              Your sample greeting has been successfully submitted!
            </p>
            <button 
              onClick={() => router.push('/')} 
              className="px-6 py-3 bg-white text-black rounded-lg font-medium mt-4"
            >
              Go to Home
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="relative flex-1 w-full h-full flex flex-col" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
      {(currentStep <= 3) && (
        <div className="w-full h-full absolute inset-0" id="video-container">
          {(currentStep === 1 || currentStep === 2) ? (
            <Webcam
              audio={true}
              ref={webcamRef}
              videoConstraints={videoConstraints}
              muted={true}
              className="w-full h-full object-cover"
            />
          ) : videoUrl ? (
            <div className="relative w-full h-full">
              <video
                ref={videoRef}
                src={videoUrl}
                autoPlay
                playsInline
                loop
                className="w-full h-full object-cover"
                onError={(e) => console.error("Video error:", e)}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
              />
              <div
                className="absolute top-0 left-0 w-full h-full bg-transparent cursor-pointer"
                onClick={() => {
                  const video = videoRef.current;
                  if (video) {
                    if (video.paused) {
                      video.play().catch(e => console.error("Play error:", e));
                    } else {
                      video.pause();
                    }
                  }
                }}
              />
            </div>
          ) : (
            <div className="w-full h-full bg-gray-900 flex items-center justify-center">
              <p className="text-white">Video preview not available</p>
            </div>
          )}
        </div>
      )}
      {renderContent()}
    </div>
  );
};

export default VideoRecorder;
