'use client';

import React, { useState, useEffect } from 'react';

const CountdownTimer = ({ onComplete }) => {
  const [count, setCount] = useState(3);

  useEffect(() => {
    if (count === 0) {
      onComplete();
      return;
    }

    const timer = setTimeout(() => {
      setCount(count - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [count, onComplete]);

  return (
    <div className="absolute inset-0 flex items-center justify-center bg-black/50 z-20">
      <div className="text-center" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
        <h2 className="text-white text-2xl mb-2">Recording in</h2>
        <div className="text-white text-9xl font-bold">{count > 0 ? count : 'Go!'}</div>
      </div>
    </div>
  );
};

export default CountdownTimer;
