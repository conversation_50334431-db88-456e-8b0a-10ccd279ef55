"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { ChevronDownIcon } from "lucide-react";
import persond from "../../../public/person.svg"
// Define wave image path
import edit from "../../../public/edit.svg"
const waveImagePath = "/wave.png";

const VoiceGreeting = () => {
  const router = useRouter();
  const [step, setStep] = useState(1); // 1: Name input, 2: Recording, 3: Review, 4: Success
  const [successMessage, setSuccessMessage] = useState("Your greeting is now uploaded");
  const [greetingName, setGreetingName] = useState("Congratulations");
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState(null);
  const [selectedTone, setSelectedTone] = useState("Happy");
  const [selectedClient, setSelectedClient] = useState("Juliana Huxtable");

  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);

  const formFields = [
    { label: "Title", value: greetingName, hasDropdown: false },
    { label: "Tone", value: selectedTone, hasDropdown: true },
    { label: "Client", value: selectedClient, hasDropdown: true },
  ];

  // Start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);

      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        setStep(3); // Move to review step
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);

      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (err) {
      console.error("Error accessing microphone:", err);
      alert("Could not access the microphone. Please check permissions.");
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop());
      }
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  const handleBack = () => {
    if (step === 1) {
      router.back();
    } else if (step === 2) {
      stopRecording();
      setStep(1);
    } else if (step === 3) {
      setStep(2);
    }
  };

  const handleUpload = () => {
    setSuccessMessage("Your greeting is now uploaded");
    setStep(4);
  };

  const handleSaveToDrafts = () => {
    setSuccessMessage("Your greeting is saved to drafts");
    setStep(4);
  };

  const handleViewGreetings = () => {
    router.push('/greetings');
  };

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (mediaRecorderRef.current && mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop());
      }

      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const handleEditClick = () => {
    setStep(1);
  };

  return (
    <div className="relative flex flex-col mx-auto w-full min-h-screen bg-black overflow-hidden">
      {/* Header */}
      {(step === 1 || step === 2) && (
        <div className="flex w-full px-4 sm:px-6 pt-6 sm:pt-10 pb-2.5 items-center justify-between font-['AG_Book_Pro',-apple-system,Roboto,Helvetica,sans-serif] z-10 relative">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/417a72186825469cac0748b469f94e51/4b3161ead91be42ea302e70a396bd59294ccaa6b?placeholderIfAbsent=true"
            className="aspect-square object-contain object-center w-5 my-auto flex-shrink-0 cursor-pointer"
            onClick={handleBack}
            alt="Back icon"
          />
          <div className="text-white text-base font-medium my-auto absolute left-1/2 -translate-x-1/2">
            Voice Greeting
          </div>
        </div>
      )}

      {/* Main Content (Steps 1 and 2) - Hidden when modal or success screen is active */}
      {(step === 1 || step === 2) && (
        <>
          {/* Greeting name input section */}
          <div className="mt-4 sm:mt-[21px] w-full px-4 sm:px-6 font-['AG_Book_Pro',-apple-system,Roboto,Helvetica,sans-serif]">
            <div className="text-white text-sm font-medium leading-none">
              Greeting Name
            </div>
            {step === 1 ? (
              <div className="flex mt-3 w-full items-center justify-between text-xl sm:text-2xl text-[#6C7278] font-normal tracking-[-0.24px] leading-[1.4]">
                <input
                  type="text"
                  value={greetingName}
                  onChange={(e) => setGreetingName(e.target.value)}
                  placeholder="Enter greeting name"
                  className="bg-transparent text-white text-xl sm:text-2xl font-normal tracking-[-0.24px] leading-[1.4] w-full outline-none"
                  autoFocus
                />
                {greetingName && (
                  <div
                    className="flex my-auto w-4 h-4 flex-shrink-0 cursor-pointer"
                    onClick={() => setGreetingName("")}
                  >
                    <img
                      src={edit.src}
                      alt="Edit"
                      className="w-4 h-4"
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className="flex mt-3 w-full items-center justify-between text-xl sm:text-2xl text-white font-normal tracking-[-0.24px] leading-[1.4]">
                <div className="my-auto">{greetingName}</div>
                {step === 2 && (
                  <img
                    src={edit.src}
                    className="w-4 h-4 cursor-pointer"
                    onClick={handleEditClick}
                    alt="Edit icon"
                  />
                )}
              </div>
            )}
          </div>

          {/* Waveform visualization */}
          {step === 2 && (
            <div className="mt-6 sm:mt-10 w-full px-4 sm:px-6">
              <div className="w-full h-[100px] sm:h-[120px] flex items-center justify-center">
                <img
                  src={waveImagePath}
                  alt="Audio waveform"
                  className="w-full h-full object-contain"
                />
              </div>
              <div className="text-white text-center mt-2">
                {formatTime(recordingTime)}
              </div>
            </div>
          )}

          <div className="border border-white min-h-[1px] mt-auto w-full"></div>

          <div className="flex self-center mt-4 sm:mt-7 mb-26 sm:mb-20 items-center gap-4 sm:gap-[34px] justify-center">
            {/* Delete icon */}
            <div
              className="aspect-square object-contain object-center w-[45px] sm:w-[57px] my-auto flex-shrink-0 cursor-pointer"
              onClick={() => {
                if (isRecording) {
                  stopRecording();
                  setRecordingTime(0);
                }
                setAudioUrl(null);
              }}
            >
              <img
                src="https://cdn.builder.io/api/v1/image/assets/417a72186825469cac0748b469f94e51/1e2764c4d1801a5f6c1283564908a19aff074ab5?placeholderIfAbsent=true"
                className="w-full h-full"
                alt="Delete icon"
              />
            </div>

            {/* Mic button */}
            <div
              className="flex flex-col relative aspect-square my-auto w-[70px] sm:w-[85px] items-center justify-start cursor-pointer"
              onClick={() => {
                if (step === 1) {
                  setStep(2);
                  startRecording();
                } else if (isRecording) {
                  stopRecording();
                } else {
                  startRecording();
                }
              }}
            >
              <div
                className="absolute inset-0 h-full w-full flex items-center justify-center rounded-[88px] border border-[rgba(216,216,216,0.05)] bg-[linear-gradient(135deg,rgba(248,251,255,0.04)_0%,rgba(255,255,255,0)_100%)] shadow-[0px_0px_14.176px_0px_rgba(211,240,253,0.32)_inset,14.176px_7.088px_28.352px_0px_rgba(0,0,0,0.08)] backdrop-blur-[17.72px]"
              >
                {isRecording ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="58" height="58" viewBox="0 0 58 58" fill="none">
                    <path d="M28.8594 35.6562C26.776 35.6562 25.0052 34.9271 23.5469 33.4688C22.0885 32.0104 21.3594 30.2396 21.3594 28.1562V13.1562C21.3594 11.0729 22.0885 9.30208 23.5469 7.84375C25.0052 6.38542 26.776 5.65625 28.8594 5.65625C30.9427 5.65625 32.7135 6.38542 34.1719 7.84375C35.6302 9.30208 36.3594 11.0729 36.3594 13.1562V28.1562C36.3594 30.2396 35.6302 32.0104 34.1719 33.4688C32.7135 34.9271 30.9427 35.6562 28.8594 35.6562ZM26.3594 53.1562V45.4688C22.026 44.8854 18.4427 42.9479 15.6094 39.6563C12.776 36.3646 11.3594 32.5312 11.3594 28.1562H16.3594C16.3594 31.6146 17.5781 34.5625 20.0156 37C22.4531 39.4375 25.401 40.6562 28.8594 40.6562C32.3177 40.6562 35.2656 39.4375 37.7031 37C40.1406 34.5625 41.3594 31.6146 41.3594 28.1562H46.3594C46.3594 32.5312 44.9427 36.3646 42.1094 39.6563C39.276 42.9479 35.6927 44.8854 31.3594 45.4688V53.1562H26.3594Z" fill="#FF0000"/>
                  </svg>
                ) : (
                  <svg
                    className="w-8 h-8 sm:w-10 sm:h-10 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                    ></path>
                  </svg>
                )}
              </div>
            </div>

            {/* Save icon */}
            <div
              className="aspect-[1.02] object-contain object-center w-[45px] sm:w-[58px] my-auto flex-shrink-0 cursor-pointer"
              onClick={() => {
                if (isRecording) {
                  stopRecording();
                }
                setStep(3);
              }}
            >
              <img
                src="https://cdn.builder.io/api/v1/image/assets/417a72186825469cac0748b469f94e51/fd3e25fe1360e2020607bfab279de1c35b779bea?placeholderIfAbsent=true"
                className="w-full h-full"
                alt="Save icon"
              />
            </div>
          </div>
        </>
      )}
      {/* Review Greeting Modal with Header */}
      {step === 3 && (
        <div className="fixed inset-0 flex flex-col z-50 bg-black">
          {/* Keep the header visible */}
          <div className="flex w-full px-4 sm:px-6 pt-6 sm:pt-10 pb-2.5 items-center justify-between font-['AG_Book_Pro',-apple-system,Roboto,Helvetica,sans-serif] relative">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/417a72186825469cac0748b469f94e51/4b3161ead91be42ea302e70a396bd59294ccaa6b?placeholderIfAbsent=true"
              className="aspect-square object-contain object-center w-5 my-auto flex-shrink-0 cursor-pointer"
              onClick={handleBack}
              alt="Back icon"
            />
            <div className="text-white text-base font-medium my-auto absolute left-1/2 -translate-x-1/2">
              Voice Greeting
            </div>
            <div className="text-white text-xs font-normal my-auto cursor-pointer"></div>
          </div>

          {/* Review Modal */}
          <div className="flex-1 flex items-start justify-center overflow-y-auto pt-4 pb-20">
            <div className="flex flex-col w-[90%] max-w-[342px] items-center gap-2 sm:gap-3 p-4 sm:p-5 bg-white rounded-xl shadow-lg mb-20">
              <div className="flex items-start justify-between relative self-stretch w-full">
                <div className="relative w-full text-center font-sans font-normal text-[#090909] text-base tracking-[-0.32px] leading-[25.6px]">
                  Review Greeting
                </div>
                <button
                  onClick={() => setStep(2)}
                  className="absolute right-0 top-0"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>

              <div className="w-full space-y-2 sm:space-y-3">
                {formFields.map((field, index) => (
                  <div
                    key={index}
                    className="flex-col items-start gap-0.5 flex relative self-stretch w-full"
                  >
                    <div className="inline-flex h-[21px] items-center justify-center gap-2.5 relative z-[1] rounded-[100px]">
                      <div className="relative w-fit font-sans font-normal text-[#6c7278] text-xs tracking-[-0.24px] leading-[19.2px] whitespace-nowrap">
                        {field.label}
                      </div>
                    </div>

                    {field.hasDropdown ? (
                      <div className="relative w-full">
                        <select className="h-[36px] sm:h-[40px] w-full px-3 sm:px-3.5 py-[8px] sm:py-[10px] bg-white rounded-lg overflow-hidden border border-solid border-[#edf1f3] shadow-[0px_1px_2px_#e4e5e73d] appearance-none text-sm text-[#1a1c1e] font-sans">
                          <option>{field.value}</option>
                        </select>
                        <ChevronDownIcon className="w-4 h-4 absolute right-3 sm:right-3.5 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                      </div>
                    ) : (
                      <div className="h-[36px] sm:h-[40px] w-full px-3 sm:px-3.5 py-[8px] sm:py-[10px] bg-white rounded-lg overflow-hidden border border-solid border-[#edf1f3] shadow-[0px_1px_2px_#e4e5e73d] flex items-center">
                        <div className="text-sm text-[#1a1c1e] font-sans">{field.value}</div>
                      </div>
                    )}
                  </div>
                ))}

                <div className="flex flex-col items-start relative self-stretch w-full">
                  <div className="flex flex-col items-center justify-end gap-2 sm:gap-3 p-3 sm:p-4 relative self-stretch w-full bg-black rounded-2xl border border-solid border-[#edf1f3] shadow-[0px_4px_6px_-2px_#10182808]">
                    <img
                      className="relative w-full max-w-[264px] h-[40px] sm:h-[50px]"
                      alt="Audio waveform"
                      src={waveImagePath}
                    />
                    <div className="inline-flex items-center gap-1 relative">
                      <div className="relative w-fit mt-[-1.00px] font-sans text-[#f9f8fd] text-[10px] tracking-[0] leading-[12px] whitespace-nowrap">
                        00:00/00:05
                      </div>
                    </div>
                    <div className="inline-flex items-start gap-3 sm:gap-4 relative">
                      {[1, 2, 3].map((index) => (
                        <div
                          key={index}
                          className="relative w-7 h-7 sm:w-8 sm:h-8 rounded-full overflow-hidden shadow-md bg-gradient-to-br from-white/42 to-gray-500/42"
                        >
                          <div
                            className={`absolute ${
                              index === 2
                                ? "w-5 h-5 sm:w-6 sm:h-6 top-1 left-1"
                                : "w-4 h-4 sm:w-5 sm:h-5 top-1.5 left-1.5"
                            } rounded-full bg-cover bg-center`}
                            style={{
                              backgroundImage: `url(https://c.animaapp.com/mamkp10llDI8lo/img/base-icon${
                                index === 1 ? "-2" : index === 2 ? "-1" : ""
                              }.svg)`,
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <button
                  onClick={handleUpload}
                  className="flex h-10 w-full items-center justify-center gap-2 px-[18px] py-2 rounded-lg overflow-hidden border-none shadow-[0px_1px_2px_#1018280d] bg-black text-white"
                >
                  Upload
                </button>

                <button
                  onClick={handleSaveToDrafts}
                  className="flex h-10 w-full items-center justify-center gap-2 px-[18px] py-2 rounded-lg overflow-hidden border-none shadow-[0px_1px_2px_#1018280d,inset_-0.5px_-1px_1px_#00000073,inset_1px_1px_1.67px_#ffffff40] bg-gradient-to-b from-[rgba(214,214,214,0)] to-[rgba(167,167,167,1)] via-[rgba(242,242,242,1)] text-black"
                >
                  Save to drafts
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Success Screen - Centered with Backdrop */}
      {step === 4 && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black">
          <div className="relative w-full max-w-[390px] h-screen max-h-[844px] overflow-hidden bg-black px-4">
            {/* Success message */}
            <div className="absolute w-full max-w-[309px] left-1/2 -translate-x-1/2 top-[25%] font-normal text-white text-xl sm:text-2xl text-center tracking-[0] leading-normal">
              {successMessage}
            </div>

            {/* Person icon with glowing effect */}
            <div className="absolute left-1/2 -translate-x-1/2 top-[40%] -mt-10">
              {/* Glowing circle effect */}
              <div className="relative w-[80px] h-[80px] mx-auto">
                {/* Glow effect behind */}
                <div className="absolute -inset-1 bg-[#ffffff30] rounded-3xl blur-xl"></div>
                {/* Icon container */}
                <div className="absolute w-full h-full rounded-2xl bg-[#ffffff20] backdrop-blur-md overflow-hidden flex items-center justify-center">
                  <img
                    src={persond.src}
                    alt="Person"
                    className="w-8 h-8"
                  />
                </div>
              </div>
            </div>

            <div className="absolute w-[90%] max-w-[287px] left-1/2 -translate-x-1/2 top-[55%] font-normal text-white text-sm text-center tracking-[-0.28px] leading-[22.4px]">
              Nice work — your greeting is now part of your public profile.
            </div>

            {/* Action buttons */}
            <div className="absolute bottom-32 sm:bottom-40 left-1/2 -translate-x-1/2 w-[90%] max-w-[346px]">
            <button
             onClick={handleViewGreetings}
                className="flex w-full h-11 sm:h-12 px-6 py-2.5 justify-center items-center gap-2.5 rounded-lg border border-solid border-black bg-gradient-to-b from-[rgba(255,255,255,0.2)] to-[rgba(255,255,255,0)] to-100% bg-black text-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.48)] hover:bg-gray-900"
              >
        View Greetings
              </button>
            </div>


          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceGreeting;
