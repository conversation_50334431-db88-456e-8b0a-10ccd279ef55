"use client";

import { useFormik<PERSON>ontext, <PERSON><PERSON><PERSON>y, useField } from "formik";
import { useRef } from "react";

const Step2 = () => {
  const { values, setFieldValue } = useFormikContext();
  const inputRef = useRef(null);

  const addTag = (e) => {
    e.preventDefault();
    const newTag = inputRef.current.value.trim();

    if (newTag && values.tags.length < 5 && !values.tags.includes(newTag)) {
      setFieldValue("tags", [...values.tags, newTag]);
      inputRef.current.value = "";
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      addTag(e);
    }
  };

  const removeTag = (index) => {
    const updatedTags = values.tags.filter((_, i) => i !== index);
    setFieldValue("tags", updatedTags);
  };

  return (
    <div className="p-4">
      <div className="mb-4">
        <div className="relative flex-grow">
          <input
            type="text"
            ref={inputRef}
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 pl-3 outline-none"
            placeholder="(e.g. Rapper, Comedian, LA Native)"
            onKeyDown={handleKeyDown}
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 flex justify-center items-center text-xs rounded-3xl border border-black bg-black text-white px-2 py-1 cursor-pointer"
            onClick={addTag}
          >
            Add
          </button>
        </div>

        <div className="flex flex-wrap gap-2 mt-3">
          {values.tags.map((tag, index) => (
            <div
              key={index}
              className="flex items-center bg-gray-100 rounded-full px-3 py-1 text-sm"
            >
              {tag}
              <button
                type="button"
                onClick={() => removeTag(index)}
                className="ml-1 text-gray-500 hover:text-gray-700 cursor-pointer"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Step2;
