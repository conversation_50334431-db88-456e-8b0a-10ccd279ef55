"use client";

import { useFormikContext } from "formik";

const Step7 = () => {
  const { values, setFieldValue } = useFormikContext();

  const handleAmountChange = (e) => {
    const value = e.target.value.replace(/[^0-9.]/g, "");
    setFieldValue("pricing.amount", value);
  };

  const handleExtraAmountChange = (e) => {
    const value = e.target.value.replace(/[^0-9.]/g, "");
    setFieldValue("pricing.extraCharacterAmount", value);
  };

  const handleCharacterCountChange = (e) => {
    const value = parseInt(e.target.value) || 0;
    setFieldValue("pricing.characterCount", value);
  };

  const handleExtraCharacterCountChange = (e) => {
    const value = parseInt(e.target.value) || 0;
    setFieldValue("pricing.extraCharacterCount", value);
  };

  return (
    <div>
      {/* Character Count */}
      <div className="mb-4">
        <label className="block text-xs text-[#6C7278] mb-1">
          Number of characters
        </label>
        <div className="relative">
          <input
            type="text"
            value={values.pricing.characterCount}
            onChange={handleCharacterCountChange}
            className="w-full p-2 border border-gray-300 text-sm rounded-md outline-none"
            placeholder="0"
          />
        </div>
      </div>

      {/* Amount */}
      <div className="mb-4">
        <label className="block text-xs text-[#6C7278] mb-1">Amount</label>
        <div className="relative">
          <input
            type="text"
            value={values.pricing.amount}
            placeholder="CBLX"
            onChange={handleAmountChange}
            className="w-full p-2 border border-gray-300 text-sm rounded-md outline-none"
          />
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-xs text-[#6C7278] mb-1">
          Extra number of characters{" "}
        </label>
        <div className="relative">
          <input
            type="text"
            value={values.pricing.extraCharacterCount}
            onChange={handleExtraCharacterCountChange}
            className="w-full p-2 border border-gray-300 text-sm rounded-md outline-none"
            placeholder="0"
          />
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-xs text-[#6C7278] mb-1">
          Amount for extra characters
        </label>
        <div className="relative">
          <input
            type="text"
            value={values.pricing.extraCharacterAmount}
            placeholder="$"
            onChange={handleExtraAmountChange}
            className="w-full p-2 border border-gray-300 text-sm rounded-md outline-none"
          />
        </div>
      </div>
    </div>
  );
};

export default Step7;
