"use client";

import { useFormikContext, Field } from "formik";

const Step3 = () => {
  const { values, setFieldValue } = useFormikContext();

  return (
    <div className="space-y-4">
      <label htmlFor="bio" className="block text-xs text-[#6C7278] mb-2">
        Bio
      </label>
      <Field
        as="textarea"
        name="bio"
        id="bio"
        maxLength={200}
        value={values.bio}
        onChange={(e) => setFieldValue("bio", e.target.value)}
        className="w-full p-3 border rounded-lg min-h-[200px] h-full outline-none"
        placeholder="Tell us about yourself..."
      />
      <div className="text-right text-gray-500 text-sm">
        {values.bio.length} / 200 characters
      </div>
    </div>
  );
};

export default Step3;
