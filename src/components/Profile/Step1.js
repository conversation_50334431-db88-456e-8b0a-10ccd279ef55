// components/Profile/Step1.js
"use client";

import { selectAuth } from "@/redux/slices/auth/selectors";
import { Field, FieldArray, useFormikContext } from "formik";
import Image from "next/image";
import { useSelector } from "react-redux";

const Step1 = ({ values, errors, touched, categories }) => {
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const { setFieldValue } = useFormikContext();

  const handleImageChange = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      setFieldValue("profileImage", file);
    }
  };

  return (
    <>
      {/* Profile Picture */}
      <div className="flex justify-center">
        <div className="relative">
          <div className="bg-[#B2B2B233] w-20 h-20 rounded-full flex items-center justify-center overflow-hidden">
            {values.profileImage ? (
              <Image
                src={
                  typeof values.profileImage != "string"
                    ? URL.createObjectURL(values.profileImage)
                    : values.profileImage
                }
                alt="Profile"
                width={80}
                height={80}
                className="rounded-full object-cover"
              />
            ) : (
              <Image
                src="/person.svg"
                alt="Default Profile"
                width={48}
                height={48}
              />
            )}
          </div>
          <input
            type="file"
            id="profileImageInput"
            accept="image/*"
            className="hidden"
            onChange={handleImageChange}
          />
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              document.getElementById("profileImageInput")?.click();
            }}
            className="absolute bottom-0 right-[-7px] bg-black w-8 h-8 rounded-full flex items-center justify-center cursor-pointer"
          >
            <Image src="/edit.svg" alt="Edit" width={16} height={16} />
          </button>
        </div>
      </div>

      <div className="text-center text-sm text-[#ACB5BB] my-6">
        User ID: {user?._id}
      </div>

      {/* Input Fields */}
      <div className="space-y-2 mb-4">
        <div className="flex gap-2">
          <div className="flex flex-col gap-1">
            <label htmlFor="firstname" className="text-xs text-[#6C7278]">
              First Name
            </label>
            <Field
              name="firstName"
              type="text"
              id="firstname"
              className={`w-full px-[14px] py-[12.5px] rounded-lg border text-sm ${
                errors.firstName && touched.firstName
                  ? "border-red-500"
                  : "border-[#EDF1F3]"
              } outline-none`}
            />
            {errors.firstName && touched.firstName && (
              <div className="text-red-500 text-xs">{errors.firstName}</div>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <label htmlFor="lastname" className="text-xs text-[#6C7278]">
              Last Name
            </label>
            <Field
              name="lastName"
              type="text"
              id="lastname"
              className={`w-full px-[14px] py-[12.5px] rounded-lg border text-sm ${
                errors.lastName && touched.lastName
                  ? "border-red-500"
                  : "border-[#EDF1F3]"
              } outline-none`}
            />
            {errors.lastName && touched.lastName && (
              <div className="text-red-500 text-xs">{errors.lastName}</div>
            )}
          </div>
        </div>
        {user?.role === "celebrity" && (
          <>
            <div className="flex flex-col gap-1 mt-4">
              <label htmlFor="name" className="text-xs text-[#6C7278]">
                Celebrity Name
              </label>
              <Field
                name="name"
                type="text"
                id="name"
                placeholder="Celebrity name"
                className={`w-full px-[14px] py-[12.5px] rounded-lg border text-sm  ${
                  errors.name && touched.name
                    ? "border-red-500"
                    : "border-[#EDF1F3]"
                } outline-none`}
              />
              {errors.name && touched.name && (
                <div className="text-red-500 text-xs">{errors.name}</div>
              )}
            </div>
            <div className="mt-4">
              <label className="block text-xs text-[#6C7278] mb-[2px]">
                Title
              </label>
              <FieldArray name="roles">
                {({ push, remove }) => (
                  <div className="flex flex-col gap-3">
                    {categories.length > 0 &&
                      values.roles.length > 0 &&
                      values.roles.map((role, index) => (
                        <div
                          key={index}
                          className="flex justify-between w-full px-[14px] py-[12.5px] rounded-lg border border-[#EDF1F3] outline-none text-sm text-[#1A1C1E]"
                        >
                          <span>
                            {
                              categories.find((item) => item?._id === role)
                                ?.name
                            }
                          </span>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              remove(index);
                            }}
                            className="text-gray-500"
                          >
                            <Image
                              src="/X.svg"
                              height={16}
                              width={16}
                              alt="x-icon"
                              className="cursor-pointer"
                            />
                          </button>
                        </div>
                      ))}
                    <Field
                      as="select"
                      name="newRole"
                      onChange={(e) => {
                        const selectedRole = e.target.value;
                        if (
                          selectedRole &&
                          !values.roles.includes(selectedRole)
                        ) {
                          push(selectedRole);
                        }
                        e.target.value = "";
                      }}
                      className="w-full px-[14px] py-[12.5px] rounded-lg border border-[#EDF1F3] outline-none mt-3 text-sm text-[#1A1C1E]"
                    >
                      <option value="">Select role</option>
                      {categories.map((category, index) => (
                        <option key={index} value={category?._id}>
                          {category?.name}
                        </option>
                      ))}
                    </Field>
                    {errors.roles && (
                      <div className="text-red-500 text-xs">{errors.roles}</div>
                    )}
                    <button
                      type="button"
                      className="flex flex-start mt-3 text-xs"
                      onClick={(e) => {
                        e.preventDefault();
                        push("");
                      }}
                    >
                      {/* <span>+</span> <span className="underline">Add more</span> */}
                    </button>
                  </div>
                )}
              </FieldArray>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default Step1;
