"use client";

import { useFormikContext, FieldArray } from "formik";

const Step6 = () => {
  const { values, setFieldValue } = useFormikContext();

  const addMoreLink = (e) => {
    e.preventDefault();
    setFieldValue("socialLinks", [...values.socialLinks, ""]);
  };

  const updateLink = (index, newValue) => {
    const updatedLinks = [...values.socialLinks];
    updatedLinks[index] = newValue;
    setFieldValue("socialLinks", updatedLinks);
  };

  const removeLink = (index) => {
    if (values.socialLinks.length > 1) {
      const updatedLinks = values.socialLinks.filter((_, i) => i !== index);
      setFieldValue("socialLinks", updatedLinks);
    }
  };

  return (
    <div className="p-4">
      <FieldArray
        name="socialLinks"
        render={() => (
          <div className="space-y-3">
            {values.socialLinks.map((link, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    value={link}
                    onChange={(e) => updateLink(index, e.target.value)}
                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 pl-3"
                    placeholder="www.instagram.com/"
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    @
                  </span>
                </div>
                {values.socialLinks.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeLink(index)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ×
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              className="mt-3 text-xs"
              onClick={addMoreLink}
            >
              <span>+</span> <span className="underline">Add more</span>
            </button>
          </div>
        )}
      />
    </div>
  );
};

export default Step6;
