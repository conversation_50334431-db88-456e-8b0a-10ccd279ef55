"use client";

import { useFormikContext, FieldArray } from "formik";

const Step5 = () => {
  const { values, setFieldValue } = useFormikContext();

  const tones = [
    { name: "Friendly", emoji: "😊" },
    { name: "Playful /Funny", emoji: "😂" },
    { name: "Inspirational", emoji: "✨" },
    { name: "Heartfelt", emoji: "❤️" },
    { name: "Confident", emoji: "💪" },
    { name: "Cool & Casual", emoji: "😎" },
    { name: "Classy / Glamorous", emoji: "💃" },
    { name: "Dramatic", emoji: "🎭" },
    { name: "Personal/Warm", emoji: "🤗" },
  ];

  const toggleTone = (name) => {
    if (values.tones.includes(name)) {
      setFieldValue(
        "tones",
        values.tones.filter((item) => item !== name)
      );
    } else {
      setFieldValue("tones", [...values.tones, name]);
    }
  };

  return (
    <div className="p-4">
      <FieldArray
        name="tones"
        render={() => (
          <div className="flex flex-wrap items-center justify-center gap-1 mb-6">
            {tones.map((tone) => (
              <button
                key={tone.name}
                type="button"
                onClick={() => toggleTone(tone.name)}
                className={`flex gap-1 items-center p-2 rounded-[24px] border-1 transition-all duration-200 cursor-pointer ${
                  values.tones.includes(tone.name)
                    ? "border-[#7F56D9] bg-[#F9F5FF]"
                    : "border-[#EAECF0] bg-white hover:bg-gray-50"
                }`}
              >
                <span className="text-xl mr-2">{tone.emoji}</span>
                <span className="font-normal text-sm text-[#101828]">
                  {tone.name}
                </span>
              </button>
            ))}
          </div>
        )}
      />
    </div>
  );
};

export default Step5;
