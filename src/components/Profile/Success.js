"use client";

import Link from "next/link";

const Success = ({ isGreeting }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-80 z-50 pageBgGradient">
      <div className=" max-w-md m-auto p-10 w-full h-screen flex-col flex items-center justify-center">
        <div className="flex flex-col items-center gap-[53px] text-white flex-1 justify-center ">
          <h2 className="text-2xl font-semibold">
            {isGreeting
              ? "Your greeting is ready!"
              : "Your profile is now live"}
          </h2>
          <div
            className="relative flex justify-center p-[14.176px] items-center gap-[17.72px] rounded-[21.264px] 
    border-[1.418px] border-[rgba(216,216,216,0.05)] 
    bg-gradient-to-br from-[rgba(248,251,255,0.04)] to-transparent 
    shadow-[0px_0px_14.176px_rgba(211,240,253,0.32)_inset,14.176px_7.088px_28.352px_rgba(0,0,0,0.08)] 
    backdrop-blur-[17.72px] w-[85px] h-[85px]"
          >
            <div className="absolute">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="326"
                height="327"
                viewBox="0 0 326 327"
                fill="none"
              >
                <g filter="url(#filter0_f_75_9416)">
                  <circle
                    cx="162.825"
                    cy="163.33"
                    r="82.7585"
                    fill="white"
                    fill-opacity="0.26"
                  />
                </g>
                <defs>
                  <filter
                    id="filter0_f_75_9416"
                    x="0.0664062"
                    y="0.571289"
                    width="325.516"
                    height="325.517"
                    filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB"
                  >
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend
                      mode="normal"
                      in="SourceGraphic"
                      in2="BackgroundImageFix"
                      result="shape"
                    />
                    <feGaussianBlur
                      stdDeviation="40"
                      result="effect1_foregroundBlur_75_9416"
                    />
                  </filter>
                </defs>
              </svg>
            </div>

            <img src="/user.svg" alt="Profile" width={32} height={32} />
          </div>

          <p className="text-center text-sm mt-2">
            {isGreeting
              ? "Preview it on your profile or share it with friends"
              : " You’re all set. Fans can now discover your voice and request personalized greetings or set up your video greeting"}
          </p>
        </div>
        <div className="flex flex-col mb-20 w-full">
          {isGreeting ? (
            <button
              href="/"
              className="w-full m-auto text-sm font-semibold grayButton text-white transition-all relative rounded-lg border border-white/5 max-w-80 h-14 flex gap-2 items-center justify-center text-center cursor-pointer hover:opacity-80"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
              >
                <path
                  d="M6.70812 10.5563L11.8306 13.5413M11.8231 5.30633L6.70812 8.29133M16.0156 4.17383C16.0156 5.41647 15.0083 6.42383 13.7656 6.42383C12.523 6.42383 11.5156 5.41647 11.5156 4.17383C11.5156 2.93119 12.523 1.92383 13.7656 1.92383C15.0083 1.92383 16.0156 2.93119 16.0156 4.17383ZM7.01562 9.42383C7.01562 10.6665 6.00827 11.6738 4.76562 11.6738C3.52298 11.6738 2.51562 10.6665 2.51562 9.42383C2.51562 8.18119 3.52298 7.17383 4.76562 7.17383C6.00827 7.17383 7.01562 8.18119 7.01562 9.42383ZM16.0156 14.6738C16.0156 15.9165 15.0083 16.9238 13.7656 16.9238C12.523 16.9238 11.5156 15.9165 11.5156 14.6738C11.5156 13.4312 12.523 12.4238 13.7656 12.4238C15.0083 12.4238 16.0156 13.4312 16.0156 14.6738Z"
                  stroke="white"
                  stroke-width="1.2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Share celebrityX greetings
            </button>
          ) : (
            <Link
              href="/"
              className="w-full m-auto text-sm font-semibold grayButton text-white transition-all relative rounded-lg border border-white/5 max-w-80 h-14 flex items-center justify-center text-center cursor-pointer hover:opacity-80"
            >
              Setup video greetings
            </Link>
          )}

          {isGreeting ? (
            <button className="w-full m-auto text-sm font-semibold py-2 rounded-md bg-white btnGrayGradient  text-black mt-4  max-w-80 text-center  h-14  flex gap-2 items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
              >
                <path
                  d="M16.5156 11.6738V14.6738C16.5156 15.0717 16.3576 15.4532 16.0763 15.7345C15.795 16.0158 15.4134 16.1738 15.0156 16.1738H4.51562C4.1178 16.1738 3.73627 16.0158 3.45496 15.7345C3.17366 15.4532 3.01563 15.0717 3.01562 14.6738V11.6738M6.01562 7.92383L9.76562 11.6738M9.76562 11.6738L13.5156 7.92383M9.76562 11.6738V2.67383"
                  stroke="black"
                  stroke-width="1.3"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Download
            </button>
          ) : (
            <Link
              href="/profile"
              className="w-full m-auto text-sm font-semibold py-2 rounded-md bg-white btnGrayGradient  text-black mt-4  max-w-80 text-center  h-14  flex items-center justify-center"
            >
              View Profile
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Success;
