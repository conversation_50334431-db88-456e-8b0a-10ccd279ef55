'use client';

import Link from 'next/link';
import {
  HomeIcon,
  VideoCameraIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  UserCircleIcon,
  BellIcon,
  HeartIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  LifebuoyIcon,
} from '@heroicons/react/24/outline';

const sidebarLinks = [
  {
    title: 'Dashboard',
    path: '/customer/home',
    icon: <HomeIcon className='w-5 h-5' />,
  },
  {
    title: 'My Videos',
    path: '/customer/dashboard/videos',
    icon: <VideoCameraIcon className='w-5 h-5' />,
  },
  // {
  //   title: 'Orders',
  //   path: '/customer/orders',
  //   icon: <ShoppingCartIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Payments',
  //   path: '/customer/payments',
  //   icon: <CreditCardIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Profile',
  //   path: '/customer/profile',
  //   icon: <UserCircleIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Notifications',
  //   path: '/customer/notifications',
  //   icon: <BellIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Favorites',
  //   path: '/customer/favorites',
  //   icon: <HeartIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'History',
  //   path: '/customer/history',
  //   icon: <DocumentTextIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Settings',
  //   path: '/customer/settings',
  //   icon: <Cog6ToothIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Support',
  //   path: '/customer/support',
  //   icon: <LifebuoyIcon className='w-5 h-5' />,
  // },
];

export default function CustomerSidebar() {
  return (
    <aside className='relative w-64 h-screen bg-[#0c0c0c] shadow-lg z-10'>
      {/* Gradient Right Border */}
      <div className='absolute top-0 right-0 w-[2px] h-full bg-gradient-to-b from-white/0 via-white/50 to-white/0'></div>
      <nav className='mt-6 space-y-2 px-4'>
        {sidebarLinks.map(({ title, path, icon }) => (
          <Link
            key={title}
            href={path}
            className='flex items-center gap-3 py-2 px-3 rounded hover:bg-indigo-100 hover:text-black text-white'
          >
            {icon}
            <span>{title}</span>
          </Link>
        ))}
      </nav>
    </aside>
  );
} 