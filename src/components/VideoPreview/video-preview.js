"use client";
import React, { useState, useRef, useEffect } from 'react';
import { ArrowLeft, Share2, MoreVertical, Play, Pause, ChevronLeft, Home } from 'lucide-react';
import SampleGreetingService from '@/services/SampleGreetingService';
import { useRouter } from 'next/navigation';
import { Loader, LoaderVariants } from '@/components/common/Loader';

const VideoPreview = ({ videoSrc, avatar, greeting }) => {
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);
  const [approved, setApproved] = useState(false);
  const [showGreetingInfo, setShowGreetingInfo] = useState(false);
  const [greetingName, setGreetingName] = useState('');
  const [tags, setTags] = useState([]);
  const [currentTag, setCurrentTag] = useState('');
  const [showFinalStep, setShowFinalStep] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const videoRef = useRef(null);
  const router = useRouter();

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(err => {
        console.error("Error auto-playing video:", err);
        setIsPlaying(false);
      });
    }
  }, [videoSrc]);

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && videoRef.current.duration) {
      // Get the actual video duration from the video element
      setVideoDuration(videoRef.current.duration);
      setCurrentTime(0);
    }
  };

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleApprove = () => {
    setApproved(true);
    setShowGreetingInfo(true);
  };

  const addTag = () => {
    if (currentTag.trim()) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (index) => {
    const updatedTags = [...tags];
    updatedTags.splice(index, 1);
    setTags(updatedTags);
  };

  const handleUploadGreeting = async () => {
    if (!greeting || !greeting.id) return;
    if (isUploading) return; // Prevent multiple submissions
    
    setIsUploading(true);
    const formData = new FormData();
    formData.append('title', greetingName || 'My Greeting');
    formData.append('script', 'This is a static script for the greeting.');
    tags.forEach(tag => formData.append('tags', tag));
    
    try {
      await SampleGreetingService.updateSampleGreeting(greeting.id, formData);
      setShowGreetingInfo(false);
      setShowFinalStep(true);
    } catch (e) {
      console.error("Error uploading greeting:", e);
    } finally {
      setIsUploading(false);
    }
  };

  const videoPoster = avatar || undefined;

  const handleNavigateToRecordVideo = () => {
    if (greeting && greeting.id) {
      router.push(`/record-video?greetingId=${greeting.id}`);
    } else {
      router.push('/record-video');
    }
  };

  const handleNavigateToUploadPhoto = () => {
    if (greeting && greeting.id) {
      router.push(`/upload-photo?greetingId=${greeting.id}`);
    } else {
      router.push('/upload-photo');
    }
  };

  return (
    <div className="relative h-screen w-full bg-black text-white overflow-hidden" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
      {/* Show avatar as thumbnail above video */}
      {/* {avatar && (
        <div className="flex justify-center pt-8">
          <img
            src={avatar}
            alt="Avatar Thumbnail"
            className="w-24 h-24 object-cover rounded-full border-4 border-white shadow-lg"
          />
        </div>
      )} */}

      {showFinalStep ? (
        <div className="relative h-screen w-full flex flex-col items-center justify-between bg-black p-6">
          <div className="absolute top-4 right-4">
            <button className="p-2 text-white rounded-full transition-colors">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="21"
                height="21"
                viewBox="0 0 21 21"
                fill="none"
              >
                <path
                  d="M15.75 5.68115L5.75 15.6812M5.75 5.68115L15.75 15.6812"
                  stroke="white"
                  strokeWidth="1.2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>

          {/* Main content */}
          <div className="flex flex-col items-center text-center mt-auto mb-8">
            <h2 className="text-white text-center font-medium leading-normal text-2xl mb-2">
              Your greeting is now<br />uploaded
            </h2>
            <p className="text-gray-300 text-sm mb-6">
              Nice work — your greeting is now part of your public profile.
            </p>
            <img
              src="/record.png"
              alt="Selected Photo"
              className="w-34 h-34 object-cover rounded-full mb-4 translate-x-4"
            />
          </div>

          <div className="w-full max-w-md px-4 space-y-4 mb-6">
            <button
              className="w-full py-3 text-white font-medium rounded-lg flex items-center justify-center"
              style={{
                borderRadius: '8px',
                border: '1px solid #000',
                background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000',
                boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.48)'
              }}
            >
              <Share2 size={18} className="mr-2" />
              Share celebrity's greeting
            </button>
            <button
              className="w-full py-3 text-black font-medium rounded-lg flex items-center justify-center"
              style={{
                borderRadius: '8px',
                border: '1px solid #000',
                background: 'linear-gradient(180deg, rgba(214, 214, 214, 0.00) 0%, #A7A7A7 100%), #F2F2F2',
                boxShadow: '0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset, -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset, 0px 1px 2px 0px rgba(16, 24, 40, 0.05)'
              }}
            >
              <Home size={18} className="mr-2" />
              View profile
            </button>
          </div>
        </div>
      ) : showGreetingInfo ? (
        <div className="relative h-full w-full">
          {/* Keep video in background */}
          <video
            ref={videoRef}
            src={videoSrc}
            poster={videoPoster}
            className="h-full w-full object-cover opacity-50"
            loop
            playsInline
            muted
          />

          {/* Overlay content */}
          <div className="absolute inset-0 flex flex-col">
            <div className="top-4 left-0 right-0 flex justify-between px-4 z-10">
              <button
                onClick={() => setShowGreetingInfo(false)}
                className="w-6 h-6"
              >
                <ArrowLeft size={24} />
              </button>
              <div className="text-center text-white text-sm font-medium">
                Greeting Info
              </div>
              <div className="w-6 h-6"></div>
            </div>
            <div className="flex-1 flex flex-col justify-end items-center">
              <div className="w-full"
                style={{
                  background: 'linear-gradient(180deg, #000 0%, #242424 100%), #FFF',
                  borderTopLeftRadius: '16px',
                  borderTopRightRadius: '16px',
                  padding: '16px 16px 32px 16px',
                }}>
                {/* Greeting Name Field */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-white">Greeting Name</h3>
                    <button className="text-gray-400">
                   <img src="/edit.svg" alt="Close" className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={greetingName}
                      onChange={(e) => setGreetingName(e.target.value)}
                      placeholder="Enter greeting name"
                      className="w-full bg-transparent text-white text-lg p-3 focus:outline-none border-0"
                    />
                  </div>
                </div>

                {/* Tags Field */}
                <div className="mb-4">
                  <div className="relative">
                    <input
                      type="text"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      placeholder="(e.g. happy, emotional, etc)"
                      className="w-full bg-transparent border border-gray-700 rounded-lg p-3 text-white focus:outline-none"
                    />
                    {currentTag && (
                     <button
  onClick={addTag}
  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm bg-white/20 px-3 py-1 rounded-3xl border border-black"
>
  Add tag
</button>
                    )}
                  </div>
                </div>

                {/* Display Tags */}
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {tags.map((tag, index) => (
                      <div key={index} className="bg-black/20 rounded-full px-3 py-1 text-sm flex items-center border border-gray-600">
                        {tag}
                        <button
                          onClick={() => removeTag(index)}
                          className="ml-2 text-gray-400 hover:text-white"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {tags.length > 0 && (
                  <button
                    onClick={handleUploadGreeting}
                    disabled={isUploading}
                    className="w-full py-3 text-white font-medium rounded-lg mt-4 flex items-center justify-center"
                    style={{
                      borderRadius: '8px',
                      border: '1px solid #000',
                      background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000',
                      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.48)',
                      opacity: isUploading ? 0.7 : 1
                    }}
                  >
                    {isUploading ? (
                      <Loader variant={LoaderVariants.circular} size={8} circularLoadersize={6} />
                    ) : (
                      'Upload greeting'
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative h-full w-full">
          <video
            ref={videoRef}
            src={videoSrc}
            poster={videoPoster}
            className="h-full w-full object-cover"
            onClick={togglePlayPause}
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            loop
            playsInline
          />

          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center">
              <button
                className="h-20 w-20 bg-black/30 rounded-full flex items-center justify-center transition-transform hover:scale-110"
                onClick={togglePlayPause}
              >
                <Play size={40} className="text-white ml-2" />
              </button>
            </div>
          )}

          <div className="absolute top-0 left-0 right-0 p-4 flex justify-between items-center">
            <button className="p-2   ">
              <ChevronLeft size={24} />
            </button>
            <h2 className="text-lg font-medium">Preview Video</h2>
            <div className="flex gap-3">
              <button className="p-2  ">
            <img src="/Download.svg" alt="Info" className="w-5 h-5" />
              </button>
              <button className="p-2  ">
               <img src="/share.svg" alt="Info" className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-black p-4">
            {/* Progress Bar */}
            <div className="mb-4">
              <div className="h-1 w-full bg-gray-800 rounded-full overflow-hidden">
               <div
  className="h-full"
  style={{
    width: videoDuration ? `${(currentTime / videoDuration) * 100}%` : '0%',
    backgroundColor: 'white',
    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
  }}
></div>

              </div>
              <div className="flex justify-between text-sm mt-2 text-white">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(videoDuration)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-3 gap-2 pt-2">
              <button 
                onClick={handleNavigateToRecordVideo}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
              >
                <div className="h-12 w-12 rounded-full flex items-center justify-center mb-1"
                  style={{
                    background: 'linear-gradient(180deg, #000 0%, #242424 100%), #FFF',
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}>
                  <img src="/re-record.svg" alt="Re-record" className="w-5 h-5" />
                </div>
                <span className="text-xs mt-1">Re-record script</span>
              </button>

              <button
                onClick={handleApprove}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
              >
                <div className="h-14 w-14 rounded-full bg-green-500 flex items-center justify-center mb-1"
                  style={{
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}>
                  <img src="/looks-great.svg" alt="Looks great" className="w-6 h-6" />
                </div>
                <span className="text-xs mt-1">Looks great</span>
              </button>

              <button 
                onClick={handleNavigateToUploadPhoto}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
              >
                <div className="h-12 w-12 rounded-full flex items-center justify-center mb-1"
                  style={{
                    background: 'linear-gradient(180deg, #000 0%, #242424 100%), #FFF',
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}>
                  <img src="/change-photo.svg" alt="Change photo" className="w-5 h-5" />
                </div>
                <span className="text-xs mt-1">Change photo</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPreview;