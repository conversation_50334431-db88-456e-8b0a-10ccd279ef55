"use client";

import { useState } from "react";
import {
  UserIcon,
  HomeIcon,
  ArrowRightOnRectangleIcon,
} from "@heroicons/react/24/outline";
import { usePathname } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import Link from "next/link";
import ProfileDropdown from "./common/ProfileDropdown";
import { logoutUser } from "@/redux/slices/auth/slice";
import { useRouter } from "next/navigation";

export default function Navbar() {
  const router = useRouter();
  const dispatch = useDispatch();
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const initials = user?.firstName?.charAt(0)?.toUpperCase();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const pathname = usePathname();
  const menuItems = [
    {
      label: "Dashboard",
      icon: "home",
      url: user?.role === "celebrity" ? "/celebrity/home" : "/customer/home",
    },
    { label: "Discover", icon: "discover", url: "/customer/discover" },
    { label: "Greetings", icon: "greet", url: user?.role === "celebrity" ? "/celebrity/greetings" : "/customer/greetings" },
    { label: "Create", icon: "create", url: "/celebrity/create-greeting" },
    {
      label: "Profile",
      icon: "profile",
      url: "/profile",
    },
  ];

  const handleLogout = () => {
    dispatch(logoutUser());
    router.push("/auth/start");
  };

  const filteredMenuItems = menuItems.filter((item) => {
    if (item.label === "Discover" && user?.role === "celebrity") {
      return false;
    }
    if (item.label === "Create" && user?.role === "customer") {
      return false;
    }
    return true;
  });

  return (
    <nav className="navbarBgGradient fixed bottom-0 left-0 right-0 z-50 h-24">
      <div className="mx-auto max-w-md  m-auto">
        <div className="grid grid-cols-4 py-4 cursor-pointer">
          {filteredMenuItems.map((item, index) => {
            const isActive = pathname === item.url;

            return (
              <div
                key={item.label + index}
                className={`flex justify-center items-center ${
                  isActive ? "text-white" : "text-white/60 hover:text-white"
                }`}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <Link
                  href={item.url}
                  className="flex justify-center items-center flex-col gap-2"
                >
                  <img
                    src={
                      isActive || hoveredIndex === index
                        ? `/images/home/<USER>
                        : `/images/home/<USER>
                    }
                    className="w-6"
                  />
                  <span className={isActive ? "font-semibold" : ""}>
                    {item.label}
                  </span>
                </Link>
              </div>
            );
          })}
        </div>
        <div className="hidden h-16 justify-between ">
          <div className="flex">
            <div className="flex flex-shrink-0 items-center">
              <Link href="/" className="text-2xl font-bold text-white">
                CelebGreetings
              </Link>
            </div>
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              <Link
                href="/celebrity"
                className="inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-gray-300 hover:border-white hover:text-white"
              >
                Celebrities
              </Link>
              <Link
                href="/how-it-works"
                className="inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-gray-300 hover:border-white hover:text-white"
              >
                How It Works
              </Link>
              <Link
                href="/pricing"
                className="inline-flex items-center border-b-2 border-transparent px-1 pt-1 text-sm font-medium text-gray-300 hover:border-white hover:text-white"
              >
                Pricing
              </Link>
            </div>
          </div>
          <div className="hidden sm:ml-6 sm:flex sm:items-center">
            {user ? (
              <ProfileDropdown />
            ) : (
              <Link
                href="/auth/start"
                className="rounded-md bg-white px-3.5 py-2 text-sm font-semibold text-black shadow-sm hover:bg-gray-200"
              >
                Sign in
              </Link>
            )}
          </div>

          <div className="-mr-2 flex items-center sm:hidden">
            {/* Mobile menu button */}
            <button
              type="button"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-300 hover:bg-gray-800 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              aria-controls="mobile-menu"
              aria-expanded={mobileMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              <svg
                className="block h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div
            className="sm:hidden absolute top-16 left-0 right-0 bg-black shadow-lg z-50"
            id="mobile-menu"
          >
            {user ? (
              <div className="border-t border-gray-700 pb-3 pt-4">
                <div className="flex items-center px-4 mb-3">
                  <div className="h-8 w-8 flex items-center justify-center rounded-full bg-indigo-500 text-white font-bold">
                    {initials}
                  </div>
                  <span className="ml-3 text-white">
                    {user?.firstName?.split(" ")[0]}
                  </span>
                </div>
                <div className="space-y-1">
                  <Link
                    href="/profile"
                    className="flex items-center gap-2 px-4 py-2 text-gray-300 hover:bg-gray-800 hover:text-white"
                  >
                    <UserIcon className="h-5 w-5" />
                    Profile
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex w-full items-center gap-2 px-4 py-2 text-left text-gray-300 hover:bg-gray-800 hover:text-white cursor-pointer"
                  >
                    <ArrowRightOnRectangleIcon className="h-5 w-5" />
                    Sign out
                  </button>
                  <Link
                    href={
                      user?.role === "celebrity"
                        ? "/celebrity/dashboard"
                        : "/customer/dashboard"
                    }
                    className="flex items-center gap-2 px-4 py-2 hover:bg-gray-800"
                  >
                    <HomeIcon className="h-5 w-5 text-white" />
                    Dashboard
                  </Link>
                </div>
              </div>
            ) : (
              <div className="border-t border-gray-700 pb-3 pt-4">
                <div className="flex items-center px-4">
                  <Link
                    href="/auth/start"
                    className="w-full rounded-md bg-white px-3.5 py-2 text-center text-sm font-semibold text-black shadow-sm hover:bg-gray-200"
                  >
                    Sign in
                  </Link>
                </div>
              </div>
            )}
            <div className="space-y-1 pb-3 pt-2">
              <Link
                href="/celebrity"
                className="block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-300 hover:border-white hover:bg-gray-800 hover:text-white"
              >
                Celebrities
              </Link>
              <Link
                href="/how-it-works"
                className="block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-300 hover:border-white hover:bg-gray-800 hover:text-white"
              >
                How It Works
              </Link>
              <Link
                href="/pricing"
                className="block border-l-4 border-transparent py-2 pl-3 pr-4 text-base font-medium text-gray-300 hover:border-white hover:bg-gray-800 hover:text-white"
              >
                Pricing
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
