"use client"
import React from 'react';
import { ChevronLeft, Camera, Video } from 'lucide-react';
import { useRouter } from "next/navigation";


const Button = ({ children, onClick, className = '' }) => {
  return (
    <button
      onClick={onClick}
      className={`
        w-full py-3 px-6 rounded-lg
        border border-black
        text-white font-medium
        shadow-[0px_1px_2px_0px_rgba(0,0,0,0.48)]
        bg-black
        bg-gradient-to-b from-white/20 to-transparent
        transition-all duration-200
        hover:from-white/30 active:from-white/10
        ${className}
      `}
    >
      {children}
    </button>
  );
};



const ProfileSetup = () => {
  const router = useRouter();

  const handleNavigateToUploadPhoto = () => {
    router.push('/upload-photo');
  };

  return (
    <div className="min-h-screen  text-white flex flex-col" style={{ fontFamily: "AG Book Pro, Arial, sans-serif" }}>
      <div className="p-5 relative">
        <div className="flex items-center justify-center mb-4 relative">
          <button className="p-1 rounded-full absolute left-0">
            <ChevronLeft size={20} />
          </button>
          <h2 className="text-lg font-medium">Setup your video</h2>
        </div>
      </div>

      <div className="bg-white text-black rounded-[32px] p-6 mx-4 mb-4 flex-1 overflow-y-auto">
        <div className="max-w-md mx-auto px-2">
          <h1 className="text-2xl font-semibold mb-2 text-center">Build Your Signature Video</h1>
          <p className="text-sm text-gray-600 mb-6 text-center">
            Just two quick steps so fans can receive personalized video messages that sound and look like you
          </p>

          <div
            className="border border-gray-100 rounded-lg p-4 mb-4 cursor-pointer hover:bg-gray-50 transition-colors"
            // onClick={handleNavigateToUploadPhoto}
          >
            <div className="flex gap-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-black"> 
                    <li>Face forward</li>
                    <li>No filters or sunglasses</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="border border-gray-100 rounded-lg p-4 mb-4">
            <div className="flex gap-4">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-black">
           <img src="/re-record.svg" alt="Camera" className="w-5 h-5" />
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-semibold mb-1">
                  Step 2: Record a Short Script
                </h3>
                <div className="text-[13px] text-gray-700 space-y-2">
                  <p>Read a quick 60-second script so your voice, tone, and personality come through</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Speak naturally</li>
                    <li>Show emotion (excited, calm, confident)</li>
                    <li>You can preview and re-record</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 mb-4">
            <Button
              onClick={handleNavigateToUploadPhoto}
              className="rounded-lg shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.08),0px_8px_8px_-4px_rgba(16,24,40,0.03)] bg-gradient-to-b from-[#000] to-[#242424]"
            >
              Let's get started
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileSetup;