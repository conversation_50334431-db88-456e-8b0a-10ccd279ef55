"use client";

import {
  UserIcon,
  ArrowRightOnRectangleIcon,
  HomeIcon,
} from "@heroicons/react/24/outline";
import { selectAuth } from "@/redux/slices/auth/selectors";
import { logoutUser } from "@/redux/slices/auth/slice";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const ProfileDropdown = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const auth = useSelector(selectAuth);
  const user = auth?.userInfo?.user;
  const initials = user?.firstName?.charAt(0)?.toUpperCase();

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const handleLogout = () => {
    dispatch(logoutUser());
    router.push("/auth/start");
  };
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setDropdownOpen(!dropdownOpen)}
        className="flex items-center space-x-2 rounded-full bgGray px-3 py-2 text-sm text-white focus:outline-none focus:ring cursor-pointer"
      >
        <div className="h-8 w-8 flex items-center justify-center rounded-full bg-indigo-500 text-white font-bold">
          {initials}
        </div>
        <span>{user?.firstName?.split(" ")[0]}</span>
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {dropdownOpen && (
        <div className="absolute right-0 mt-2 w-32 rounded-md bg-black shadow-lg ring-1 ring-white ring-opacity-10 z-50">
          <div className="py-1 text-sm text-white">
            <Link
              href="/profile"
              className="flex items-center gap-2 px-4 py-2 hover:bg-gray-800 text-white"
            >
              <UserIcon className="h-5 w-5 text-white" />
              Profile
            </Link>
            <button
              onClick={handleLogout}
              className="flex w-full items-center gap-2 px-4 py-2 text-left hover:bg-gray-800 cursor-pointer"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 text-white" />
              Sign out
            </button>

            <Link
              href={
                user?.role === "celebrity"
                  ? "/celebrity/dashboard" 
                  : user?.role === "admin"
                  ? "/admin/users"
                  : "/customer/dashboard"
              }
              className="flex items-center gap-2 px-4 py-2 hover:bg-gray-800 text-white"
            >
              <HomeIcon className="h-5 w-5 text-white" />
              Dashboard
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileDropdown;
