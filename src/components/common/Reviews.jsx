"use client";

import Image from "next/image";

const Reviews = () => {
  return (
    <div className="mt-4 overflow-y-auto">
      {[1, 2].map((_, idx) => (
        <div
          key={idx}
          className="flex flex-col gap-3 text-black border border-black p-4 rounded-lg mb-2"
        >
          <div className="flex justify-between items-center">
            <p className="font-semibold">Anonymous</p>
            <div className="flex gap-1">
              <Image src="/rating.svg" width={16} height={16} alt="rating" />
              <span className="underline text-xs">4.6 (20)</span>
            </div>
          </div>
          <p className="text-gray-400">
            Consequat velit qui adipisicing sunt do rependerit ad laborum tempor
            ullamco exercitation. Ullamco tempor adipisicing et voluptate duis
            sit esse aliqua
          </p>
          <p className="underline text-xs">Read more</p>
        </div>
      ))}
    </div>
  );
};

export default Reviews;
