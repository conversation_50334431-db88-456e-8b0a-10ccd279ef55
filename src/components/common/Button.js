import React from 'react';

const Button = ({ 
  children, 
  onClick, 
  disabled = false,
  className = ''
}) => {
  return (
    <button
      className={`w-full mx-auto max-w-[350px] h-[60px] bg-[#111] py-3 px-4 rounded-lg border border-white/20
        bg-gradient-to-b from-white/30 via-white/10 to-transparent
        shadow-[0px_4px_8px_0px_rgba(0,0,0,0.5),_0px_1px_0px_0px_rgba(255,255,255,0.1)_inset]
        text-white text-lg font-medium tracking-wide transition-all duration-200
        flex items-center justify-center
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90 hover:shadow-lg hover:from-white/40 active:scale-[0.98]'}
        ${className}`} 
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default Button;