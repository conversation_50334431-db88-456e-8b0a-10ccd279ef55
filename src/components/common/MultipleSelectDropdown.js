import { useState, useEffect, useRef } from 'react';

const MultiSelectDropdown = ({
  options,
  selectedValues = [],
  onChange,
  label,
  error,
  touched,
  disabled,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleOption = (option) => {
    const newSelected = selectedValues.includes(option)
      ? selectedValues.filter((v) => v !== option)
      : [...selectedValues, option];
    onChange(newSelected);
  };

  return (
    <div className='relative w-full' ref={dropdownRef}>
      <label className='block text-sm font-medium text-black mb-1'>
        {label}
      </label>

      {/* Dropdown trigger */}
      <div
        className={`relative cursor-pointer rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm 
          ${
            disabled
              ? 'bg-gray-100 cursor-not-allowed'
              : 'hover:border-gray-400'
          }
          ${isOpen ? 'ring-2 ring-indigo-500 border-indigo-500' : ''}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className='flex items-center justify-between'>
          <span className='truncate text-black'>
            {selectedValues.length > 0
              ? selectedValues.join(', ')
              : 'Select categories'}
          </span>
          <svg
            className={`h-5 w-5 text-gray-400 transform transition-transform ${
              isOpen ? 'rotate-180' : ''
            }`}
            viewBox='0 0 20 20'
            fill='currentColor'
          >
            <path
              fillRule='evenodd'
              d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z'
              clipRule='evenodd'
            />
          </svg>
        </div>

        {/* Dropdown content */}
        {isOpen && !disabled && (
          <div className='absolute z-10 mt-1 w-full left-0 bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-auto'>
            <div className='p-2 space-y-1'>
              {options.map((option) => (
                <label
                  key={option}
                  onClick={() => toggleOption(option)}
                  className='flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer'
                >
                  <input
                    type='checkbox'
                    checked={selectedValues.includes(option)}
                    onChange={() => toggleOption(option)}
                    className='h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
                    onClick={(e) => e.stopPropagation()}
                  />
                  <span className='ml-2 text-sm text-gray-700'>{option}</span>
                </label>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Selected categories chips */}
      {selectedValues.length > 0 && (
        <div className='mt-2 flex flex-wrap gap-2'>
          {selectedValues.map((value) => (
            <span
              key={value}
              className='inline-flex items-center px-2 py-1 rounded-md bg-indigo-100 text-indigo-700 text-sm'
            >
              {value}
              <button
                type='button'
                onClick={() => {
                  const updated = selectedValues.filter((v) => v !== value);
                  onChange(updated);
                }}
                className='ml-2 text-indigo-400 hover:text-indigo-600 focus:outline-none cursor-pointer'
                disabled={disabled}
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}

      {touched && error && <p className='mt-1 text-sm text-red-600'>{error}</p>}
    </div>
  );
};

export default MultiSelectDropdown;
