"use client";

import React, { useState, useEffect } from "react";
import { X, Share, Download, Instagram, Link, Copy } from "lucide-react";
import Image from "next/image";
import { useDispatch } from "react-redux";
import { setShareModalOpen } from "@/redux/slices/ui/slice";

const ShareModal = ({
  isOpen,
  onClose,
  greetingData = {
    title: "Your greeting is ready!",
    subtitle: "Preview it on your profile or share it with friends",
    celebrityName: "French Montana",
    celebrityRole: "Musician",
    celebrityImage: "/doubleicon.png"
  }
}) => {
  const dispatch = useDispatch();
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [showShareCard, setShowShareCard] = useState(false);

  // Update Redux state when modal opens/closes
  useEffect(() => {
    dispatch(setShareModalOpen(isOpen));
  }, [isOpen, dispatch]);

  if (!isOpen) return null;

  const handleShareClick = () => {
    setShowShareOptions(true);
  };

  const handleShareVia = () => {
    setShowShareCard(true);
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
  };

  const handleDownload = () => {
    // Implement download functionality
    console.log("Download greeting");
  };

  const handleShareToStories = () => {
    // Implement Instagram stories sharing
    console.log("Share to Instagram stories");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50">
      {/* Main Modal - Full Screen */}
      {!showShareOptions && !showShareCard && (
        <div className="w-full h-full max-w-sm mx-auto">
          <div className="bg-black w-full h-full flex flex-col justify-center text-white relative px-6" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
            <button
              onClick={onClose}
              className="absolute top-8 right-6 text-white hover:text-gray-300"
            >
              <X size={24} />
            </button>

            <div className="text-center">
              <h2 className="text-xl font-bold mb-2">{greetingData.title}</h2>

              <div className="flex items-center justify-center gap-1 mb-16">
                <div className=" rounded-xl flex items-center justify-center">
                  <Image
                    src={"/record.png"}
                    width={82}
                    height={82}
                    alt="audio wave"
                    className="filter invert"
                  />
                </div>
                <div className="w-16 h-16 rounded-full overflow-hidden -ml-7">
                  <Image
                    src={greetingData.celebrityImage}
                    width={64}
                    height={64}
                    alt="celebrity"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <p className="text-sm text-gray-300 mb-12">{greetingData.subtitle}</p>

              <div className="space-y-3">
                <button
                  onClick={handleShareClick}
                  className="w-full text-white py-3 px-6 flex items-center justify-center gap-2 transition-colors"
                  style={{
                    borderRadius: '8px',
                    border: '1px solid #000',
                    background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000',
                    boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.48)'
                  }}
                >
                  <Share size={18} />
                  <span className="font-medium">Share celebrityX greeting</span>
                </button>

                <button
                  onClick={handleDownload}
                  className="w-full bg-gray-200 text-black py-3 px-6 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
                >
                  <Download size={18} />
                  <span className="font-medium">Download</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showShareOptions && !showShareCard && (
        <div className="absolute bottom-0 left-0 right-0 flex flex-col items-center">

          {/* Greeting Content - Above the card */}
          <div className="text-center text-white mb-6" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
            <h2 className="text-xl font-bold mb-4">{greetingData.title}</h2>
            <div className="flex items-center justify-center gap-1">
              <div className="  rounded-xl flex items-center justify-center">
                <Image
                  src={"/record.png"}
                  width={82}
                  height={82}
                  alt="audio wave"
                  className="filter invert"
                />
              </div>
              <div className="w-16 h-16 rounded-full overflow-hidden -ml-7">
                <Image
                  src={greetingData.celebrityImage}
                  width={64}
                  height={64}
                  alt="celebrity"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>

          {/* White Card */}
          <div className="bg-white w-full max-w-sm rounded-t-3xl text-black relative" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Share greeting</h3>
              <button
                onClick={() => setShowShareOptions(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="px-6 pb-8">
              <div className="space-y-3">
                <button
                  // onClick={handleShareToStories}
                  onClick={handleShareVia}
                  className="w-full bg-black text-white py-3 px-6 rounded-lg flex items-center justify-center gap-2 transition-colors"
                >
                  <Instagram size={18} />
                  <span className="font-medium">Share to stories</span>
                </button>

                <button
                  onClick={handleShareVia}
                  className="w-full bg-gray-200 text-black py-3 px-6 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
                >
                  <Share size={18} />
                  <span className="font-medium">Share via</span>
                </button>

                <button
                  onClick={handleCopyLink}
                  className="w-full bg-gray-200 text-black py-3 px-6 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-300 transition-colors"
                >
                  <Copy size={18} />
                  <span className="font-medium">Copy link</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Share Card Modal - Full Screen */}
      {showShareCard && (
        <div className="w-full h-full max-w-sm mx-auto">
          <div className="bg-white w-full h-full flex flex-col overflow-hidden relative" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
            <div className="flex items-center justify-between p-4 border-b">
              <button
                onClick={() => setShowShareCard(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
              <h3 className="text-lg font-semibold text-black">Share greeting</h3>
              <button className="text-gray-500 hover:text-gray-700">
                <Download size={20} />
              </button>
            </div>

            <div className="relative flex-1">
              <div
                className="h-full bg-gradient-to-br from-red-400 to-red-600 relative overflow-hidden"
                style={{
                  backgroundImage: "url('/image.png')",
                  backgroundSize: "cover",
                  backgroundPosition: "center"
                }}
              >
                <div className="absolute top-4 left-4 flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src={greetingData.celebrityImage}
                      width={48}
                      height={48}
                      alt="celebrity"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="text-white">
                    <h4 className="font-bold text-lg">{greetingData.celebrityName}</h4>
                    <p className="text-sm opacity-90">{greetingData.celebrityRole}</p>
                  </div>
                </div>

                <div className="absolute bottom-4 left-4 text-white">
                  <p className="text-sm mb-2">Just dropped a new greeting at</p>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold">celebrity</span>
                    <span className="text-2xl font-bold">X</span>
                    <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                      <div className="w-3 h-3 bg-black rounded-full"></div>
                    </div>
                    <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                      <div className="w-3 h-3 bg-gray-600 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-4">
              <button
                onClick={handleShareToStories}
                className="w-full bg-black text-white py-3 px-6 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-900 transition-colors"
              >
                <Instagram size={18} />
                <span className="font-medium">Share to stories</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShareModal;
