// app/components/AppLayout.tsx or utils/AppLayout.tsx
"use client";

import { usePathname } from "next/navigation";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { selectAuth } from "@/redux/slices/auth/selectors";
import { selectIsShareModalOpen, selectIsGreetingPreviewOpen } from "@/redux/slices/ui/selectors";
export default function AppLayout({ children }) {
  const pathname = usePathname();
  const isAdmin = pathname.startsWith("/admin");
  const isAuthPage = pathname.startsWith("/auth");
  const isLandingPage = pathname === "/";
  const isVerificationPage = pathname.startsWith("/identity-verification");
  const isRecordVideoPage = pathname.startsWith("/record-video");
  const isVideoPreview = pathname.startsWith("/video-preview");
  const isSetupProfile = pathname.startsWith("/setup-profile");
  const isUploadPhoto = pathname.startsWith("/upload-photo");
  const isCustomerVideoPreview = pathname.startsWith("/customer/video-preview");
  const isWallet=pathname.startsWith("/wallet-connect")


  const showLayoutElements =
    !isAdmin && !isAuthPage && !isLandingPage && !isVerificationPage && !isRecordVideoPage &&!isVideoPreview && !isSetupProfile && !isUploadPhoto &&  !isCustomerVideoPreview && !isWallet;

  const auth = useSelector(selectAuth);
  const isShareModalOpen = useSelector(selectIsShareModalOpen);
  const isGreetingPreviewOpen = useSelector(selectIsGreetingPreviewOpen);

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1">{children}</div>
      {showLayoutElements && auth?.isLogin && !isShareModalOpen && !isGreetingPreviewOpen && <Navbar />}
    </div>
  );
}
