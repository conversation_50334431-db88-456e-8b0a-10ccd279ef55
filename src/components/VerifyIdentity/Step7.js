"use client";

import React, { useState } from "react";
import { useFormikContext } from "formik";
import Image from "next/image";
import www from "../../../public/www.svg"
const Step7 = () => {
  const { values, setFieldValue } = useFormikContext();
  const [links, setLinks] = useState([
    { id: 1, url: "" }
  ]);

  const handleLinkChange = (id, value) => {
    const updatedLinks = links.map(link =>
      link.id === id ? { ...link, url: value } : link
    );
    setLinks(updatedLinks);

    // Update Formik values
    setFieldValue('socialLinks', updatedLinks.map(link => link.url).filter(Boolean));
  };

  const addMoreLink = () => {
    setLinks([...links, { id: Date.now(), url: "" }]);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {links.map((link, index) => (
          <div key={link.id} className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Image
                  src={www}
                  width={20}
                  height={20}
                  alt="URL"
                />
              </div>
              <input
                type="text"
                value={link.url}
                onChange={(e) => handleLinkChange(link.id, e.target.value)}
                placeholder="https://www.instagram.com/beyonce"
                className="w-full pl-10 pr-3 py-4 border border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-300"
              />
            </div>
          </div>
        ))}
      </div>

      <div className="flex flex-col space-y-4">
        <button
          type="button"
          onClick={addMoreLink}
          className="flex items-center text-black font-medium text-sm mt-2"
        >
          <span className="mr-1">+</span> Add more
        </button>

        <div className="flex justify-center w-full mt-8">
          <button
            type="button"
            onClick={() => {
              // Navigate to next step
              const nextStepEvent = new CustomEvent('nextStep');
              document.dispatchEvent(nextStepEvent);
            }}
            className="w-full max-w-[287px] py-4 rounded-lg text-white font-medium"
            style={{
              borderRadius: "8px",
              border: "1px solid #000",
              background: "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
              boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)"
            }}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step7;
