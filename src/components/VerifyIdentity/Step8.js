"use client";

import React from "react";
import Image from "next/image";
import elementro from "../../../public/Elemento.png";

const Step8 = ({ setCurrentStep }) => {
  return (
    <div
      className="fixed inset-0 flex flex-col items-center justify-center text-center bg-black"
      style={{ fontFamily: '"AG Book Pro", sans-serif' }}
    >
      <div className="flex flex-col items-center justify-center space-y-6 px-4">
        <h1 className="text-3xl font-medium text-white text-center leading-normal mb-14">
          Verification Complete
        </h1>
        <div className=" flex items-center justify-center">
          <Image
            src={elementro}
            alt="Verification Icon"
            width={120}
            height={120}
            className="rounded-xl"
          />
        </div>

        <p className="text-white text-sm max-w-[280px] mt-4 text-center font-normal leading-[160%] tracking-[-0.28px]">
          We've received your details — our team is reviewing them now. You'll
          get an email once you're approved.
        </p>
      </div>

      <div className="absolute bottom-16 w-full flex justify-center px-4">
        <button
          type="button"
          onClick={() => {
            window.location.href = "/celebrity/home";
            setCurrentStep(0);
          }}
          className="w-full max-w-[280px] h-[48px] rounded-lg text-black font-medium"
          style={{
            borderRadius: "8px",
            border: "1px solid #000",
            background:
              "linear-gradient(180deg, rgba(214, 214, 214, 0.00) 0%, #A7A7A7 100%), #F2F2F2",
            boxShadow:
              "0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset, -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset, 0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default Step8;
