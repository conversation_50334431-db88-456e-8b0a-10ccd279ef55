"use client";

import React from "react";
import { useFormikContext } from "formik";

const Step4 = ({ values }) => {
  const { setFieldValue } = useFormikContext();
  // This component shows the captured ID document with confirmation options
  return (
    <div className="space-y-4">
      <div className="flex flex-col items-center">
        <div className="w-full bg-gray-100 rounded-lg p-2 mb-4">
          <img
            src={values?.idImage || "/images/sample-id.jpg"}
            alt="ID document"
            className="w-full h-auto rounded-lg object-contain"
          />
        </div>

        <div className="flex flex-col w-full gap-3 items-center">
          <button
            type="button"
            onClick={() => {
              // Make sure the ID image is set in the form values
              if (values?.idImage) {
                // Navigate to next step when confirmed
                const nextStepEvent = new CustomEvent('nextStep');
                document.dispatchEvent(nextStepEvent);
              } else {
                alert("No ID image captured. Please try again.");
              }
            }}
            className="w-full max-w-[287px] py-3 text-white rounded-lg font-medium"
            style={{
              borderRadius: "8px",
              border: "1px solid #000",
              background: "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
              boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)"
            }}
          >
            Confirm and Continue
          </button>
          <button
            type="button"
            onClick={() => {
              // Go back to camera step
              setFieldValue('idImage', null);
              // Dispatch prevStep event to go back to the camera step
              const prevStepEvent = new CustomEvent('prevStep');
              document.dispatchEvent(prevStepEvent);
            }}
            className="w-full max-w-[287px] py-3 text-gray-700 rounded-lg font-medium"
            style={{
              borderRadius: "8px",
              // border: "1px solid #000",
              background: "linear-gradient(180deg, rgba(214, 214, 214, 0.00) 0%, #A7A7A7 100%), #F2F2F2",
              boxShadow: "0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset, -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset, 0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
            }}
          >
            Take Another Photo
          </button>
        </div>
      </div>
    </div>
  );
};

export default Step4;
