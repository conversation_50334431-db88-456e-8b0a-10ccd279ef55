import React from "react";
import { Camera, PenLine, User } from "lucide-react";


const Step1 = () => {
  const Points = [
    {
      icon: <User size={20} />,
      title: "Government-issued ID",
      description: "Passport, driver's license, national ID",
    },
    {
      icon: <Camera size={20} />,
      title: "Short verification video",
      description:
        "Record a quick video to match your ID using facial recognition",
    },
    {
      icon: <PenLine size={20} />,
      title: "Signed terms of use and rights waiver",
      description: "Authorize the use of your likeness on celebrityX",
    },
  ];
  return (
    <div className="w-full">
      <div className="space-y-4 w-full">
        {Points.map((item, index) => (
          <div key={index} className="flex items-start p-4 bg-white border border-gray-200 rounded-xl shadow-sm">
            <div className="w-10 h-10 rounded-full flex items-center justify-center bg-white shadow-md mr-3">
              <span className="text-gray-800">
                {item.icon}
              </span>
            </div>

            <div>
              <h3 className="text-base font-medium text-gray-900 mb-1">{item.title}</h3>
              <p className="text-xs text-gray-500">{item.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Step1;
