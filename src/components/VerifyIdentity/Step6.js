"use client";

import React, { useState } from "react";
import { useFormikContext } from "formik";
import { Loader, LoaderVariants } from "@/components/common/Loader";

const Step6 = ({ loading }) => {
  const { values, setFieldValue, submitForm } = useFormikContext();
  const [checkboxes, setCheckboxes] = useState({
    authorize: false,
    confirm: false,
    agree: false,
  });

  const handleCheckboxChange = (name) => {
    setCheckboxes((prev) => ({
      ...prev,
      [name]: !prev[name],
    }));

    setFieldValue(`terms.${name}`, !checkboxes[name]);
  };

  const allChecked =
    checkboxes.authorize && checkboxes.confirm && checkboxes.agree;

  return (
    <div
      className="space-y-6 px-2 sm:px-4"
      style={{ fontFamily: '"AG Book Pro", sans-serif' }}
    >
      <div className="space-y-4">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="authorize"
            checked={checkboxes.authorize}
            onChange={() => handleCheckboxChange("authorize")}
            className="mt-1 h-4 w-4 accent-black"
            style={{ accentColor: "black" }}
          />
          <div>
            <label htmlFor="authorize" className="text-sm">
              I authorize celebrityX to use my name, image, and voice to create
              and deliver personalized AI-generated greetings.
            </label>
          </div>
        </div>

        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="confirm"
            checked={checkboxes.confirm}
            onChange={() => handleCheckboxChange("confirm")}
            className="mt-1 h-4 w-4 accent-black"
            style={{ accentColor: "black" }}
          />
          <div>
            <label htmlFor="confirm" className="text-sm">
              I confirm I am the rightful owner of the likeness and media I've
              provided.
            </label>
          </div>
        </div>

        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="agree"
            checked={checkboxes.agree}
            onChange={() => handleCheckboxChange("agree")}
            className="mt-1 h-4 w-4 accent-black"
            style={{ accentColor: "black" }}
          />
          <div>
            <label htmlFor="agree" className="text-sm">
              I agree to the celebrityX's{" "}
              <a href="#" className="underline text-blue-800">
                Terms of Use
              </a>{" "}
              and{" "}
              <a href="#" className="underline text-blue-800">
                Content Policy
              </a>
              .
            </label>
          </div>
        </div>
      </div>

      <div className="w-full flex justify-center mt-6">
        <button
          type="submit"
          onClick={async () => {
            if (allChecked) {
              await submitForm();
            }
          }}
          className={`w-full max-w-[287px] py-4 rounded-lg font-medium flex items-center justify-center ${
            allChecked
              ? "text-white"
              : "bg-gray-200 text-gray-400 cursor-not-allowed"
          }`}
          disabled={!allChecked}
          style={
            allChecked
              ? {
                  borderRadius: "8px",
                  border: "1px solid #000",
                  background:
                    "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
                  boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)",
                }
              : {
                  background: "#E0E0E0",
                  boxShadow: "none",
                }
          }
        >
          {loading ? (
            <Loader
              variant={LoaderVariants.rotatingLines}
              size={25}
              color="white"
            />
          ) : (
            "Accept and Proceed"
          )}
        </button>
      </div>
    </div>
  );
};

export default Step6;
