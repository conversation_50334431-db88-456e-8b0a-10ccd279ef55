"use client";

import { useRef, useState, useEffect } from "react";
import { Camera } from "lucide-react";
import { useFormikContext } from "formik";
import { toast } from "react-toastify";

const Step5 = ({ values }) => {
  const { setFieldValue } = useFormikContext();
  const [image, setImage] = useState(null);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedChunksRef = useRef([]);
  const timerRef = useRef(null);

  const initialToastShownRef = useRef(false);
  const successToastShownRef = useRef(false);
  const lastErrorMessageRef = useRef("");

  const showErrorToast = (message) => {
    if (lastErrorMessageRef.current !== message) {
      toast.error(message);
      lastErrorMessageRef.current = message;
    }
  };

  useEffect(() => {
    setFieldValue("selfieVideo", null);

    successToastShownRef.current = false;

    initialToastShownRef.current = true;

    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: "user" },
          audio: true,
        });

        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }

        setIsCameraActive(true);
      } catch (err) {
        if (err.name === "NotAllowedError") {
          toast.error(
            "Camera access denied. Please allow camera access in your browser settings."
          );
        } else if (err.name === "NotFoundError") {
          toast.error(
            "No camera found. Please ensure your device has a working camera."
          );
        } else {
          toast.error("Could not access the camera. Please check permissions.");
        }
      }
    };

    startCamera();

    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject.getTracks().forEach((track) => track.stop());
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const startRecording = () => {
    if (!videoRef.current || !videoRef.current.srcObject) {
      showErrorToast("Camera not ready. Please refresh and try again.");
      return;
    }

    try {
      lastErrorMessageRef.current = "";

      setFieldValue("selfieVideo", null);

      recordedChunksRef.current = [];
      const stream = videoRef.current.srcObject;
      if (!stream.active) {
        showErrorToast(
          "Camera stream is not active. Please refresh and try again."
        );
        return;
      }

      const mediaRecorder = new MediaRecorder(stream);

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        try {
          if (recordedChunksRef.current.length === 0) {
            showErrorToast("No video data recorded. Please try again.");
            return;
          }

          const blob = new Blob(recordedChunksRef.current, {
            type: "video/webm",
          });

          const url = URL.createObjectURL(blob);
          setImage(url);
          const reader = new FileReader();
          reader.readAsDataURL(blob);

          reader.onloadend = () => {
            try {
              const base64data = reader.result;
              setFieldValue("selfieVideo", base64data);

              if (!successToastShownRef.current) {
                toast.success("Video recorded successfully!");
                successToastShownRef.current = true;
              }
            } catch (err) {
              showErrorToast("Failed to process video. Please try again.");
            }
          };

          reader.onerror = (event) => {
            showErrorToast("Failed to process video. Please try again.");
          };
        } catch (err) {
          showErrorToast("Failed to process video. Please try again.");
        }
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
    } catch (err) {
      console.error("Error starting recording:", err);
      showErrorToast("Failed to start recording. Please try again.");
    }

    timerRef.current = setInterval(() => {
      setRecordingTime((prev) => {
        if (prev >= 5) {
          stopRecording();
          return 0;
        }
        return prev + 1;
      });
    }, 1000);
  };

  const stopRecording = () => {
    try {
      if (mediaRecorderRef.current && isRecording) {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        setIsCameraActive(false);
        if (timerRef.current) {
          clearInterval(timerRef.current);
          setRecordingTime(0);
        }
      } else {
        if (!mediaRecorderRef.current || !isRecording) {
          showErrorToast("Recording failed. Please try again.");
        }
      }
    } catch (err) {
      showErrorToast("Failed to stop recording. Please try again.");
    }
  };

  return (
    <div className="relative h-screen w-full flex flex-col bg-black">
      <div className="flex-1 relative overflow-hidden">
        {isCameraActive ? (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
          />
        ) : image ? (
          <video
            src={image}
            controls
            className="w-full h-full object-contain"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-black">
            <Camera size={48} className="text-gray-400" />
          </div>
        )}

        {isCameraActive && (
          <div className="absolute bottom-20 left-0 right-0 flex justify-center">
            <button
              onClick={isRecording ? stopRecording : startRecording}
              className={`w-20 h-20 rounded-full border-2 border-white flex items-center justify-center p-[10px] ${
                isRecording ? "bg-red-500" : ""
              }`}
            >
              {isRecording ? (
                <div className="w-12 h-12 rounded bg-red-600"></div>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="60"
                  height="61"
                  viewBox="0 0 60 61"
                  fill="none"
                >
                  <circle cx="30" cy="30.5273" r="30" fill="white" />
                </svg>
              )}
            </button>
          </div>
        )}

        {isRecording && (
          <div className="absolute top-6 left-0 right-0 flex justify-center">
            <div className="bg-black bg-opacity-50 px-4 py-2 rounded-full text-white">
              {recordingTime}s
            </div>
          </div>
        )}

        {image && (
          <div className="absolute bottom-20 left-0 right-0 flex justify-center space-x-4">
            <button
              type="button"
              onClick={() => {
                // Dispatch prevStep event to go back to previous step
                const prevStepEvent = new CustomEvent("prevStep");
                document.dispatchEvent(prevStepEvent);
              }}
              className="px-6 py-3 text-gray-700 rounded-lg font-medium"
              style={{
                borderRadius: "8px",
                // border: "1px solid #000",
                background:
                  "linear-gradient(180deg, rgba(214, 214, 214, 0.00) 0%, #A7A7A7 100%), #F2F2F2",
                boxShadow:
                  "0.996px 0.996px 1.668px 0px rgba(255, 255, 255, 0.25) inset, -0.498px -0.996px 0.996px 0px rgba(0, 0, 0, 0.45) inset, 0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
              }}
            >
              Try again
            </button>
            <button
              type="button"
              onClick={() => {
                if (values.selfieVideo) {
                  const nextStepEvent = new CustomEvent("nextStep");
                  document.dispatchEvent(nextStepEvent);
                } else {
                  toast.info(
                    "Please wait while your video is being processed..."
                  );

                  setTimeout(() => {
                    if (values.selfieVideo) {
                      const nextStepEvent = new CustomEvent("nextStep");
                      document.dispatchEvent(nextStepEvent);
                    } else {
                      showErrorToast(
                        "Failed to process video. Please try recording again."
                      );
                    }
                  }, 1000);
                }
              }}
              className="px-6 py-3 text-white rounded-lg font-medium"
              style={{
                borderRadius: "8px",
                border: "1px solid #000",
                background:
                  "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
                boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)",
              }}
            >
              Confirm
            </button>
          </div>
        )}

        <canvas ref={canvasRef} className="hidden" />
      </div>

      <div className="relative top-[-15px] flex flex-col gap-2 rounded-t-xl items-center justify-center bg-black h-24 z-100 text-white">
        <h4 className="text-lg font-medium">Record yourself</h4>
        <p className="text-sm text-gray-300 text-center px-8">
          Record a quick video to match your ID using facial recognition
        </p>
      </div>
    </div>
  );
};

export default Step5;
