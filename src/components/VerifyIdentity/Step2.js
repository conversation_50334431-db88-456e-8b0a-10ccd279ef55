import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useFormikContext } from "formik";
import directioncar from "../../../public/directionscar.svg";
import global from "../../../public/global.svg";
import idcard from "../../../public/id-card.svg";

const Step2 = ({ values }) => {
  const { setFieldValue } = useFormikContext();
  const [selectedDocument, setSelectedDocument] = useState(
    values?.idType || ""
  );
  const [selectedCountry, setSelectedCountry] = useState(
    values?.country || "United Kingdom"
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    // Initialize Formik values if not already set
    if (!values.country) {
      setFieldValue("country", selectedCountry);
    }
  }, [values.country, selectedCountry, setFieldValue]);

  // List of countries
  const countries = ["United Kingdom", "United States", "Canada", "Australia"];

  // List of document types with their API values matching KycDocumentType enum
  const documentTypes = [
    { label: "National ID", value: "national_id", icon: idcard },
    { label: "Passport", value: "passport", icon: global },
    { label: "Driver's License", value: "drivers_license", icon: directioncar },
  ];

  return (
    <div className="space-y-4">
      {/* Country Label */}
      <p className="text-center text-sm text-gray-600 mb-4">
        Select the country where your ID was issued
      </p>
      <label className="block mb-1 text-gray-600 font-medium text-sm">
        Country
      </label>

      {/* Country Selection as Dropdown */}
      <div
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="w-full p-4 bg-white border border-gray-200 rounded-xl flex justify-between items-center cursor-pointer shadow-sm"
      >
        <span className="text-gray-900">{selectedCountry}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="17"
          height="17"
          viewBox="0 0 17 17"
          fill="none"
        >
          <path
            d="M6.16016 12.5L10.1602 8.5L6.16016 4.5"
            stroke="#ACB5BB"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <div className="mt-2 border border-gray-200 rounded-xl bg-white shadow-md">
          {countries.map((country, index) => (
            <div
              key={index}
              onClick={() => {
                setSelectedCountry(country);
                setFieldValue("country", country);
                setIsDropdownOpen(false);
              }}
              className={`p-4 text-gray-700 hover:bg-gray-100 cursor-pointer ${
                selectedCountry === country ? "bg-gray-100" : ""
              } ${index === 0 ? "rounded-t-xl" : ""} ${
                index === countries.length - 1 ? "rounded-b-xl" : ""
              }`}
            >
              {country}
            </div>
          ))}
        </div>
      )}

      {/* Document Type Label */}
      <label className="block mb-1 text-gray-600 font-medium text-sm mt-4">
        Select the type of document
      </label>

      {/* Document Type Selection */}
      <div className="space-y-4 mt-4">
        {documentTypes.map((doc, index) => (
          <div
            key={index}
            onClick={() => {
              setSelectedDocument(doc.label);
              // Store both the display label and the API value
              setFieldValue("idType", {
                label: doc.label,
                value: doc.value,
              });
            }}
            className={`flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer shadow-sm ${
              selectedDocument === doc.label ? "bg-gray-100" : "bg-white"
            }`}
          >
            <Image
              src={doc.icon}
              width={24}
              height={24}
              alt={doc.label}
              className="mr-3"
            />
            <span className="text-gray-900">{doc.label}</span>
          </div>
        ))}
      </div>

      {/* Continue Button */}
      <div className="mt-8 flex justify-center w-full">
        <button
          type="button"
          onClick={() => {
            if (selectedDocument) {
              // Navigate to next step
              const nextStepEvent = new CustomEvent("nextStep");
              document.dispatchEvent(nextStepEvent);
            }
          }}
          className="w-full max-w-[287px] py-4 rounded-lg text-white font-medium"
          style={{
            borderRadius: "8px",
            border: "1px solid #000",
            background:
              "linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000",
            boxShadow: "0px 1px 2px 0px rgba(0, 0, 0, 0.48)",
          }}
          disabled={!selectedDocument}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default Step2;
