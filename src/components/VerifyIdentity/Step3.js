"use client";

import { useRef, useState, useEffect } from "react";
import { Camera } from "lucide-react";
import { useFormikContext } from "formik";

const Step3 = () => {
  const { setFieldValue } = useFormikContext();
  const [image, setImage] = useState(null);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showFlash, setShowFlash] = useState(false);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  // Start camera automatically on mount
  useEffect(() => {
    const startCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: "environment" },
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        setIsCameraActive(true);
      } catch (err) {
        console.error("Error accessing camera:", err);
        alert("Could not access the camera. Please check permissions.");
      }
    };

    startCamera();

    return () => {
      // Clean up camera on unmount
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  // Take picture and automatically proceed to next step
  const takePicture = () => {
    if (!videoRef.current || !canvasRef.current) return;

    // Show camera flash effect
    setShowFlash(true);

    // Capture the image after a brief flash
    setTimeout(() => {
      // Set processing state to show loading indicator
      setIsProcessing(true);
      setShowFlash(false);

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      const imageDataUrl = canvas.toDataURL("image/png");
      setImage(imageDataUrl);

      // Make sure the image is properly set in the form values
      // This is critical to prevent the error toast
      setFieldValue("idImage", imageDataUrl);

      // Log to confirm the image is set

      setIsCameraActive(false);

      // Automatically proceed to next step after a short delay
      // The delay gives time for the image to be set in the form values
      setTimeout(() => {
        // Reset processing state
        setIsProcessing(false);
        // Navigate to next step
        const nextStepEvent = new CustomEvent("nextStep");
        document.dispatchEvent(nextStepEvent);
      }, 500); // Slightly longer delay to ensure the image is processed
    }, 150); // Short delay for flash effect
  };

  // Note: The retakePhoto functionality has been moved to Step4.js
  // Users can now try again directly from the review screen

  return (
    <div className="relative h-screen w-full flex flex-col bg-black">
      {/* Camera/Image Preview Area - takes remaining space */}
      <div className="flex-1 relative overflow-hidden">
        {/* Camera flash effect */}
        {showFlash && (
          <div className="absolute inset-0 bg-white z-50 opacity-70"></div>
        )}
        {isCameraActive ? (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            className="w-full h-full object-cover"
          />
        ) : image ? (
          <div className="relative w-full h-full">
            <img
              src={image}
              alt="Captured document"
              className="w-full h-full object-contain"
            />

            {/* Processing indicator */}
            {isProcessing && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div className="text-white text-center">
                  <div className="inline-block w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                  <p>Processing image...</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-black">
            <Camera size={48} className="text-gray-400" />
          </div>
        )}

        {/* Capture button positioned over the camera */}
        {isCameraActive && (
          <div className="absolute bottom-6 left-0 right-0 flex justify-center ">
            <button
              onClick={takePicture}
              className="w-20 h-20 rounded-full border-2 border-white flex items-center justify-center p-[10px]"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="60"
                height="61"
                viewBox="0 0 60 61"
                fill="none"
              >
                <circle cx="30" cy="30.5273" r="30" fill="white" />
              </svg>{" "}
            </button>
          </div>
        )}

        {/* No confirmation buttons needed as we automatically proceed to next step */}

        {/* Hidden canvas for capturing images */}
        <canvas ref={canvasRef} className="hidden" />
      </div>

      {/* Bottom Information Section */}
      <div className="relative top-[-15px] flex flex-col gap-2 rounded-t-xl items-center justify-center bg-black h-36 z-100 text-white">
        <h4 className="text-lg font-medium">Front of document</h4>
        <p className="text-sm text-gray-300">
          Take a photo of the front of your ID
        </p>
      </div>
    </div>
  );
};

export default Step3;
