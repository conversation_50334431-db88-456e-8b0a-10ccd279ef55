const projects = [
  {
    id: 1,
    name: "<PERSON>table",
    date: "3/24/2025",
    hasAudio: true,
    audioDuration: "00:10",
    type: "Birthday",
    tags: ["Strong", "Empowering"],
    message:
      "Happy birthday to you, Happy birthday to you, Happy birthday Happy birthday dear <PERSON>, Happy birthday to youMess<PERSON>",
    expectedDate: "3/25/2025",
    submitted: false,
  },
  {
    id: 2,
    name: "<PERSON> Huxtable",
    date: "3/24/2025",
    hasAudio: false,
    audioDuration: "00:00",
    type: "Birthday",
    tags: ["Strong", "Empowering"],
    message:
      "Happy birthday to you, Happy birthday to you, Happy birthday Happy birthday dear <PERSON>, Happy birthday to youMessage",
    expectedDate: "3/25/2025",
    submitted: false,
  },
];

export default function CongratsGreeting({ setShowSubmitted }) {
  return (
    <div className="flex flex-col h-screen pageBgGradient fixed inset-0">
      <div className="flex-1 flex flex-col items-center justify-center px-5 pt-10 pb-20">
        
          <div className="p-8 flex flex-col justify-between items-center shadow-lg">
            <div className="flex-1 ">
              <div className="text-2xl font-semibold text-white text-center mb-2">
                Your greeting has been submitted
              </div>

              <div className="relative w-72 h mt-10 flex items-center justify-center">
                <img
                  src="/images/home/<USER>"
                  alt="ellipse"
                  className="w-full m-auto absolute left-0 right-0 top-0 bottom-0 z-20"
                />
                <img
                  src="/images/home/<USER>"
                  alt="Landing Page"
                  className="w-20 m-auto relative z-10"
                />
              </div>

              <div className="text-gray-400 text-center my-8">
                take a look at your profile
              </div>
            </div>
            
          </div>
        
      </div>

      <button
              className="flex-none w-full py-3 mb-40 rounded-md pageBgGradient border border-white/20 text-white font-semibold text-sm"
              onClick={() => setShowSubmitted(false)}
            >
              View Greetings
            </button>
    </div>
  );
}
