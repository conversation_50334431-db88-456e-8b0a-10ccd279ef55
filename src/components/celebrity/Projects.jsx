import { useState } from "react";

import CongratsGreeting from "./CongratsGreeting";

const projects = [
  {
    id: 1,
    name: "<PERSON> Hu<PERSON>",
    date: "3/24/2025",
    hasAudio: true,
    audioDuration: "00:10",
    type: "Birthday",
    tags: ["Strong", "Empowering"],
    message:
      "Happy birthday to you, Happy birthday to you, Happy birthday Happy birthday dear <PERSON>, Happy birthday to youMessage",
    expectedDate: "3/25/2025",
    submitted: false,
  },
  {
    id: 2,
    name: "<PERSON> Hu<PERSON>",
    date: "3/24/2025",
    hasAudio: false,
    audioDuration: "00:00",
    type: "Birthday",
    tags: ["Strong", "Empowering"],
    message:
      "Happy birthday to you, Happy birthday to you, Happy birthday Happy birthday dear <PERSON>, Happy birthday to youMessage",
    expectedDate: "3/25/2025",
    submitted: false,
  },
];

export default function Projects({ expandedProjectId, setExpandedProjectId }) {
  const [showSubmitted, setShowSubmitted] = useState(false);

  return (
    <div className="flex flex-col gap-4 relative">
      {projects.map((project) => (
        <div
          key={project.id}
          className="pageBgGradient rounded-xl px-3 py-4 border border-white-700 text-white relative"
        >
          <div className="flex justify-between items-start w-full">
            <div className="font-semibold text-lg flex items-start gap-2">
              <img
                src="/images/home/<USER>"
                alt="Voice Icon"
                className="w-8 mt-1"
              />
              <div className="flex flex-col justify-start items-start min-h-12">
                <div className="font-semibold">{project.name}</div>
                <button
                  className="text-xs text-white-700 mt-2 underline cursor-pointer flex items-center gap-2"
                  onClick={() => setExpandedProjectId(expandedProjectId === project.id ? null : project.id)}
                >
                  {expandedProjectId === project.id ? "Hide Details " : "View Details "}
                  <img
                    src="/images/home/<USER>"
                    alt="Logo"
                    className={`w-4 ${expandedProjectId === project.id ? "rotate-270" : "rotate-180"}`}
                  />
                </button>
              </div>
            </div>

            <div className="flex items-start">
              <span className="text-xs text-white-700 mr-2">{project.expectedDate}</span>
              <button className="flex items-center justify-center">
                <img alt="Options" className="w-5" src="/images/home/<USER>" />
              </button>
            </div>
          </div>

          {/* Details section - always present but conditionally visible */}
          <div 
            className={`mt-2 overflow-hidden transition-all duration-300 ${
              expandedProjectId === project.id ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
            }`}
          >
            <div className="flex gap-2 items-center">
              <img
                src="/images/home/<USER>"
                alt="Calendar"
                className="w-3"
              />
              <div className="text-xs font-light pt-1">
                {project.type}
              </div>
            </div>
            <div className="flex gap-2 items-center pb-2">
              <img
                src="/images/home/<USER>"
                alt="Music"
                className="w-3"
              />
              <div className="text-xs font-light pt-1">
                {project.tags.join(", ")}
              </div>
            </div>

            <div className="font-semibold text-xs">Message</div>
            <div className="text-sm mb-2 whitespace-pre-line font-light">
              {project.message}
            </div>

            <div className="font-semibold text-xs">Expected Date</div>
            <div className="text-sm mb-2 whitespace-pre-line font-light">
              {project.expectedDate}
            </div>
          </div>

          <div className="w-full flex justify-end items-end waveBg h-10 mt-5 mb-1 text-xs shadow-black">
            <p>00:00/{project.audioDuration || "00:00"}</p>
          </div>

          {project.hasAudio ? (
            <div className="flex gap-2 mt-4">
              <button
                className="flex-1 py-2 rounded-md btnGrayGradient text-black font-semibold"
                onClick={() => setShowSubmitted(true)}
              >
                Submit
              </button>
              <button className="flex-1 py-2 rounded-md btnGrayGradient text-black font-semibold">
                Edit
              </button>
            </div>
          ) : (
            <button
              className="w-full py-2 rounded-md bg-white text-black font-semibold mt-4"
              onClick={() => setExpandedProjectId(project.id)}
            >
              Create Greeting
            </button>
          )}
        </div>
      ))}

      {showSubmitted && (
        <CongratsGreeting />
      )}
    </div>
  );
}