"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import Reviews from "../common/Reviews";
import GreetingsList from "../greetings/GreetingsList";
import useApiHook from "@/hooks/useApiHook";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";
import useToaster from "@/hooks/useToaster";
import { Loader, LoaderVariants } from "../common/Loader";
import { useSelector } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import AllCelebGreetings from "./AllCelebGreetings";
import Link from "next/link";
import ShareModal from "../common/ShareModal";

const CelebrityDetail = ({
  celebrity,
  categories,
  isProfile,
  fetchCelebrity,
}) => {
  const auth = useSelector(selectAuth);
  const userId = auth?.userInfo?.user?._id;
  const router = useRouter();
  const { handleApiCall } = useApiHook();
  const handleApiError = useApiErrorHandler();
  const { showSuccessToast } = useToaster();
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  const handleSubscriber = async () => {
    try {
      const response = await handleApiCall({
        method: "POST",
        url: "celebrities/follow",
        data: {
          celebrityId: celebrity?._id,
        },
      });
      if (response.status === 200) {
        fetchCelebrity();
        if (response?.data.isSubscribed) {
          showSuccessToast("Celebrity followed successfully");
        } else {
          showSuccessToast("Celebrity unfollowed successfully");
        }
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      {/* Profile */}
      <div className="relative w-full min-h-[478px]">
        <div className="w-full absolute px-6 pt-10 z-20">
          <div className="flex justify-between items-center gap-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-white cursor-pointer"
            >
              <Image
                src="/left-arrow.svg"
                width={20}
                height={20}
                alt="left-arrow"
              />
            </button>
            <h2 className="text-base font-normal">
              {isProfile ? "Profile" : "Discover"}
            </h2>
            <Image src="/options.svg" width={20} height={20} />
          </div>
          <div className="pt-[215px]">
            {celebrity?.verified && (
              <div className="flex gap-1 items-center">
                <Image
                  src="/verified.svg"
                  height={20}
                  width={20}
                  alt="verified-icon"
                />
                <h4 className="text-xs">Verified Celebrity</h4>
              </div>
            )}
            <div className="flex flex-col gap-2 pt-[10px]">
              <h1 className="text-4xl font-bold">
                {celebrity?.userId?.firstName} {celebrity?.userId?.lastName}
              </h1>
              <div className="flex justify-between items-center">
                <button
                  className="flex flex-col justify-center items-center"
                  onClick={handleSubscriber}
                >
                  <span className="text-sm font-medium">
                    {celebrity?.usageCount}
                  </span>
                  <span className="text-xs">Subscribers</span>
                </button>
                <div className="flex flex-col justify-center items-center">
                  <span className="text-sm font-medium">
                    {celebrity?.ratingSum}
                  </span>
                  <span className="text-xs">Reviews</span>
                </div>
                <div className="flex flex-col justify-center items-center">
                  <span className="text-sm font-medium">
                    ${celebrity?.price}
                  </span>
                  <span className="text-xs">Price</span>
                </div>
              </div>
              <div className="mt-2 flex gap-3">
                <button
                  className="cursor-pointer py-2 px-6 flex justify-center items-center gap-[10px] rounded-lg border border-[#ffffff50] w-1/2 grayButton"
                  onClick={() => {
                    if (isProfile) router.push("/edit-profile");
                    else handleSubscriber();
                  }}
                >
                  <Image
                    src={isProfile ? "/user.svg" : "/star.svg"}
                    width={18}
                    height={18}
                    alt="star"
                  />
                  <span className="text-sm font-medium">
                    {isProfile
                      ? "Edit Profile"
                      : celebrity?.subscribers?.some(
                          (item) => item?.userId === userId
                        )
                      ? "Subscribed"
                      : "Subscribe"}
                  </span>
                </button>
                <button
                  onClick={() => setIsShareModalOpen(true)}
                  className="cursor-pointer py-2 px-6 flex justify-center items-center gap-[10px] rounded-lg border border-black w-1/2 bg-white"
                >
                  <Image src="/share.svg" width={18} height={18} alt="share" />
                  <span className="text-black text-sm font-medium">Share</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <Image
          src={celebrity?.avatarUrl[0] || "/mock-profile.png"}
          alt={celebrity?.name}
          layout="fill"
          objectFit="cover"
          className="rounded-lg z-0 relative"
        />
        <div className=" absolute left-0 right-0 top-0 bottom-0 z-10 profile-gradient"></div>
      </div>

      <div className={`px-6 bg-white pt-6 pb-24`}>
        <div className="flex flex-col gap-3 bg-white text-black">
          <h2 className="text-base font-medium">{celebrity?.profession}</h2>
          <p className="text-sm">{celebrity?.bio}</p>
          {categories?.length > 0 && (
            <div className="flex gap-[10px] items-center">
              {celebrity?.categories?.map((item, index) => (
                <div
                  key={index}
                  className="flex justify-center items-center text-xs  rounded-3xl border border-gray-300 p-2"
                >
                  {categories.find((category) => category?._id === item)?.name}
                </div>
              ))}
            </div>
          )}
          {celebrity?.socialLinks?.length > 0 && (
            <div className="flex gap-4">
              {[
                "/images/social-icons/twitter.png",
                "/images/social-icons/instagram.svg",
                "/images/social-icons/random.svg",
              ].map((item, index) => (
                <Image
                  key={index}
                  src={item}
                  height={30}
                  width={30}
                  alt="social-icon"
                  className="cursor-pointer"
                />
              ))}
            </div>
          )}
        </div>

        {!isProfile && (
          <>
            <Reviews />
            <Link
              href={`/video-creation/${celebrity._id}`}
              className="mb-4 flex w-full justify-center cursor-pointer rounded-lg text-white p-3 grayButton  transition-all mt-4 font-bold"
            >
              Create Voice Greeting - $CLBX {celebrity.price}
            </Link>
          </>
        )}

        {isProfile && (
          <>
            <AllCelebGreetings />

            <div className="opacity-0 w-full border border-gray-300 rounded-lg mt-4 flex flex-col justify-center items-center py-6 ">
              <p className="text-sm text-black">
                Add a greeting for fans to see
              </p>
              <button
                onClick={() => router.push("/celebrity/create-greeting")}
                className="mb-4 flex gap-3 justify-center items-center cursor-pointer rounded-full text-white px-4 py-2 m-auto bg-black  transition-all mt-4 text-xs font-medium"
              >
                <img className="w-6" src="/images/home/<USER>" />{" "}
                <span>Create a greeting</span>
              </button>
            </div>
          </>
        )}
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        greetingData={{
          title: "Your greeting is ready!",
          subtitle: "Preview it on your profile or share it with friends",
          celebrityName: `${celebrity?.userId?.firstName} ${celebrity?.userId?.lastName}`,
          celebrityRole: celebrity?.category || "Celebrity",
          celebrityImage: celebrity?.avatarUrl?.[0] || "/iconuser.png",
          audioWaveIcon: "/re-record.png"
        }}
      />
    </div>
  );
};

export default CelebrityDetail;
