const requests = [
  {
    id: 1,
    name: "<PERSON> Hu<PERSON>",
    price: 250,
    type: "Birthday",
    tags: ["Strong", "Empowering"],
    message:
      "Happy birthday to you, Happy birthday to you, Happy birthday Happy birthday dear <PERSON>, Happy birthday to youMessage",
    expectedDate: "3/25/2025",
  },
  {
    id: 2,
    name: "<PERSON>",
    price: 250,
    type: "Congratulations",
    tags: ["Strong", "Empowering"],
    message: "",
    expectedDate: "",
  },
  {
    id: 3,
    name: "<PERSON>",
    price: 250,
    type: "Congratulations",
    tags: ["Strong", "Empowering"],
    message: "",
    expectedDate: "",
  },
];

export default function Requests({ expandedId, setExpandedId }) {
  return (
    <div className="flex flex-col gap-4">
      {requests.map((req) => (
        <div
          key={req.id}
          className="pageBgGradient rounded-xl p-4 border  text-white relative"
        >
          <div className="flex justify-between items-start pb-3">
            <div className="font-semibold flex items-center gap-2">
              {expandedId === req.id && (
                <img alt="Logo" className="w-4" src="/images/home/<USER>" />
              )}
              {req.name}
            </div>
            <div className="flex items-start gap-2">
              <div className="font-semibold">${req.price}</div>
              <img alt="Logo" className="w-5" src="/images/home/<USER>"></img>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <img
              src="/images/home/<USER>"
              alt="Voice Icon"
              className="w-3"
            />
            <div className="text-xs font-light pt-1">{req.type}</div>
          </div>

          <div className="flex gap-2 items-center  pb-2">
            <img
              src="/images/home/<USER>"
              alt="Voice Icon"
              className="w-3"
            />
            <div className="text-xs font-light pt-1">{req.tags.join(", ")}</div>
          </div>
          {expandedId === req.id ? (
            <>
              <button
                className="text-xs text-white-700 mt-2 underline cursor-pointer flex items-center gap-2"
                onClick={() => setExpandedId(null)}
              >
                Hide Details <img src="/images/home/<USER>" alt="Logo" className="w-4 rotate-270" />
              </button>
              <div className="mt-2">
                <div className="font-semibold text-xs">Message</div>
                <div className="text-sm mb-2 whitespace-pre-line  font-light">
                  {req.message}
                </div>
                <div className="font-semibold text-xs">Expected Date</div>
                <div className="text-sm mb-4 whitespace-pre-line  font-light">{req.expectedDate}</div>
                <div className="flex gap-2 text-sm">
                  <button onClick={() => setExpandedId(null)} className="flex-1 py-2 rounded-md btnGrayGradient text-black font-semibold  cursor-pointer hover:font-bold transition-all duration-300">
                    Accept
                  </button>
                  <button onClick={() => setExpandedId(null)} className="flex-1 py-2 rounded-md btnGrayGradient text-black font-semibold  cursor-pointer hover:font-bold transition-all duration-300">
                    Decline
                  </button>
                  <button onClick={() => setExpandedId(null)} className="flex-1 py-2 rounded-md btnGrayGradient text-black font-semibold  cursor-pointer hover:font-bold transition-all duration-300">
                    Message
                  </button>
                </div>
              </div>
            </>
          ) : (
            <button
              className="text-xs text-white-700 mt-2 underline cursor-pointer flex items-center gap-2"
              onClick={() => setExpandedId(req.id)}
            >
              View Details <img src="/images/home/<USER>" alt="Logo" className="w-4 rotate-180" />
            </button>
          )}
        </div>
      ))}
    </div>
  );
}
