const requests = [
  {
    id: 1,
    name: "<PERSON> Huxtable",
    price: 250,
    type: "Congratulations",
    time: ["00:00/00:10"],
    expectedDate: "3/24/2025",
  },
  {
    id: 2,
    name: "<PERSON>",
    price: 250,
    type: "Congratulations",
    time: ["00:00/00:10"],
    expectedDate: "3/24/2025",
  },
];
export default function Uploads() {
  return (
    <div className="flex flex-col gap-4">
      {requests.map((req) => (
        <div
          key={req.id}
          className="pageBgGradient rounded-xl px-3 py-2 border border-white-700 text-white relative"
        >
          <div className="flex justify-between items-start">
            <div className="font-semibold text-lg flex items-center gap-2">
              <img
                src="/images/home/<USER>"
                alt="Voice Icon"
                className="w-8"
              />
              <div className=" flex flex-col pt-2">
                <div className="font-semibold">{req.name}</div>
                <div className="flex gap-2 items-center">
                  <img
                    src="/images/home/<USER>"
                    alt="Voice Icon"
                    className="w-3"
                  />
                  <div className="text-xs font-light pt-1">{req.type}</div>
                </div>
              </div>
            </div>

            <button className="ml-2 flex gap-2  pt-2 items-start justify-start">
              <span className="material-icons text-white text-[.7rem] pt-1">
                {req.expectedDate}
              </span>
              <img alt="Logo" className="w-5" src="/images/home/<USER>"></img>
            </button>
          </div>
          <div className="w-full flex justify-end items-end waveBg h-10 mt-5 mb-1 text-xs shadow-black ">
            <p>{req.time}</p>
          </div>

        </div>
      ))}
    </div>
  );
}
