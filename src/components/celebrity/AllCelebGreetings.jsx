"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect ,useRef} from "react";
import { SampleGreetingService } from "@/services/SampleGreetingService";
import { useSelector, useDispatch } from "react-redux";
import { selectAuth } from "@/redux/slices/auth/selectors";
import { setGreetingPreviewOpen } from "@/redux/slices/ui/slice";
import { MoreHorizontal, X, Play, Pause, Edit, Download } from "lucide-react";

export default function AllCelebGreetings({ celebrities }) {
  const pathname = usePathname();
  const dispatch = useDispatch();
  const [greetings, setGreetings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [selectedGreeting, setSelectedGreeting] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [expandedId, setExpandedId] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);
  const videoRef = useRef(null);
  const [editFormData, setEditFormData] = useState({
    occasion: '',
    tone: '',
    tags: [],
    script: ''
  });
  const [newTag, setNewTag] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const auth = useSelector(selectAuth);
  const celebrityId = auth?.userInfo?.user?.celebrityProfile?._id ||
                     auth?.userInfo?.user?._id;

  useEffect(() => {
    const fetchGreetings = async () => {
      if (!celebrityId) {
        setLoading(false);
        return;
      }
      try {
        const response = await SampleGreetingService.getSampleGreetings(celebrityId);
        console.log('Fetched greetings:', response.data);
        if (response?.data) {
          setGreetings(Array.isArray(response.data) ? response.data : [response.data]);
        }
      } catch (error) {
        console.error('Error fetching greetings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGreetings();
  }, [celebrityId]);

  useEffect(() => {
    if (videoRef.current && selectedGreeting?.recordedVideoUrl && showPreview) {
      const video = videoRef.current;

      video.load();

      return () => {
        if (video) {
          video.pause();
          video.currentTime = 0;
        }
      };
    }
  }, [selectedGreeting?.recordedVideoUrl, showPreview]);

  const handleGreetingClick = (greeting) => {
    setSelectedGreeting(greeting);
    setShowPreview(true);
    setIsPlaying(false);
    setCurrentTime(0);
    setVideoDuration(0);
    dispatch(setGreetingPreviewOpen(true));
  };

  const handleClosePreview = () => {
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }

    setShowPreview(false);
    setSelectedGreeting(null);
    setIsPlaying(false);
    setCurrentTime(0);
    setVideoDuration(0);
    dispatch(setGreetingPreviewOpen(false)); // Show bottom nav
  };

  const handleEditGreeting = async () => {
    setShowPreview(false);
    setShowEditModal(true);

    if (selectedGreeting?._id) {
      setEditFormData({
        occasion: selectedGreeting.occasion || selectedGreeting.title || '',
        tone: selectedGreeting.tone || selectedGreeting.tones?.[0] || '',
        tags: Array.isArray(selectedGreeting.tags) ? selectedGreeting.tags : [],
        script: selectedGreeting.script || selectedGreeting.scriptText || ''
      });
    }
    // Keep bottom nav hidden since we're still in modal mode
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedGreeting(null);
    dispatch(setGreetingPreviewOpen(false)); // Show bottom nav
  };

  const handleUpdateGreeting = async () => {
    if (!selectedGreeting?._id) return;

    setIsUpdating(true);
    try {
      const formData = new FormData();
      formData.append('occasion', editFormData.occasion);
      formData.append('tone', editFormData.tone);
      formData.append('script', "this is the sample greeting scripte");

      // Add tags
      editFormData.tags.forEach(tag => {
        formData.append('tags', tag);
      });

      const response = await SampleGreetingService.updateSampleGreeting(selectedGreeting._id, formData);

      if (response.status === 200) {
        // Refresh greetings list
        const updatedGreetings = await SampleGreetingService.getSampleGreetings(celebrityId);
        if (updatedGreetings?.data) {
          setGreetings(Array.isArray(updatedGreetings.data) ? updatedGreetings.data : [updatedGreetings.data]);
        }

        setShowEditModal(false);
        setShowSuccessModal(true);
      }
    } catch (error) {
      console.error('Error updating greeting:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && editFormData.tags.length < 5 && !editFormData.tags.includes(newTag.trim())) {
      setEditFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setEditFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleFormFieldChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLooksGreat = () => {
    setShowPreview(false);
    setShowSuccessModal(true);
    // Keep bottom nav hidden since we're showing success modal
  };

  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    setSelectedGreeting(null);
    dispatch(setGreetingPreviewOpen(false)); // Show bottom nav
  };

  const handleOptionsClick = (e, greetingId) => {
    e.stopPropagation();
    setExpandedId(expandedId === greetingId ? null : greetingId);
  };

  const togglePlayPause = () => {
    if (videoRef.current && selectedGreeting?.recordedVideoUrl) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        videoRef.current.play().then(() => {
          setIsPlaying(true);
        }).catch((error) => {
          console.error('Error playing video:', error);
          setIsPlaying(false);
        });
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current && !videoRef.current.paused) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current && videoRef.current.duration && isFinite(videoRef.current.duration)) {
      // Get the actual video duration from the video element
      setVideoDuration(videoRef.current.duration);
      setCurrentTime(0);
    }
  };

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Fallback sample data
  const sampleGreetings = [
    {
      _id: '1',
      title: 'Birthday',
      avatarImageUrl: '/images/celebrities/david.jpg',
      tags: ['Friendly', 'Warm'],
      occasion: 'Birthday',
      tone: 'Personal / Warm'
    },
    {
      _id: '2',
      title: 'Anniversary',
      avatarImageUrl: '/images/celebrities/david.jpg',
      tags: ['Romantic', 'Sweet'],
      occasion: 'Anniversary',
      tone: 'Personal / Warm'
    },
    {
      _id: '3',
      title: 'Congratulations',
      avatarImageUrl: '/images/celebrities/david.jpg',
      tags: ['Excited', 'Happy'],
      occasion: 'Congratulations',
      tone: 'Personal / Warm'
    }
  ];

  const displayGreetings = loading ? sampleGreetings : greetings;

  return (
    <>
      <div className="overflow-x-auto celebGreetings mt-4">
        <div
          className="flex gap-2 mb-4"
          style={{ minWidth: "calc(100% + 16px)" }}
        >
          {displayGreetings.map((greeting, index) => (
            <div
              key={greeting._id || index}
              className="w-44 rounded-lg flex flex-col border shrink-0 border-gray-300 cursor-pointer"
              onClick={() => handleGreetingClick(greeting)}
            >
              <div className="w-full relative rounded-lg">
                <div className="relative">
                  <button
                    className="absolute right-2 top-2 z-40 p-1 rounded-full bg-black/40 backdrop-blur-sm text-white hover:bg-black/60 transition-colors"
                    onClick={(e) => handleOptionsClick(e, greeting._id)}
                  >
                    <MoreHorizontal size={16} />
                  </button>

                  {expandedId === greeting._id && (
                    <div className="absolute top-10 right-2 w-36 bg-black border border-gray-800 rounded-md shadow-lg overflow-hidden z-50">
                      <div className="flex flex-col text-sm">
                        <button
                          className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleGreetingClick(greeting);
                          }}
                        >
                          View details
                        </button>
                        <button className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white">
                          Edit greeting
                        </button>
                        <button className="px-4 py-2 text-left hover:bg-white/10 transition-colors text-white">
                          Delete
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="absolute left-0 right-0 top-0 bottom-0 z-10 profile-gradient"></div>
                <div className="w-full h-56">
                  <Image
                    src={greeting.avatarImageUrl || "/images/celebrities/david.jpg"}
                    width={146}
                    height={146}
                    alt="celeb-image"
                    className="w-full h-full rounded-md object-cover relative z-0"
                  />
                </div>

                <div className="flex items-start flex-col absolute left-2 bottom-2 gap-2 z-20">
                  <h3 className="font-semibold text-sm text-white">
                    {greeting.title || greeting.occasion || 'Birthday'}
                  </h3>
                  <div className="flex gap-2">
                    {(greeting.tags || greeting.tones || []).slice(0, 2).map((tag, tagIndex) => (
                      <div
                        key={tagIndex}
                        className="flex justify-center items-center text-[.65rem] rounded-3xl border border-white/30 py-1 px-2 text-white"
                      >
                        {typeof tag === 'string' ? tag : tag.name || tag.text || tag}
                      </div>
                    ))}
                    {/* Show fallback if no tags */}
                    {(!greeting.tags || greeting.tags.length === 0) && (!greeting.tones || greeting.tones.length === 0) && (
                      <div className="flex justify-center items-center text-[.65rem] rounded-3xl border border-white/30 py-1 px-2 text-white">
                        Friendly
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Voice Preview Modal */}
      {showPreview && selectedGreeting && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
          {/* Header with close button */}
          <div className="absolute top-4 right-4 z-10">
            <button
              onClick={handleClosePreview}
              className="text-white hover:text-gray-300 transition-colors p-2"
            >
              <X size={24} />
            </button>
          </div>

          {/* Full screen video or thumbnail */}
          <div className="relative flex-1">
            {selectedGreeting.recordedVideoUrl ? (
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                controls={false}
                autoPlay={isPlaying}
                muted={false}
                loop={false}
                onEnded={() => setIsPlaying(false)}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
                onError={(e) => console.error('Video error:', e)}
                playsInline
                style={{ display: isPlaying ? 'block' : 'none' }}
              >
                <source src={selectedGreeting.recordedVideoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : null}

            {!isPlaying && (
              <Image
                src={selectedGreeting.avatarImageUrl || "/images/celebrities/david.jpg"}
                fill
                alt="celebrity"
                className="object-cover"
              />
            )}

            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/80"></div>

            {/* Celebrity name overlay */}
            <div className="absolute top-4 left-4 z-10">
              <div className="bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2">
                <h3 className="text-white font-semibold text-sm">
                  {selectedGreeting.title || selectedGreeting.occasion || 'French Montana'}
                </h3>
                <p className="text-white/80 text-xs">
                  {selectedGreeting.tone || selectedGreeting.tones?.[0] || 'Personal / Warm'}
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-black p-4">
            {/* Progress Bar */}
            <div className="mb-4">
              <div className="h-1 w-full bg-gray-800 rounded-full overflow-hidden">
                <div
                  className="h-full"
                  style={{
                    width: videoDuration ? `${(currentTime / videoDuration) * 100}%` : '0%',
                    backgroundColor: 'white',
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}
                />
              </div>
              <div className="flex justify-between text-sm mt-2 text-white">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(videoDuration)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-3 gap-2 pt-2">
              <button
                onClick={handleEditGreeting}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
              >
                <div className="h-12 w-12 rounded-full flex items-center justify-center mb-1"
                  style={{
                    background: 'linear-gradient(180deg, #000 0%, #242424 100%), #FFF',
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}>
                  <Edit size={20} className="text-white" />
                </div>
                <span className="text-xs mt-1">Edit greeting</span>
              </button>

              <button
                onClick={togglePlayPause}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
                disabled={!selectedGreeting.recordedVideoUrl}
              >
                <div className="h-14 w-14 rounded-full bg-white/20 flex items-center justify-center mb-1"
                  style={{
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)',
                    opacity: !selectedGreeting.recordedVideoUrl ? 0.5 : 1
                  }}>
                  {isPlaying ? <Pause size={24} className="text-white" /> : <Play size={24} className="text-white" />}
                </div>
                <span className="text-xs mt-1">
                  {!selectedGreeting.recordedVideoUrl
                    ? 'No video available'
                    : isPlaying
                      ? 'Pause greeting'
                      : 'Play greeting'
                  }
                </span>
              </button>

        <button
                onClick={handleLooksGreat}
                className="flex flex-col items-center justify-center p-2 transition-transform hover:scale-105"
              >
                <div className="h-14 w-14 rounded-full  flex items-center justify-center mb-1"
                  style={{
                    boxShadow: '0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03)'
                  }}>
                  <img src="/looks-great.svg" alt="Looks great" className="w-6 h-6" />
                </div>
                <span className="text-xs mt-1">Looks great</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Greeting Modal */}
      {showEditModal && selectedGreeting && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-800">
            <button
              onClick={handleCloseEditModal}
              className="text-white hover:text-gray-300 transition-colors"
            >
              <X size={24} />
            </button>
            <h3 className="text-white text-lg font-semibold">Greeting details</h3>
            <div className="w-6"></div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 space-y-6 overflow-y-auto">
            {/* Celebrity Avatar */}
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-20 h-20 rounded-full overflow-hidden">
                  <Image
                    src={selectedGreeting.avatarImageUrl || "/images/celebrities/david.jpg"}
                    width={80}
                    height={80}
                    alt="celebrity"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                  <Edit size={12} className="text-white" />
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              <div>
                <label className="block text-gray-400 text-sm mb-2">Occasion</label>
                <select
                  className="w-full bg-black text-white p-3 rounded-lg border border-gray-600 focus:border-white focus:outline-none"
                  value={editFormData.occasion}
                  onChange={(e) => handleFormFieldChange('occasion', e.target.value)}
                >
                  <option value="Birthday">Birthday</option>
                  <option value="Anniversary">Anniversary</option>
                  <option value="Congratulations">Congratulations</option>
                  <option value="Just Because">Just Because</option>
                  <option value="Graduation">Graduation</option>
                  <option value="Holiday">Holiday</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-400 text-sm mb-2">Tone</label>
                <select
                  className="w-full bg-black text-white p-3 rounded-lg border border-gray-600 focus:border-white focus:outline-none"
                  value={editFormData.tone}
                  onChange={(e) => handleFormFieldChange('tone', e.target.value)}
                >
                  <option value="Personal / Warm">Personal / Warm</option>
                  <option value="Professional">Professional</option>
                  <option value="Funny">Funny</option>
                  <option value="Inspirational">Inspirational</option>
                  <option value="Excited">Excited</option>
                  <option value="Calm">Calm</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-400 text-sm mb-2">Add Tags (max. 5 tags)</label>

                <div className="flex">
                  <input
                    type="text"
                    placeholder="e.g. Rapper, Comedian, LA Native"
                    className="flex-1 bg-black text-white p-3 rounded-l-lg border border-gray-600 focus:border-white focus:outline-none"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                  />
                  <button
                    className="bg-black text-white px-4 rounded-r-lg border border-gray-600 hover:bg-gray-900 transition-colors"
                    onClick={handleAddTag}
                    disabled={editFormData.tags.length >= 5}
                  >
                    Add
                  </button>
                </div>

              </div>
   <div className="flex flex-wrap gap-2 mb-3">
                  {editFormData.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-gray-600 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2"
                    >
                      {tag}
                      <X
                        size={14}
                        className="cursor-pointer hover:text-gray-300"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </span>
                  ))}
                </div>
              <div>
                <label className="block text-gray-400 text-sm mb-2">Message</label>
                <textarea
                  rows={4}
                  className="w-full bg-black text-white p-3 rounded-lg border border-gray-600 focus:border-white focus:outline-none resize-none"
                  value={editFormData.script}
                  onChange={(e) => handleFormFieldChange('script', e.target.value)}
                  placeholder="Enter your greeting message..."
                />
              </div>
            </div>
          </div>

          {/* Bottom Button */}
          <div className="p-6">
            <button
              onClick={handleUpdateGreeting}
              disabled={isUpdating}
              className="w-full text-white py-3 px-6 font-semibold transition-colors disabled:opacity-50"
              style={{
                borderRadius: '8px',
                border: '1px solid #000',
                background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000',
                boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.48)'
              }}
            >
              {isUpdating ? 'Updating...' : 'Update greeting'}
            </button>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && selectedGreeting && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col items-center justify-center" style={{ fontFamily: '"AG Book Pro", sans-serif' }}>
          {/* Close button */}
          <div className="absolute top-4 right-4 z-10">
            <button
              onClick={handleCloseSuccessModal}
              className="text-white hover:text-gray-300 transition-colors p-2"
            >
              <X size={24} />
            </button>
          </div>

          {/* Success Content */}
          <div className="text-center px-6">
            <div className="mb-8">
              <div className="w-24 h-24 rounded-full overflow-hidden mx-auto mb-4 relative">
                <img
                  src="/images/home/<USER>"
                  alt="ellipse"
                  className="max-w-64 m-auto absolute left-0 right-0 top-0 bottom-0 z-20"
                />
                <Image
                  src={selectedGreeting.avatarImageUrl || "/images/celebrities/david.jpg"}
                  width={96}
                  height={96}
                  alt="celebrity"
                  className="w-full h-full object-cover relative z-10"
                />
              </div>

            </div>

            <h2 className="text-white text-2xl font-bold mb-4">Creating greeting preview!</h2>
            <p className="text-gray-300 text-sm mb-8 max-w-xs mx-auto">
              Your greeting has been saved and is now available on your profile
            </p>
{/*
            <button
              onClick={handleCloseSuccessModal}
              className="w-full max-w-xs mx-auto text-white py-3 px-6 font-semibold transition-colors"
              style={{
                borderRadius: '8px',
                border: '1px solid #000',
                background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.00) 100%), #000',
                boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.48)'
              }}
            >
              Continue
            </button> */}
          </div>
        </div>
      )}
    </>
  );
}
