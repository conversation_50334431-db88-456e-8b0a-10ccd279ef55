import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function AllCelebrities({ celebrities }) {
  const pathname = usePathname();
  return (
    <div className="overflow-x-auto">
      <div
        className="flex gap-4 mb-4"
        style={{ minWidth: "calc(100% + 16px)" }}
      >
        {celebrities &&
          celebrities.map((item, index) => (
            <div
              key={index}
              className={`min-w-[45%] p-3 rounded-lg flex flex-col border shrink-0 ${
                pathname === "/profile" ? "border-black" : "border-[#ffffff50]"
              }`}
            >
              <div className="w-full relative rounded-lg">
                <button className="absolute bottom-2 left-2 flex items-center justify-center w-8 h-8 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,_rgba(255,255,255,0.42)_0%,_rgba(153,153,153,0.42)_100%)] shadow-[0px_4px_4px_rgba(0,0,0,0.25)] cursor-pointer">
                  <Image
                    src="/Play.svg"
                    height={18}
                    width={18}
                    alt="play-icon"
                  />
                </button>
                <div className="w-full h-[10rem]">
                  <Image
                    src={
                      (item?.avatarUrl?.length > 0 && item?.avatarUrl[0]) ||
                      "/images/home/<USER>"
                    }
                    width={146}
                    height={146}
                    alt="celeb-image"
                    className="w-full h-[10rem] rounded-md object-cover"
                  />
                </div>
              </div>
              <div className=" pt-1">
                <Link
                  href={`/customer/discover/${item?._id}`}
                  className="text-sm font-medium mt-2 hover:underline cursor-pointer"
                >
                  {item?.userId?.firstName} {item?.userId?.lastName}
                </Link>
                <p className="text-xs">{item?.profession}</p>
                <div className="w-full flex justify-between items-center">
                  <span>${item.price}</span>
                  <div className="flex gap-1">
                    <Image
                      src="/rating.svg"
                      width={16}
                      height={16}
                      alt="rating"
                    />
                    <span className="underline text-xs">
                      {item.rating} ({item.ratingSum})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
