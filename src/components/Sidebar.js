'use client';

import Link from 'next/link';
import {
  UserIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon,
  Squares2X2Icon,
  StarIcon,
  UsersIcon,
  HomeIcon,
  BellIcon,
  ShoppingCartIcon,
  CreditCardIcon,
  UserCircleIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  LifebuoyIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { logoutUser } from '@/redux/slices/auth/slice';

export default function Sidebar() {
  const dispatch = useDispatch();
  const router = useRouter();
  const handleLogout = () => {
    dispatch(logoutUser());
    router.push('/auth/start');
  };
  const sidebarLinks = [
    
    {
      title: 'Users Management',
      path: '/admin/users',
      icon: <UsersIcon className='w-5 h-5' />,
    },
    {
      title: 'Platform Analytics',
      path: '/admin/analytics',
      icon: <HomeIcon className='w-5 h-5' />,
    },
    {
      title: 'Orders',
      path: '/admin/orders',
      icon: <ShoppingCartIcon className='w-5 h-5' />,
    },
    {
      title: 'Settings',
      path: '/admin/settings',
      icon: <Cog6ToothIcon className='w-5 h-5' />,
    },
    {
      title: 'Logout',
      onclick: handleLogout,
      icon: <ArrowRightOnRectangleIcon className='w-5 h-5' />,
    },
    // {
    //   title: 'Payments',
    //   path: '/admin/payments',
    //   icon: <CreditCardIcon className='w-5 h-5' />,
    // },
    
    // {
    //   title: 'Users',
    //   path: '/admin/users',
    //   icon: <UsersIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Analytics',
    //   path: '/admin/analytics',
    //   icon: <ChartBarIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Audit',
    //   path: '/admin/audit',
    //   icon: <ClipboardDocumentListIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Categories',
    //   path: '/admin/categories',
    //   icon: <Squares2X2Icon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Celebrity',
    //   path: '/admin/celebrity',
    //   icon: <StarIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Customers',
    //   path: '/admin/customers',
    //   icon: <UserIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Notifications',
    //   path: '/admin/notifications',
    //   icon: <BellIcon className='w-5 h-5' />,
    // },

    // {
    //   title: 'Profile',
    //   path: '/admin/profile',
    //   icon: <UserCircleIcon className='w-5 h-5' />,
    // },
    // {
    //   title: 'Reports',
    //   path: '/admin/reports',
    //   icon: <DocumentTextIcon className='w-5 h-5' />,
    // },
    
    // {
    //   title: 'Support',
    //   path: '/admin/support',
    //   icon: <LifebuoyIcon className='w-5 h-5' />,
    // },

  ];
  return (
    <aside className='relative w-64 h-screen bg-[#0c0c0c] shadow-lg z-10'>
      {/* Gradient Right Border */}
      <div className='absolute top-0 right-0 w-[2px] h-full bg-gradient-to-b from-white/0 via-white/50 to-white/0'></div>

      <div className='px-4 sm:px-6 lg:px-8 py-8'>
        <Link href='/' className='text-2xl font-bold text-white'>
          CelebGreetings
        </Link>
      </div>
      <nav className='mt-6 space-y-2 px-4'>
        {sidebarLinks.map(({ title, path, icon, onclick }) =>
          title === 'Logout' ? (
            <button
              className='w-full flex items-center gap-3 py-2 px-3 rounded hover:bg-indigo-100 hover:text-black text-white cursor-pointer'
              onClick={onclick}
            >
              {icon}
              {title}
            </button>
          ) : (
            <Link
              key={title}
              href={path}
              className='flex items-center gap-3 py-2 px-3 rounded hover:bg-indigo-100 hover:text-black text-white'
            >
              {icon}
              <span>{title}</span>
            </Link>
          )
        )}
      </nav>
    </aside>
  );
}
