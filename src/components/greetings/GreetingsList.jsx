"use client";

import Image from "next/image";

export default function GreetingsList() {
  return (
    <div className="flex flex-col gap-2 mb-4">
      {[1, 2, 3].map((item) => (
        <div
          key={item}
          className="flex  cursor-pointer items-center justify-between p-3 rounded-lg border-[1.5px] border-[#ffffff50] bg-gradient-to-b from-white/20 to-transparent bg-black"
        >
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 rounded-full">
              <Image
                src="/trending-voice.png"
                width={40}
                height={40}
                alt="voice"
              />
            </div>
            <div className="flex flex-col justify-center gap-0.5 text-white">
              <p className="text-sm font-medium"><PERSON><PERSON><PERSON></p>
              <div className="flex gap-1">
                <Image
                  src="/Calendar.svg"
                  width={12}
                  height={12}
                  alt="calender"
                />
                <p className="text-xs">Birthday</p>
              </div>
            </div>
          </div>
          <span>00:05</span>
        </div>
      ))}
    </div>
  );
}
