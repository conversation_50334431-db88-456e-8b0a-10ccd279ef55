"use client";

import { useFormikContext, Field } from "formik";

const Step4 = () => {
  const { values, setFieldValue } = useFormikContext();

  return (
    <div className="space-y-4">
      <label htmlFor="message" className="block text-xs text-[#6C7278] mb-2">
        Message
      </label>
      <Field
        as="textarea"
        name="message"
        id="message"
        maxLength={400}
        value={values.bio}
        onChange={(e) => setFieldValue("message", e.target.value)}
        className="w-full p-3 border border-gray-300 rounded-lg min-h-[200px] h-full outline-none"
        placeholder="Type here"
      />
      <div className="text-right text-gray-500 text-sm">
        {values.message.length} / 400 characters
      </div>
      <div className="relative flex justify-between px-3 py-4 border border-gray-300 rounded-lg">
        <div className="text-black">
          <h3 className="font-[500] text-sm">More characters</h3>
          <span className="text-xs">400 extra characters for $CLBX 250</span>
        </div>
        <button
          type="button"
          className="absolute right-3 top-1/2 transform -translate-y-1/2 flex justify-center items-center text-xs rounded-3xl border border-black bg-black text-white px-2 py-1 cursor-pointer"
        >
          Add
        </button>{" "}
      </div>
    </div>
  );
};

export default Step4;
