"use client";
import { Field } from "formik";
import Image from "next/image";

const Step1 = ({ values, errors, touched, celebrity }) => {
  return (
    <div>
      {/* Profile Picture */}
      <div className="flex flex-col items-center gap-3">
        <div className="w-16 h-16 relative rounded-full overflow-hidden">
          <Image
            src={celebrity?.avatarUrl ? celebrity?.avatarUrl[0] : "/person.svg"}
            alt="Celebrity Profile"
            fill
            style={{ objectFit: "cover" }}
            className="rounded-full"
          />
        </div>
        {celebrity?.userId && (
          <h1 className="text-center font-semibold text-lg">
            {`${celebrity?.userId?.firstName} ${celebrity?.userId?.lastName}`}
          </h1>
        )}
      </div>

      {/* Radio Options */}
      <div className="mt-6 space-y-4">
        <label className="flex items-center border rounded-lg p-4 cursor-pointer">
          <Field
            type="radio"
            name="greetingFor"
            value="myself"
            className="mr-4 h-5 w-5 accent-black"
          />
          <span className="text-base font-normal">For myself</span>
        </label>

        <label className="flex items-center border rounded-lg p-4 cursor-pointer">
          <Field
            type="radio"
            name="greetingFor"
            value="someone"
            className="mr-4 h-5 w-5 accent-black"
          />
          <span className="text-base font-normal">For someone</span>
        </label>
      </div>

      {/* Conditional Name Input */}
      {values.greetingFor === "myself" && (
        <div className="mt-6">
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            What is your name
          </label>
          <Field
            id="name"
            name="name"
            type="text"
            placeholder="Name"
            className="w-full rounded-lg border border-gray-300 p-3 text-base focus:outline-none"
          />
          {errors.name && touched.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>
      )}

      {/* Conditional Inputs for "someone" */}
      {values.greetingFor === "someone" && (
        <div className="mt-6 space-y-4">
          <div>
            <label
              htmlFor="to"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              To
            </label>
            <Field
              id="to"
              name="to"
              type="text"
              className="w-full rounded-lg border border-gray-300 p-3 text-base focus:outline-none"
            />
            {errors.to && touched.to && (
              <p className="mt-1 text-sm text-red-600">{errors.to}</p>
            )}
          </div>
          <div>
            <label
              htmlFor="from"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              From
            </label>
            <Field
              id="from"
              name="from"
              type="text"
              className="w-full rounded-lg border border-gray-300 p-3 text-base focus:outline-none"
            />
            {errors.from && touched.from && (
              <p className="mt-1 text-sm text-red-600">{errors.from}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Step1;
