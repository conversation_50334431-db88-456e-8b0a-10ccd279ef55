"use client";
import { Field } from "formik";
import Image from "next/image";

const Step5 = ({ values, celebrity }) => {
  return (
    <div>
      {/* Profile Picture */}
      <div className="flex flex-col items-center gap-3 mb-6">
        <div className="w-16 h-16 relative rounded-full overflow-hidden">
          <Image
            src={celebrity?.avatarUrl[0]}
            alt="Celebrity Profile"
            fill
            style={{ objectFit: "cover" }}
            className="rounded-full"
          />
        </div>
        <h1 className="text-center font-semibold text-lg">
          {`${celebrity?.userId?.firstName} ${celebrity?.userId?.lastName}`}
        </h1>
      </div>

      {/* To */}
      <div className="mb-4">
        <label
          htmlFor="to"
          className="block text-xs font-medium text-[Grey] mb-1"
        >
          To
        </label>
        <Field
          id="to"
          name="to"
          type="text"
          placeholder="To"
          className="w-full rounded-lg border border-gray-300 p-3 text-sm text-[Grey] focus:outline-none focus:ring-2 focus:ring-black cursor-not-allowed"
          disabled
        />
      </div>

      {/* From */}
      <div className="mb-4">
        <label
          htmlFor="to"
          className="block text-xs font-medium text-[Grey] mb-1"
        >
          From
        </label>
        <Field
          id="from"
          name="from"
          type="text"
          placeholder="From"
          className="w-full rounded-lg border border-gray-300 p-3 text-sm text-[Grey] focus:outline-none focus:ring-2 focus:ring-black cursor-not-allowed"
          disabled
        />
      </div>

      {/* Occasion */}
      <div className="mb-4">
        <label
          htmlFor="to"
          className="block text-xs font-medium text-[Grey] mb-1"
        >
          Occasions
        </label>
        <Field
          id="occasions"
          name="occasions"
          type="text"
          className="w-full rounded-lg border border-gray-300 p-3 text-sm text-[Grey] focus:outline-none focus:ring-2 focus:ring-black cursor-not-allowed"
          disabled
        />
      </div>

      {/* Tone with Emoji prefix */}
      <div className="mb-4">
        <label
          htmlFor="to"
          className="block text-xs font-medium text-[Grey] mb-1"
        >
          Tones
        </label>
        <Field
          id="tones"
          name="tones"
          type="text"
          className="w-full rounded-lg border border-gray-300 p-3 text-sm text-[Grey] focus:outline-none focus:ring-2 focus:ring-black cursor-not-allowed"
          disabled
        />
      </div>

      {/* Message textarea with character count */}
      <div className="mb-6">
        <label
          htmlFor="to"
          className="block text-xs font-medium text-[Grey] mb-1"
        >
          Message
        </label>
        <Field
          as="textarea"
          id="message"
          name="message"
          placeholder="Message"
          maxLength={400}
          rows={5}
          className="w-full rounded-lg border border-gray-300 p-3 text-sm text-[Grey] focus:outline-none focus:ring-2 focus:ring-black cursor-not-allowed"
          disabled
        />
        <div className="text-right text-xs text-gray-500 mt-1">
          {values.message ? values.message.length : 0} / 400 characters
        </div>
      </div>
    </div>
  );
};

export default Step5;
