"use client";

import { useFormikContext, FieldArray } from "formik";

const Step2 = () => {
  const { values, setFieldValue } = useFormikContext();

  const occasions = [
    { name: "Birthday" },
    { name: "Miles<PERSON>" },
    { name: "<PERSON>" },
    { name: "Empowerment" },
    { name: "Anniversary" },
    { name: "Congratulations" },
    { name: "Just Because" },
    { name: "<PERSON>" },
    { name: "Pop Talk" },
    { name: "Roast/Funny" },
  ];

  const toggleOccasion = (name) => {
    if (values.occasions.includes(name)) {
      setFieldValue(
        "occasions",
        values.occasions.filter((item) => item !== name)
      );
    } else {
      setFieldValue("occasions", [...values.occasions, name]);
    }
  };

  return (
    <div className="p-4">
      <FieldArray
        name="occasions"
        render={() => (
          <div className="flex flex-wrap items-center justify-center gap-1 mb-6">
            {occasions.map((occasion) => (
              <button
                key={occasion.name}
                type="button"
                onClick={() => toggleOccasion(occasion.name)}
                className={`flex flex-col items-center p-2 rounded-[24px] border-1 transition-all duration-200 cursor-pointer ${
                  values.occasions.includes(occasion.name)
                    ? "border-black bg-black text-white"
                    : "border-[#EAECF0] bg-white hover:bg-gray-50"
                }`}
              >
                <span className="font-normal text-xs leading-[140%] tracking-[-1%]">
                  {occasion.name}
                </span>
              </button>
            ))}
          </div>
        )}
      />
    </div>
  );
};

export default Step2;
