"use client";

import Image from "next/image";

const ShowPreview = ({ celebrity, isGreeting, setShowCheckout }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-80 z-50 previewpageBgGradient">
      <div className="max-w-md m-auto p-10 w-full h-screen flex-col flex items-center justify-center">
        <div
          className={`w-full flex items-center mb-4 max-w-md text-white ${
            isGreeting ? "justify-end" : "justify-between"
          }`}
        >
          {!isGreeting && (
            <h2 className="text-base font-medium w-full text-center">
              Voice Preview
            </h2>
          )}
          <button onClick={() => isGreeting && setShowCheckout(false)}>
            <Image
              src="/x.svg"
              width={20}
              height={20}
              alt="cross"
              className="invert"
            />
          </button>
        </div>
        <div className="flex flex-col items-center justify-end  gap-[50px] text-white flex-1">
          <h2 className="text-2xl font-semibold text-center">
            {isGreeting
              ? "Your greeting is ready!"
              : "Creating greeting preview"}
          </h2>
          <div className="relative w-full flex items-center justify-center">
            <div className="absolute">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="327"
                height="326"
                viewBox="0 0 327 326"
                fill="none"
              >
                <g filter="url(#filter0_f_254_6988)">
                  <circle
                    cx="163.341"
                    cy="162.777"
                    r="82.7585"
                    fill="white"
                    fill-opacity="0.26"
                  />
                </g>
                <defs>
                  <filter
                    id="filter0_f_254_6988"
                    x="0.582031"
                    y="0.0185547"
                    width="325.516"
                    height="325.517"
                    filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB"
                  >
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend
                      mode="normal"
                      in="SourceGraphic"
                      in2="BackgroundImageFix"
                      result="shape"
                    />
                    <feGaussianBlur
                      stdDeviation="40"
                      result="effect1_foregroundBlur_254_6988"
                    />
                  </filter>
                </defs>
              </svg>
            </div>
            <div className={`relative w-20 h-20 ${isGreeting && "ml-14"}`}>
              {/* Soundwave Image behind */}
              {isGreeting && (
                <div className="absolute left-[-80px] top-[-16px] z-10">
                  <Image
                    src="/soundwave.svg"
                    alt="sound-wave"
                    width={125}
                    height={125}
                    className="object-contain"
                  />
                </div>
              )}

              {/* Circular Profile Image overlapping on top */}
              <div className="absolute top-1/2 left-1/2 z-20 w-20 h-20 rounded-full border-[1.8px] border-[#999999] overflow-hidden transform -translate-x-1/2 -translate-y-1/2 bg-white">
                <Image
                  src={
                    celebrity?.avatarUrl
                      ? celebrity?.avatarUrl[0]
                      : "/person.svg"
                  }
                  alt="Celebrity Profile"
                  width={80}
                  height={80}
                  className="object-cover w-full h-full"
                />
              </div>
            </div>
          </div>

          <p className="text-center text-sm mt-2">
            {isGreeting
              ? "Preview it on your profile or share it with friends"
              : " Sit tight — we’re bringing your voice to life. This may take a few moments."}
          </p>

          {isGreeting && (
            <div className="pt-32 flex flex-col mb-20 w-full">
              <button
                href="/"
                className="w-full text-sm font-semibold grayButton text-white transition-all relative rounded-lg border border-white/5 max-w-80 h-14 flex gap-2 items-center justify-center text-center cursor-pointer hover:opacity-80"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="19"
                  height="19"
                  viewBox="0 0 19 19"
                  fill="none"
                >
                  <path
                    d="M6.70812 10.5563L11.8306 13.5413M11.8231 5.30633L6.70812 8.29133M16.0156 4.17383C16.0156 5.41647 15.0083 6.42383 13.7656 6.42383C12.523 6.42383 11.5156 5.41647 11.5156 4.17383C11.5156 2.93119 12.523 1.92383 13.7656 1.92383C15.0083 1.92383 16.0156 2.93119 16.0156 4.17383ZM7.01562 9.42383C7.01562 10.6665 6.00827 11.6738 4.76562 11.6738C3.52298 11.6738 2.51562 10.6665 2.51562 9.42383C2.51562 8.18119 3.52298 7.17383 4.76562 7.17383C6.00827 7.17383 7.01562 8.18119 7.01562 9.42383ZM16.0156 14.6738C16.0156 15.9165 15.0083 16.9238 13.7656 16.9238C12.523 16.9238 11.5156 15.9165 11.5156 14.6738C11.5156 13.4312 12.523 12.4238 13.7656 12.4238C15.0083 12.4238 16.0156 13.4312 16.0156 14.6738Z"
                    stroke="white"
                    stroke-width="1.2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Share celebrityX greetings
              </button>
              <button className="w-full text-sm font-semibold py-2 rounded-md bg-white btnGrayGradient  text-black mt-4  max-w-80 text-center  h-14  flex gap-2 items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="19"
                  height="19"
                  viewBox="0 0 19 19"
                  fill="none"
                >
                  <path
                    d="M16.5156 11.6738V14.6738C16.5156 15.0717 16.3576 15.4532 16.0763 15.7345C15.795 16.0158 15.4134 16.1738 15.0156 16.1738H4.51562C4.1178 16.1738 3.73627 16.0158 3.45496 15.7345C3.17366 15.4532 3.01563 15.0717 3.01562 14.6738V11.6738M6.01562 7.92383L9.76562 11.6738M9.76562 11.6738L13.5156 7.92383M9.76562 11.6738V2.67383"
                    stroke="black"
                    stroke-width="1.3"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Download
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShowPreview;
