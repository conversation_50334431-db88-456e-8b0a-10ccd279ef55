import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import AllCelebrities from "./celebrity/AllCelebrities";
import GreetingsList from "./greetings/GreetingsList";

const CustomerDetail = ({ celebrities, user }) => {
  const router = useRouter();
  return (
    <div>
      <div className="h-full max-w-md mx-auto">
        <div className="px-6 pt-10 pb-24 bg-white text-black">
          <div className="flex justify-between items-center gap-4 mb-4">
            <h2 className="text-base font-medium flex-1 text-center">
              Profile
            </h2>
            <Image
              src="/options-black.svg"
              width={20}
              height={20}
              alt="options-black"
            />
          </div>
          <div className="flex items-center gap-[14px]">
            <img
              src={user?.profilePhotoUrl}
              alt="Beyoncé"
              className="rounded-full object-cover border-1 border-[##999999]"
              width={64}
              height={64}
            />
            <div className="flex flex-col">
              <div className="flex flex-col gap-1">
                <span className="text-2xl font-semibold">
                  @{user?.firstName}
                </span>
                <span className="text-xs">
                  Big fan of R&B + empowerment vibes
                </span>
                <div className="flex gap-1 text-xs">
                  <img src="/Calendar-Black.svg" width={12} height={12} />
                  <span>Member since {user?.createdAt.split("-")[0]}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-3 flex gap-3">
            <button
              className="text-white cursor-pointer py-2 px-6 flex justify-center items-center gap-[10px] rounded-lg border border-black w-1/2 grayButton"
              onClick={() => {
                router.push("/edit-profile");
              }}
            >
              <img src="/user.svg" width={18} height={18} alt="user" />
              <span className="text-sm font-medium">Edit Profile</span>
            </button>
            <button className="cursor-pointer py-2 px-6 flex justify-center items-center gap-[10px] rounded-lg border border-black w-1/2 bg-white">
              <img src="/share.svg" width={18} height={18} alt="share" />
              <span className="text-black text-sm font-medium">Share</span>
            </button>
          </div>{" "}
          <div className="mt-6">
            <h2 className="text-base font-medium mb-2">Occasions</h2>
            <div className="flex gap-[10px] items-center">
              {["Birthday", "Milestone", "Love", "Empowerment"].map(
                (item, index) => (
                  <div
                    key={index}
                    className="flex justify-center items-center text-xs rounded-3xl border border-black p-2"
                  >
                    {item}
                  </div>
                )
              )}
            </div>
          </div>
          <div className="flex justify-between items-center mt-6">
            <h2 className="text-base font-medium mb-2">Favorite celebrities</h2>
            <Link href="" className="underline text-xs">
              View all
            </Link>
          </div>
          <AllCelebrities celebrities={celebrities} />
          <div className="flex justify-between items-center mt-6">
            <h2 className="text-base font-medium mb-2">Shared greetings</h2>
            <Link href="" className="underline text-xs">
              View all
            </Link>
          </div>
          <GreetingsList />
        </div>
      </div>
    </div>
  );
};

export default CustomerDetail;
