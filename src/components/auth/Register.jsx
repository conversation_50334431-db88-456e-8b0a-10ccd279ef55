"use client";
import { useFormik } from "formik";
import * as Yup from "yup";
import useApiHook from "@/hooks/useApiHook";
import useToaster from "@/hooks/useToaster";
import { Loader } from "@/components/common/Loader";
import { LoaderVariants } from "@/components/common/Loader";
import { useRouter } from "next/navigation";
import { useState } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import PageLoader from "../common/PageLoader";
import useApiErrorHandler from "@/hooks/useApiErrorHandler";

const passwordRules = /((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;

export default function Register() {
  const router = useRouter();
  const { handleApiCall, isApiLoading } = useApiHook();
  const handleApiError = useApiErrorHandler();
  const { showSuccessToast, showErrorToast } = useToaster();
  const [showRoleSelection, setShowRoleSelection] = useState(false);
  const [selectedRole, setSelectedRole] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "1234567890",
      dateOfBirth: "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required("First name is required"),
      lastName: Yup.string().required("Last name is required"),
      email: Yup.string().email("Invalid email").required("Email is required"),
      phoneNumber: Yup.string().required("Phone number is required"),
      dateOfBirth: Yup.date()
        .max(new Date(), "Date of birth cannot be in the future")
        .required("Date of birth is required"),
      password: Yup.string()
        .min(8, "Minimum 8 characters")
        .max(30, "Maximum 30 characters")
        .matches(
          passwordRules,
          "Password must contain uppercase, lowercase, and a number or special character"
        )
        .required("Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("password")], "Passwords must match")
        .required("Confirm password is required"),
    }),
    onSubmit: () => {
      setFormData(values);
      setShowRoleSelection(true);
    },
  });

  const handleRegisterClick = async (e) => {
    e.preventDefault();
    formik.setTouched({
      firstName: true,
      lastName: true,
      email: true,
      phoneNumber: true,
      dateOfBirth: true,
      password: true,
      confirmPassword: true,
    });
    const errors = await formik.validateForm();
    if (Object.keys(errors).length === 0) {
      setShowRoleSelection(true);
    }
  };

  const handleRoleSelection = async () => {
    setShowRoleSelection(false);
    try {
      const payload = {
        firstName: formik.values.firstName,
        lastName: formik.values.lastName,
        email: formik.values.email,
        phoneNumber: formik.values.phoneNumber,
        dateOfBirth: formik.values.dateOfBirth,
        password: formik.values.password,
        role: selectedRole,
      };

      const resp = await handleApiCall({
        method: "POST",
        url: "/auth/register",
        data: payload,
      });

      if (resp.status === 201) {
        showSuccessToast("Account created successfully!");
        if (resp?.data?.role === "celebrity") {
          router.push("/celebrity/home");
        } else {
          router.push("/customer/home");
        }
      }
    } catch (error) {
      handleApiError(error);
      setShowRoleSelection(false);
    }
  };

  return (
    <div className="flex flex-col justify-center text-black">
      {showRoleSelection && (
        <div className="bg-white absolute top-0 left-0 right-0 bottom-0 flex flex-col z-50 p-4 space-y-2">
          <h2 className="text-center text-lg font-bold">
            Choose an account type
          </h2>
          <p className="text-sm text-center text-black/60">
            To allow us serve you better, please choose an account that
            describes you.
          </p>
          <div className="flex flex-col gap-4">
            <div
              onClick={() => setSelectedRole("celebrity")}
              className={`${
                selectedRole === "celebrity" ? "activeBgWhite shadow-md" : ""
              } flex items-start justify-between cursor-pointer space-x-3 whiteButton border border-[#e2e1e1] py-5 px-3 rounded-lg`}
            >
              <img
                src="/images/auth/celeb.png"
                alt="Celebrity"
                className="w-7"
              />
              <div className="flex-1 flex flex-col text-black">
                <h3 className="text-sm font-bold">Celebrity</h3>
                <p className="text-sm font-medium pr-4">
                  Unlock exclusive tools to create and manage your digital
                  presence
                </p>
              </div>
              <div className="flex items-center h-full">
                <img
                  src={
                    selectedRole === "celebrity"
                      ? `/images/auth/checked.svg`
                      : "/images/auth/uncheck.svg"
                  }
                  alt="Celebrity Selection"
                  className="w-4"
                />
              </div>
            </div>

            <div
              onClick={() => setSelectedRole("customer")}
              className={`${
                selectedRole === "customer" ? "activeBgWhite shadow-md" : ""
              } flex items-start justify-between cursor-pointer space-x-3 whiteButton border border-[#e2e1e1] py-5 px-3 rounded-lg`}
            >
              <img src="/images/auth/user.png" alt="Customer" className="w-7" />
              <div className="flex-1 flex flex-col text-black">
                <h3 className="text-sm font-bold">Fan</h3>
                <p className="text-sm font-medium">
                  Get access to personalized greetings, contact celebrities to
                  create messages for you
                </p>
              </div>
              <div className="flex items-center h-full">
                <img
                  src={
                    selectedRole === "customer"
                      ? `/images/auth/checked.svg`
                      : "/images/auth/uncheck.svg"
                  }
                  alt="Customer Selection"
                  className="w-4"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-4 mt-4">
            <button
              onClick={handleRoleSelection}
              disabled={!selectedRole}
              className={`${
                !selectedRole ? "bg-gray-400 cursor-not-allowed" : "grayButton"
              } flex w-full justify-center rounded-lg text-white p-3  transition-all hover:scale-105 cursor-pointer`}
            >
              {isApiLoading ? (
                <Loader
                  variant={LoaderVariants.rotatingLines}
                  size={25}
                  color="black"
                />
              ) : (
                "Confirm"
              )}
            </button>
          </div>
        </div>
      )}

      <form onSubmit={handleRegisterClick} className="space-y-4">
        <div className="w-full grid grid-cols-2 gap-3">
          <div>
            <label
              htmlFor="firstName"
              className="block text-xs font-medium clrGray"
            >
              First name
            </label>
            <div className="mt-2">
              <input
                id="firstName"
                name="firstName"
                type="text"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.firstName}
                className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
              />
              {formik.touched.firstName && formik.errors.firstName && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.firstName}
                </p>
              )}
            </div>
          </div>

          <div>
            <label
              htmlFor="lastName"
              className="block text-xs font-medium clrGray"
            >
              Last name
            </label>
            <div className="mt-2">
              <input
                id="lastName"
                name="lastName"
                type="text"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.lastName}
                className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
              />
              {formik.touched.lastName && formik.errors.lastName && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.lastName}
                </p>
              )}
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="email" className="block text-xs font-medium clrGray">
            Email address
          </label>
          <div className="mt-2">
            <input
              id="email"
              name="email"
              type="email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
            />
            {formik.touched.email && formik.errors.email && (
              <p className="text-red-500 text-xs mt-1 font-bold">
                {formik.errors.email}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="phoneNumber"
            className="block text-xs font-medium clrGray"
          >
            Phone Number
          </label>
          <div className="mt-2 flex items-center space-x-2">
            <div className="w-full phoneDiv">
              <PhoneInput
                country={"us"}
                international
                countryCallingCodeEditable={false}
                placeholder="Select"
                value={formik.values.countryCode}
                onChange={(value) => {
                  formik.setFieldValue("countryCode", value);
                }}
                className="block w-full rounded-md  text-black text-sm sm:text-base sm:leading-6"
              />
              {formik.touched.countryCode && formik.errors.countryCode && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.countryCode}
                </p>
              )}
            </div>
            <div className="w-3/4 hidden">
              <input
                id="phoneNumber"
                name="phoneNumber"
                type="tel"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.phoneNumber}
                className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                placeholder="Enter phone number"
              />
              {formik.touched.phoneNumber && formik.errors.phoneNumber && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.phoneNumber}
                </p>
              )}
            </div>
          </div>
        </div>

        <div>
          <label
            htmlFor="dateOfBirth"
            className="block text-xs font-medium clrGray"
          >
            Date of Birth
          </label>
          <div className="mt-2">
            <input
              id="dateOfBirth"
              name="dateOfBirth"
              type="date"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.dateOfBirth}
              className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
            />
            {formik.touched.dateOfBirth && formik.errors.dateOfBirth && (
              <p className="text-red-500 text-xs mt-1 font-bold">
                {formik.errors.dateOfBirth}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="password"
            className="block text-xs font-medium clrGray"
          >
            Password
          </label>
          <div className="mt-2 relative">
            <input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.password}
              className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-4 transform cursor-pointer"
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {showPassword ? (
                <img
                  src="/images/home/<USER>"
                  alt="Logo"
                  className="w-4"
                />
              ) : (
                <img
                  src="/images/home/<USER>"
                  alt="Logo"
                  className="w-4"
                />
              )}
            </button>
            {formik.touched.password && formik.errors.password && (
              <p className="text-red-500 text-xs mt-1 font-bold">
                {formik.errors.password}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            htmlFor="confirmPassword"
            className="block text-xs font-medium clrGray"
          >
            Confirm password
          </label>
          <div className="mt-2 relative">
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.confirmPassword}
              className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-4 transform cursor-pointer"
              aria-label={
                showConfirmPassword ? "Hide password" : "Show password"
              }
            >
              {showConfirmPassword ? (
                <img
                  src="/images/home/<USER>"
                  alt="Logo"
                  className="w-4"
                />
              ) : (
                <img
                  src="/images/home/<USER>"
                  alt="Logo"
                  className="w-4"
                />
              )}
            </button>
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.confirmPassword}
                </p>
              )}
          </div>
        </div>

        <div>
          <button
            type="submit"
            className="flex w-full justify-center cursor-pointer rounded-lg text-white p-3 grayButton transition-all hover:scale-105 mt-4"
          >
            {isApiLoading ? (
              <Loader
                variant={LoaderVariants.rotatingLines}
                size={25}
                color="black"
              />
            ) : (
              "Register"
            )}
          </button>
        </div>
      </form>
      {isApiLoading && <PageLoader />}
    </div>
  );
}
