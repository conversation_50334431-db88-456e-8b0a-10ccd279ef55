"use client";

import { useFormik } from "formik";
import * as Yup from "yup";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginUser } from "@/redux/slices/auth/thunks";
import useToaster from "@/hooks/useToaster";
import { useState } from "react";
import PageLoader from "../common/PageLoader";
import useApi<PERSON>rrorHandler from "@/hooks/useApiErrorHandler";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email").required("Email is required"),
  password: Yup.string().required("Password is required"),
});
const LoginForm = ({ setIsLoginModalOpen }) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { showSuccessToast, showErrorToast } = useToaster();
  const [showPassword, setShowPassword] = useState(false);
  const handleApiError = useApiErrorHandler();

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const resp = await dispatch(
          loginUser({
            email: values.email,
            password: values.password,
          })
        );

        if (resp?.payload.status === 200) {
          const role = resp?.payload?.data?.user?.role;
          const currentPath =
            typeof window !== "undefined" ? window.location.pathname : "";
          if (currentPath.startsWith("/celebrity") && role === "customer") {
            setIsLoginModalOpen(false);
          } else {
            showSuccessToast("Login successful!");
            if (role === "celebrity") {
              router.push("/celebrity/home");
            } else if (role === "admin") {
              router.push("/admin/dashboard");
            } else {
              router.push("/");
            }
          }
        } else {
          showErrorToast(resp?.payload?.data?.message || "Login Failed!");
        }
      } catch (err) {
        handleApiError(err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <>
      <div className="flex flex-col justify-center text-black">
        <form className="space-y-4" onSubmit={formik.handleSubmit}>
          <div>
            <label
              htmlFor="email"
              className="block text-xs font-medium clrGray"
            >
              Email address
            </label>
            <div className="mt-2">
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email}
              />
              {formik.touched.email && formik.errors.email && (
                <p className="text-red-500 text-xs mt-1 font-bold">
                  {formik.errors.email}
                </p>
              )}
            </div>
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-xs font-medium clrGray"
            >
              Password
            </label>
            <div className="mt-2 relative">
              <input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                autoComplete="current-password"
                className="block w-full rounded-md border border-black/15 px-3 py-3 text-black text-sm sm:text-base sm:leading-6 pr-10" // Added pr-10 for padding-right
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.password}
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 px-3 flex items-center text-sm text-gray-400 cursor-pointer"
                onClick={() => setShowPassword((prev) => !prev)}
              >
                {showPassword ? (
                  <img
                    src="/images/home/<USER>"
                    alt="Logo"
                    className="w-4"
                  />
                ) : (
                  <img
                    src="/images/home/<USER>"
                    alt="Logo"
                    className="w-4"
                  />
                )}
              </button>
            </div>
            {formik.touched.password && formik.errors.password && (
              <p className="text-red-500 text-xs mt-1 font-bold">
                {formik.errors.password}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-700 bg-gray-800 text-white focus:ring-white"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-xs text-black/70"
              >
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link
                href="/auth/forgot-password"
                className="font-medium text-xs text-black cursor-pointer"
              >
                Forgot your password?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={formik.isSubmitting}
              className="flex w-full justify-center cursor-pointer rounded-lg text-white p-3 grayButton  transition-all hover:scale-105 mt-4 font-bold"
            >
              {formik.isSubmitting ? "Logging in..." : "Log in"}
            </button>
          </div>

          <p className="block text-xs text-black/70 w-full text-center relative">
            <span className="absolute bg-[#EDF1F3] top-2 w-full h-[1px] left-0 right-0 m-auto"></span>
            <span className="bg-white px-3 relative z-10">Or Login Width</span>
          </p>
          <div className="w-full grid grid-cols-4 gap-3">
            <span className=" h-12 border border-[#DBDBDB] hover:border-[#b1b0b0] cursor-pointer rounded-md flex justify-center align-middle">
              <img src="/images/auth/google.svg" className="w-5" />
            </span>
            <span className=" h-12 border border-[#DBDBDB] hover:border-[#b1b0b0] cursor-pointer  rounded-md flex justify-center align-middle">
              <img src="/images/auth/fb.svg" className="w-5" />
            </span>
            <span className=" h-12 border border-[#DBDBDB] hover:border-[#b1b0b0] cursor-pointer  rounded-md flex justify-center align-middle">
              <img src="/images/auth/apple.svg" className="w-5" />
            </span>
            <span className=" h-12 border border-[#DBDBDB] hover:border-[#b1b0b0] cursor-pointer  rounded-md flex justify-center align-middle">
              <img src="/images/auth/mob.svg" className="w-5" />
            </span>
          </div>
        </form>
        {formik.isSubmitting && <PageLoader />}
      </div>
    </>
  );
};

export default LoginForm;
