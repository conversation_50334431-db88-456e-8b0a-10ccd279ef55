'use client'

import { useState } from 'react'

export default function GreetingRequestForm({ celebrity, onSubmit }) {
  const [formData, setFormData] = useState({
    message: '',
    tone: 'casual',
    occasion: '',
    recipientName: '',
    specialInstructions: '',
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="recipientName" className="block text-sm font-medium text-gray-700">
          Recipient's Name
        </label>
        <input
          type="text"
          name="recipientName"
          id="recipientName"
          required
          value={formData.recipientName}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="occasion" className="block text-sm font-medium text-gray-700">
          Occasion
        </label>
        <select
          name="occasion"
          id="occasion"
          required
          value={formData.occasion}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
        >
          <option value="">Select an occasion</option>
          <option value="birthday">Birthday</option>
          <option value="anniversary">Anniversary</option>
          <option value="graduation">Graduation</option>
          <option value="wedding">Wedding</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700">
          Message
        </label>
        <textarea
          name="message"
          id="message"
          rows={4}
          required
          value={formData.message}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          placeholder="What would you like the celebrity to say?"
        />
      </div>

      <div>
        <label htmlFor="tone" className="block text-sm font-medium text-gray-700">
          Tone
        </label>
        <select
          name="tone"
          id="tone"
          required
          value={formData.tone}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
        >
          <option value="casual">Casual</option>
          <option value="formal">Formal</option>
          <option value="funny">Funny</option>
          <option value="inspirational">Inspirational</option>
        </select>
      </div>

      <div>
        <label htmlFor="specialInstructions" className="block text-sm font-medium text-gray-700">
          Special Instructions (Optional)
        </label>
        <textarea
          name="specialInstructions"
          id="specialInstructions"
          rows={2}
          value={formData.specialInstructions}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
          placeholder="Any specific requirements or preferences?"
        />
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          <p>Price: ${celebrity?.price || 99}</p>
        </div>
        <button
          type="submit"
          className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Submit Request
        </button>
      </div>
    </form>
  )
} 