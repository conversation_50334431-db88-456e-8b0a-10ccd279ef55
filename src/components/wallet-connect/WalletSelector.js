import React from 'react';
import { ChevronLeft } from 'lucide-react';
import WalletOption from './WalletOption';
import Button from '../common/Button';


const walletOptions = [
  { id: 'stripe', name: '<PERSON><PERSON>', icon: null },
  { id: 'solana', name: '<PERSON><PERSON>', icon: null },
  { id: 'ethereum', name: 'Ethereum', icon: null },
  { id: 'base', name: 'Base', icon: null },
  { id: 'polygon', name: 'Polygon', icon: null },
  { id: 'bitcoin', name: 'Bitcoin', icon: null },
];

const WalletSelector = ({
  selectedWallet,
  onSelectWallet,
  onConnectWallet,
  onBack,
}) => {
  return (
    <div className="flex flex-col h-full max-w-md mb-8 mx-auto w-full">
      <div className="flex items-center justify-center relative py-4 sm:py-5 border-b border-gray-800">
        <button 
          className="absolute left-4 text-white p-1 rounded-full hover:bg-gray-800 transition-colors"
          onClick={onBack}
        >
          <ChevronLeft size={20} />
        </button>
        <h1 className="text-white font-medium text-lg sm:text-xl bg">Connect Wallet</h1>
      </div>
      
      <div className="flex-1 p-4 sm:p-6">
        <p className="text-gray-400 text-sm sm:text-base mb-6 sm:mb-8">Get started by connecting your preferred wallet below</p>
        
        <div className="space-y-3 sm:space-y-4">
          {walletOptions.map((wallet) => (
            <WalletOption
              key={wallet.id}
              icon={wallet.icon}
              name={wallet.name}
              isSelected={selectedWallet === wallet.id}
              onClick={() => onSelectWallet(wallet.id)}
            />
          ))}
        </div>
      </div>
      
      <div className="p-4 sm:p-6 mt-auto flex justify-center">
        <Button 
          onClick={onConnectWallet}
          disabled={!selectedWallet}
        >
          Connect wallet
        </Button>
      </div>
    </div>
  );
};

export default WalletSelector;