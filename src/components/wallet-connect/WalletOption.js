import React from 'react';

const WalletOption = ({ icon: Icon, name, isSelected, onClick }) => {
  // Map wallet name to the correct image path
  const getWalletImage = (walletName) => {
    const walletNameLower = walletName.toLowerCase();
    
    const imageMap = {
      'stripe': '/images/wallet/stripe1.png',
      'solana': '/images/wallet/sol.svg',
      'ethereum': '/images/wallet/ethereum.svg',
      'base': '/images/wallet/base1.png',
      'polygon': '/images/wallet/polygon1.png',
      'bitcoin': '/images/wallet/bitcoin.svg',
    };
    
    return imageMap[walletNameLower] || '';
  };

  const imagePath = getWalletImage(name);
  
  return (
    <button
      className={`w-full p-4 sm:p-5 rounded-[4px] flex items-center gap-3 transition-all duration-300 mb-3
        ${isSelected 
          ? 'bg-white text-black border-2 border-white/80 scale-[1.02] shadow-[0px_6px_8px_0px_rgba(0,0,0,0.35)]' 
          : 'bg-[rgba(148,142,142,0.49)] text-white hover:bg-opacity-30 border border-transparent'} 
        shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)]`}
      onClick={onClick}
    >
      <div className="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7">
        {imagePath ? (
          <img src={imagePath} alt={name} className={`w-5 h-5 sm:w-6 sm:h-6 ${isSelected ? 'filter-none' : 'opacity-90'}`} />
        ) : (
          Icon && <Icon size={20} />
        )}
      </div>
      <span className={`font-medium text-sm sm:text-base ${isSelected ? 'font-semibold' : ''}`}>{name}</span>
    </button>
  );
};

export default WalletOption;