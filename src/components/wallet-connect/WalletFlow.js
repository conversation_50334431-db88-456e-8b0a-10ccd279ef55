"use client"
import React, { useState } from 'react';
import WalletSelector from './WalletSelector';
import WalletConnected from './WalletConnect';
import { useRouter } from 'next/navigation';
const WalletFlow = () => {
  const [currentStep, setCurrentStep] = useState('select');
  const [selectedWallet, setSelectedWallet] = useState(null);
  const router=useRouter()
  const handleSelectWallet = (wallet) => {
    setSelectedWallet(wallet);
  };
  
  const handleConnectWallet = () => {
    if (selectedWallet) {
      setTimeout(() => {
        setCurrentStep('connected');
      }, 500);
    }
  };
  
  const handleGoToDashboard = () => {
   router.push("/")
  };
  
  const handleBack = () => {
    router.back()
  };

  const handleClose = () => {
    setCurrentStep('select');
    setSelectedWallet(null);
  };
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-950 text-white p-4">
      <div className="  rounded-xl overflow-hidden shadow-xl">
        {currentStep === 'select' && (
          <WalletSelector
            selectedWallet={selectedWallet}
            onSelectWallet={handleSelectWallet}
            onConnectWallet={handleConnectWallet}
            onBack={handleBack}
          />
        )}
        
        {currentStep === 'connected' && selectedWallet && (
          <WalletConnected
            walletType={selectedWallet}
            onGoToDashboard={handleGoToDashboard}
            onClose={handleClose}
          />
        )}
      </div>
    </div>
  );
};

export default WalletFlow;