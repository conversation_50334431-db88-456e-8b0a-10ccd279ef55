import React from 'react';
import { Check, X } from 'lucide-react';
import Button from '../common/Button';

const WalletConnected= ({ 
  walletType, 
  onGoToDashboard,
  onClose
}) => {
  return (
    <div className="flex flex-col h-[100vh] w-full">
      <div className="flex items-center justify-center relative py-4">
        <h1 className="text-white font-medium text-xl">Claim earnings</h1>
        <button 
          className="absolute right-1 text-white p-1 rounded-full hover:bg-gray-800 transition-colors"
          onClick={onClose}
        >
          <X size={20} />
        </button>
      </div>
      
      <div className="flex-1 px-4 flex flex-col items-center justify-center">
        <h2 className="text-white text-3xl font-bold mb-2">Wallet Connected</h2>
        
        <div className="flex justify-center items-center mb-8">
          <img 
            src="/check.png" 
            alt="Success Check" 
            className="w-32 h-32 object-contain mx-auto translate-4"
          />
        </div>
        
        <p className="text-gray-300 text-sm text-center max-w-xs mb-16">
          You're all set! Payouts will be automatically sent to your connected
          {walletType === 'stripe' ? ' Stripe' : ''} account.
        </p>
      </div>
      
      <div className="px-2 py-8 mb-6">
        <Button 
          onClick={onGoToDashboard}
          className="h-[60px] text-lg font-medium"
        >
          Go to Dashboard
        </Button>
      </div>
    </div>
  );
};

export default WalletConnected;