import Link from 'next/link'

export default function CelebrityCard({ celebrity }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="aspect-w-16 aspect-h-9">
        <img
          src={celebrity.image}
          alt={celebrity.name}
          className="w-full h-48 object-cover"
        />
      </div>
      <div className="p-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{celebrity.name}</h3>
            <p className="text-sm text-gray-500">{celebrity.category}</p>
          </div>
          <div className="text-right">
            <p className="text-lg font-bold text-indigo-600">${celebrity.price}</p>
            <div className="flex items-center">
              <span className="text-yellow-400">★</span>
              <span className="ml-1 text-sm text-gray-600">{celebrity.rating}</span>
            </div>
          </div>
        </div>
        <p className="mt-2 text-sm text-gray-600">{celebrity.description}</p>
        <div className="mt-4">
          <Link
            href={`/customer/requests?celebrity=${celebrity.id}`}
            className="block w-full text-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Request Greeting
          </Link>
        </div>
      </div>
    </div>
  )
} 