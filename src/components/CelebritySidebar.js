'use client';

import Link from 'next/link';
import {
  HomeIcon,
  VideoCameraIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserCircleIcon,
  BellIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  LifebuoyIcon,
} from '@heroicons/react/24/outline';

const sidebarLinks = [
  {
    title: 'Dashboard',
    path: '/celebrity/dashboard',
    icon: <HomeIcon className='w-5 h-5' />,
  },
  {
    title: 'My Videos',
    path: '/celebrity/dashboard/videos',
    icon: <VideoCameraIcon className='w-5 h-5' />,
  },
  // {
  //   title: 'Schedule',
  //   path: '/celebrity/dashboard/schedule',
  //   icon: <CalendarIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Earnings',
  //   path: '/celebrity/dashboard/earnings',
  //   icon: <CurrencyDollarIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Profile',
  //   path: '/celebrity/dashboard/profile',
  //   icon: <UserCircleIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Notifications',
  //   path: '/celebrity/dashboard/notifications',
  //   icon: <BellIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Messages',
  //   path: '/celebrity/dashboard/messages',
  //   icon: <ChatBubbleLeftRightIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Reports',
  //   path: '/celebrity/dashboard/reports',
  //   icon: <DocumentTextIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Settings',
  //   path: '/celebrity/dashboard/settings',
  //   icon: <Cog6ToothIcon className='w-5 h-5' />,
  // },
  // {
  //   title: 'Support',
  //   path: '/celebrity/dashboard/support',
  //   icon: <LifebuoyIcon className='w-5 h-5' />,
  // },
];

export default function CelebritySidebar() {
  return (
    <aside className='relative w-64 h-screen bg-[#0c0c0c] shadow-lg z-10'>
      {/* Gradient Right Border */}
      <div className='absolute top-0 right-0 w-[2px] h-full bg-gradient-to-b from-white/0 via-white/50 to-white/0'></div>
      <nav className='mt-6 space-y-2 px-4'>
        {sidebarLinks.map(({ title, path, icon }) => (
          <Link
            key={title}
            href={path}
            className='flex items-center gap-3 py-2 px-3 rounded hover:bg-indigo-100 hover:text-black text-white'
          >
            {icon}
            <span>{title}</span>
          </Link>
        ))}
      </nav>
    </aside>
  );
} 