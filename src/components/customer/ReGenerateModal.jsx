"use client";
import React, { useState } from "react";
import { useFormik } from "formik";

const RegenerateModal = ({
  onClose,
  onConfirm,
  balance = 120,
  cost = 20,
  modalMode,
  setModalMode,
  isEditing,
  setIsEditing,
}) => {
  const formik = useFormik({
    initialValues: {
      celebrity: "French Montana",
      to: "<PERSON>",
      from: "<PERSON>",
      occasion: "Birthday",
      tone: "🎁 Personal / Warm",
      message: "Happy Birthday Jessica from your beloved best friend <PERSON>.",
    },
    onSubmit: (values) => {
      console.log("Form submitted:", values);
      setIsEditing(true);
    },
  });
  return (
    <div className="fixed inset-0 bg-black/70 z-50 flex items-center justify-center">
      <div className="bg-white rounded-xl shadow-lg p-5 w-[360px] opacity-100 m-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <h3 className="text-base font-medium w-full text-center">
            {modalMode === "details"
              ? "Message details"
              : "Regenerate greeting"}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-black text-xl"
          >
            ×
          </button>
        </div>

        {/* CONDITIONAL CONTENT */}
        {(modalMode === "regenerate" || modalMode == "regenerate-details") && (
          <>
            <p className="text-xs text-[#6C7278] text-center leading-[160%] tracking-[-0.24px] mb-6">
              Want to try a new version of this message? We'll use the same tone
              and script to give you a fresh take.
            </p>

            <div className="mb-6 flex items-center justify-between w-full">
              <p className="text-sm font-medium">Your balance</p>
              <p className="text-sm text-white bg-black px-2 py-1 rounded-xl">
                CLBX <span className="font-bold">${balance}</span>
              </p>
            </div>

            <div
              className="flex justify-between items-center mb-6 cursor-pointer"
              onClick={() => setModalMode("details")}
            >
              <span className="text-sm underline">View message details</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="21"
                viewBox="0 0 20 21"
                fill="none"
              >
                <path
                  d="M7.5 15.9238L12.5 10.9238L7.5 5.92383"
                  stroke="#1E1E1E"
                  strokeWidth="1.2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>

            {modalMode != "regenerate-details" && (
              <button
                onClick={() => setModalMode("regenerate-details")}
                className="flex w-full justify-center rounded-lg text-white p-3 grayButton cursor-pointer"
              >
                Regenerate for ${cost} CLBX
              </button>
            )}
          </>
        )}

        {(modalMode == "details" || modalMode == "regenerate-details") && (
          <form onSubmit={formik.handleSubmit} className="space-y-4">
            {/* Celebrity */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">
                Celebrity
              </label>
              <input
                type="text"
                name="celebrity"
                value={formik.values.celebrity}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] focus:outline-none border-[#727272] ${
                  isEditing ? "text-black" : "text-[#727272]"
                }`}
              />
            </div>

            {/* To */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">To</label>
              <input
                type="text"
                name="to"
                value={formik.values.to}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] focus:outline-none border-[#727272] ${
                  isEditing ? "text-black " : "text-[#727272] "
                }`}
              />
            </div>

            {/* From */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">From</label>
              <input
                type="text"
                name="from"
                value={formik.values.from}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] focus:outline-none border-[#727272] ${
                  isEditing ? "text-black " : "text-[#727272]"
                }`}
              />
            </div>

            {/* Occasion */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">
                Occasion
              </label>
              <input
                type="text"
                name="occasion"
                value={formik.values.occasion}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] focus:outline-none border-[#727272] ${
                  isEditing ? "text-black" : "text-[#727272]"
                }`}
              />
            </div>

            {/* Tone */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">Tone</label>
              <input
                type="text"
                name="tone"
                value={formik.values.tone}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] focus:outline-none border-[#727272] ${
                  isEditing ? "text-black" : "text-[#727272]"
                }`}
              />
            </div>

            {/* Message */}
            <div>
              <label className="block text-xs text-[#6C7278] mb-2">
                Message
              </label>
              <textarea
                name="message"
                value={formik.values.message}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                readOnly={!isEditing}
                rows={3}
                className={`w-full border text-sm px-3 py-4 rounded-lg bg-[#FAFAFA] resize-none focus:outline-none border-[#727272] ${
                  isEditing ? "text-black]" : "text-[#727272]"
                }`}
              />
              <p className="text-right text-[10px] text-[#6C7278] mt-1">
                400 characters
              </p>
            </div>

            {/* Button */}
            {!isEditing ? (
              <button
                type="button"
                onClick={() => setIsEditing(true)}
                className="mt-6 flex w-full justify-center rounded-lg text-white p-3 grayButton cursor-pointer"
              >
                Edit for $CLBX 20
              </button>
            ) : (
              <button
                type="submit"
                className="flex w-full justify-center rounded-lg text-white p-3 bg-black hover:bg-gray-900 cursor-pointer"
              >
                Save
              </button>
            )}
          </form>
        )}
      </div>
    </div>
  );
};

export default RegenerateModal;
