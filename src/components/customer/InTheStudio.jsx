import Image from "next/image";

export const celebrities = [
  {
    _id: 1,
    imageurl: "/celeb-1.png",
    name: "<PERSON>",
    heading: "Empowering",
    price: 200,
    verified: true,
    review: 4.9,
    subscribers: "5M",
  },
  {
    _id: 2,
    imageurl: "/celeb-2.png",
    name: "<PERSON>",
    heading: "Empowering",
    price: 230,
    verified: true,
    review: 4.5,
    subscribers: "1M",
  },
  {
    _id: 1,
    imageurl: "/celeb-1.png",
    name: "<PERSON>",
    heading: "Empowering",
    price: 200,
    verified: true,
    review: 4.9,
    subscribers: "5M",
  },
];

export default function InTheStudio() {
  return (
    <div className="flex flex-col gap-2 mb-4 w-full text-white">
      {[1, 2, 3].map((item) => (
        <div
          key={item}
          className="flex cursor-pointer items-center justify-between p-3 rounded-lg border-[1.5px] border-[#ffffff50] bg-gradient-to-b from-white/20 to-transparent bg-black"
        >
          <div className="flex items-center gap-2">
            <div className="w-10 h-10 rounded-full">
              <Image
                src="/trending-voice.png"
                width={40}
                height={40}
                alt="trending-voice"
              />
            </div>
            <div className="flex flex-col justify-center gap-0.5">
              <p className="text-sm font-medium">Beyonce Knowles</p>
              <div className="flex gap-1">
                <Image
                  src="/Calendar.svg"
                  width={12}
                  height={12}
                  alt="calender"
                />
                <p className="text-xs">Birthday</p>
              </div>
            </div>
          </div>
          <span>00:05</span>
        </div>
      ))}
    </div>
  );
}
