{"name": "dropp-celeb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "clsx": "^2.1.1", "formik": "^2.4.6", "lucide-react": "^0.509.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-loader-spinner": "^6.1.6", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "react-webcam": "^7.2.0", "redux-persist": "^6.0.0", "yup": "^1.6.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}